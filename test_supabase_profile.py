#!/usr/bin/env python3
"""
Test Supabase profile creation - najde přesnou chybu
"""

import requests
import json

# Supabase konfigurace
SUPABASE_URL = "https://veqmmmwrxeigeagqbqor.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZlcW1tbXdyeGVpZ2VhZ3FicW9yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4NTQ1MTksImV4cCI6MjA2NjQzMDUxOX0.5vbUkeSw2IbVtReuotDu2C1z5E9Kt8qZk8MOmFhwhPI"

def test_profile_creation():
    """Test vytvoření profilu v Supabase"""
    
    print("🔍 TESTOVÁNÍ SUPABASE PROFILE CREATION")
    print("=" * 50)
    
    # 1. Test připojení k Supabase
    print("\n1. Test připojení k Supabase...")
    
    headers = {
        "apikey": SUPABASE_ANON_KEY,
        "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test základního připojení
    try:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", headers=headers)
        print(f"   ✅ Připojení OK: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Chyba připojení: {e}")
        return
    
    # 2. Test čtení z profiles tabulky
    print("\n2. Test čtení z profiles tabulky...")
    
    try:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/profiles", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            profiles = response.json()
            print(f"   ✅ Profiles tabulka existuje, počet záznamů: {len(profiles)}")
            
            if profiles:
                print("   📋 Existující profily:")
                for profile in profiles:
                    print(f"      - {profile.get('email', 'N/A')} ({profile.get('username', 'N/A')})")
        else:
            print(f"   ❌ Chyba čtení profiles: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Chyba při čtení profiles: {e}")
    
    # 3. Test registrace nového uživatele
    print("\n3. Test registrace nového uživatele...")
    
    test_user = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/auth/v1/signup",
            headers=headers,
            json=test_user
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"   ✅ Registrace úspěšná")
            
            user_id = result.get('user', {}).get('id')
            if user_id:
                print(f"   👤 User ID: {user_id}")
                
                # 4. Test vytvoření profilu pro nového uživatele
                print("\n4. Test vytvoření profilu...")
                
                profile_data = {
                    "id": user_id,
                    "username": "test_user",
                    "email": "<EMAIL>",
                    "permissions": ["basic_access"],
                    "is_active": True
                }
                
                try:
                    profile_response = requests.post(
                        f"{SUPABASE_URL}/rest/v1/profiles",
                        headers=headers,
                        json=profile_data
                    )
                    
                    print(f"   Profile creation status: {profile_response.status_code}")
                    
                    if profile_response.status_code in [200, 201]:
                        print(f"   ✅ Profil vytvořen úspěšně!")
                    else:
                        print(f"   ❌ CHYBA při vytváření profilu:")
                        print(f"   📄 Response: {profile_response.text}")
                        
                        # Pokus o detailní analýzu chyby
                        try:
                            error_data = profile_response.json()
                            print(f"   🔍 Detaily chyby: {json.dumps(error_data, indent=2)}")
                        except:
                            print(f"   🔍 Raw response: {profile_response.text}")
                            
                except Exception as e:
                    print(f"   ❌ Exception při vytváření profilu: {e}")
            
        else:
            print(f"   ❌ Chyba registrace: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception při registraci: {e}")
    
    # 5. Test is_admin funkce
    print("\n5. Test is_admin funkce...")
    
    try:
        rpc_data = {"args": {}}
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/is_admin",
            headers=headers,
            json=rpc_data
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ is_admin funkce funguje: {result}")
        else:
            print(f"   ❌ Chyba is_admin funkce: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception při testování is_admin: {e}")

if __name__ == "__main__":
    test_profile_creation()
