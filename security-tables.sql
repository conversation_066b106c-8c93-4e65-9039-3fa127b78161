-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>tn<PERSON>ch tabulek pro audit logging a invite systém
-- Spusťte po quick-fix.sql

-- 1. Tabulka pro bezpečnostní logy
CREATE TABLE IF NOT EXISTS security_logs (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp timestamptz DEFAULT now() NOT NULL,
  event_type text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  ip_address text,
  user_agent text,
  user_id uuid REFERENCES auth.users(id),
  details jsonb DEFAULT '{}'::jsonb,
  session_id text,
  risk_score integer DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
  location_info jsonb,
  device_fingerprint text,
  created_at timestamptz DEFAULT now()
);

-- 2. Tabulka pro uživatelská bezpečnostní nastavení
CREATE TABLE IF NOT EXISTS user_security (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) UNIQUE NOT NULL,
  two_factor_enabled boolean DEFAULT false,
  two_factor_secret text,
  backup_codes text[],
  last_login timestamptz,
  failed_login_attempts integer DEFAULT 0,
  account_locked_until timestamptz,
  password_changed_at timestamptz DEFAULT now(),
  security_questions jsonb DEFAULT '{}'::jsonb,
  trusted_devices jsonb DEFAULT '[]'::jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 3. Tabulka pro pozvánky
CREATE TABLE IF NOT EXISTS invites (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  email text NOT NULL,
  permissions jsonb DEFAULT '[]'::jsonb,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
  token text UNIQUE NOT NULL,
  message text,
  invited_by uuid REFERENCES auth.users(id),
  invited_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  accepted_at timestamptz,
  accepted_by uuid REFERENCES auth.users(id),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 4. Tabulka pro session tracking
CREATE TABLE IF NOT EXISTS user_sessions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  session_token text UNIQUE NOT NULL,
  device_fingerprint text,
  ip_address text,
  user_agent text,
  location_info jsonb,
  created_at timestamptz DEFAULT now(),
  last_activity timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  is_active boolean DEFAULT true
);

-- 5. Indexy pro výkon
CREATE INDEX IF NOT EXISTS idx_security_logs_timestamp ON security_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_severity ON security_logs(severity);
CREATE INDEX IF NOT EXISTS idx_security_logs_risk_score ON security_logs(risk_score DESC);

CREATE INDEX IF NOT EXISTS idx_user_security_user_id ON user_security(user_id);
CREATE INDEX IF NOT EXISTS idx_user_security_two_factor ON user_security(two_factor_enabled);

CREATE INDEX IF NOT EXISTS idx_invites_email ON invites(email);
CREATE INDEX IF NOT EXISTS idx_invites_token ON invites(token);
CREATE INDEX IF NOT EXISTS idx_invites_status ON invites(status);
CREATE INDEX IF NOT EXISTS idx_invites_expires_at ON invites(expires_at);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active, last_activity);

-- 6. RLS policies pro bezpečnostní tabulky

-- Security logs - pouze admini a vlastník logu
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can view all security logs" ON security_logs
  FOR SELECT USING (is_admin());

CREATE POLICY "Users can view own security logs" ON security_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert security logs" ON security_logs
  FOR INSERT WITH CHECK (true);

-- User security - pouze vlastník a admini
ALTER TABLE user_security ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own security settings" ON user_security
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own security settings" ON user_security
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all security settings" ON user_security
  FOR SELECT USING (is_admin());

CREATE POLICY "System can insert security settings" ON user_security
  FOR INSERT WITH CHECK (auth.uid() = user_id OR is_admin());

-- Invites - pouze admini a invite_users oprávnění
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage invites" ON invites
  FOR ALL USING (is_admin());

CREATE POLICY "Invite managers can manage invites" ON invites
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND permissions @> '["invite_users"]'::jsonb
    )
  );

-- Policy pro zobrazení pozvánek bude přidána později po testování

-- User sessions - pouze vlastník a admini
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own sessions" ON user_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON user_sessions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all sessions" ON user_sessions
  FOR SELECT USING (is_admin());

CREATE POLICY "System can insert sessions" ON user_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id OR is_admin());

-- 7. Triggery pro automatické aktualizace

-- Trigger pro updated_at v user_security
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_security_updated_at 
  BEFORE UPDATE ON user_security 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invites_updated_at 
  BEFORE UPDATE ON invites 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. Funkce pro vyčištění starých záznamů
CREATE OR REPLACE FUNCTION cleanup_old_security_logs()
RETURNS void
LANGUAGE sql
SECURITY DEFINER
AS $$
  DELETE FROM security_logs 
  WHERE timestamp < now() - interval '90 days'
  AND severity NOT IN ('high', 'critical');
  
  DELETE FROM user_sessions 
  WHERE expires_at < now() 
  OR (last_activity < now() - interval '30 days' AND is_active = false);
$$;

-- 9. Vytvoření základních záznamů (bude přidáno později po testování)

-- 10. Ověření vytvoření tabulek
SELECT 
  'Security tables created successfully' as status,
  (SELECT count(*) FROM security_logs) as security_logs_count,
  (SELECT count(*) FROM user_security) as user_security_count,
  (SELECT count(*) FROM invites) as invites_count,
  (SELECT count(*) FROM user_sessions) as sessions_count;
