import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { 
  Users, 
  UserPlus, 
  Settings, 
  Shield, 
  Mail,
  Key,
  Copy,
  Check,
  Eye,
  EyeOff,
  Trash2,
  Edit
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Tables } from '@/lib/supabase';

interface User {
  id: number;
  username: string;
  email?: string;
  permissions: string[];
  createdAt: string;
  lastLogin?: string;
  isActive: boolean;
}

interface Invite {
  inviteToken: string;
  inviteUrl: string;
  expiresAt: string;
  permissions: string[];
}

const API_BASE = 'http://localhost:3000/api';

const AdminPanel = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    email: '',
    permissions: [] as string[]
  });
  const [newInvite, setNewInvite] = useState({
    permissions: [] as string[],
    expiresInHours: 24
  });
  const [generatedInvite, setGeneratedInvite] = useState<Invite | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedInvite, setCopiedInvite] = useState(false);

  const availablePermissions = [
    { id: 'gate', label: 'Ovládání brány', description: 'Může ovládat posuvnou bránu' },
    { id: 'camera', label: 'Kamera', description: 'Může zobrazovat kamerový přenos' },
    { id: 'garage', label: 'Garáž', description: 'Může ovládat garáž' },
    { id: 'admin', label: 'Administrátor', description: 'Může spravovat uživatele a systém' }
  ];

  useEffect(() => {
    if (user?.permissions?.includes('admin')) {
      fetchUsers();
    }
  }, [user]);

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('iot-token');
      const response = await fetch(`${API_BASE}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        console.error('Chyba při načítání uživatelů');
      }
    } catch (error) {
      console.error('Chyba při načítání uživatelů:', error);
    } finally {
      setLoading(false);
    }
  };

  const createUser = async () => {
    try {
      const token = localStorage.getItem('iot-token');
      const response = await fetch(`${API_BASE}/admin/users`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newUser)
      });

      if (response.ok) {
        setShowCreateForm(false);
        setNewUser({ username: '', password: '', email: '', permissions: [] });
        fetchUsers();
        alert('Uživatel byl úspěšně vytvořen');
      } else {
        const data = await response.json();
        alert(data.error || 'Chyba při vytváření uživatele');
      }
    } catch (error) {
      console.error('Chyba při vytváření uživatele:', error);
      alert('Chyba při vytváření uživatele');
    }
  };

  const generateInvite = async () => {
    try {
      const token = localStorage.getItem('iot-token');
      const response = await fetch(`${API_BASE}/admin/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newInvite)
      });

      if (response.ok) {
        const data = await response.json();
        setGeneratedInvite(data);
        setShowInviteForm(false);
        setNewInvite({ permissions: [], expiresInHours: 24 });
      } else {
        const data = await response.json();
        alert(data.error || 'Chyba při vytváření pozvánky');
      }
    } catch (error) {
      console.error('Chyba při vytváření pozvánky:', error);
      alert('Chyba při vytváření pozvánky');
    }
  };

  const toggleUserPermission = (permission: string, isInvite: boolean = false) => {
    if (isInvite) {
      setNewInvite(prev => ({
        ...prev,
        permissions: prev.permissions.includes(permission)
          ? prev.permissions.filter(p => p !== permission)
          : [...prev.permissions, permission]
      }));
    } else {
      setNewUser(prev => ({
        ...prev,
        permissions: prev.permissions.includes(permission)
          ? prev.permissions.filter(p => p !== permission)
          : [...prev.permissions, permission]
      }));
    }
  };

  const copyInviteUrl = async () => {
    if (generatedInvite) {
      try {
        await navigator.clipboard.writeText(generatedInvite.inviteUrl);
        setCopiedInvite(true);
        setTimeout(() => setCopiedInvite(false), 2000);
      } catch (error) {
        console.error('Chyba při kopírování:', error);
      }
    }
  };

  const getPermissionBadgeColor = (permission: string) => {
    switch (permission) {
      case 'admin': return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'gate': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'camera': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'garage': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  if (!user?.permissions?.includes('admin')) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="backdrop-blur-lg bg-white/10 border-red-500/20">
          <CardContent className="p-8 text-center">
            <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-2">Přístup zamítnut</h2>
            <p className="text-red-300">Nemáte oprávnění k přístupu do admin panelu.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Settings className="w-6 h-6" />
            <span>Administrace uživatelů</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 border border-blue-500/30"
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Vytvořit uživatele
            </Button>
            <Button
              onClick={() => setShowInviteForm(true)}
              className="bg-green-500/20 hover:bg-green-500/30 text-green-300 border border-green-500/30"
            >
              <Mail className="w-4 h-4 mr-2" />
              Pozvat uživatele
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Formulář pro vytvoření uživatele */}
      {showCreateForm && (
        <Card className="backdrop-blur-lg bg-white/10 border-blue-500/20">
          <CardHeader>
            <CardTitle className="text-white">Vytvořit nového uživatele</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-purple-200">Uživatelské jméno</Label>
                <Input
                  value={newUser.username}
                  onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                  className="bg-white/10 border-purple-500/30 text-white"
                  placeholder="admin, uzivatel..."
                />
              </div>
              <div>
                <Label className="text-purple-200">Email (volitelný)</Label>
                <Input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  className="bg-white/10 border-purple-500/30 text-white"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div>
              <Label className="text-purple-200">Heslo</Label>
              <div className="relative">
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={newUser.password}
                  onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                  className="bg-white/10 border-purple-500/30 text-white pr-10"
                  placeholder="Minimálně 6 znaků"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-300 hover:text-white"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <Label className="text-purple-200 mb-3 block">Oprávnění</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availablePermissions.map(permission => (
                  <div
                    key={permission.id}
                    onClick={() => toggleUserPermission(permission.id)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      newUser.permissions.includes(permission.id)
                        ? 'bg-purple-500/20 border-purple-500/50'
                        : 'bg-white/5 border-white/20 hover:border-purple-500/30'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-4 h-4 rounded border-2 ${
                        newUser.permissions.includes(permission.id)
                          ? 'bg-purple-500 border-purple-500'
                          : 'border-gray-400'
                      }`}>
                        {newUser.permissions.includes(permission.id) && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                      <div>
                        <div className="text-white font-medium">{permission.label}</div>
                        <div className="text-xs text-purple-300">{permission.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button onClick={createUser} className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300">
                Vytvořit uživatele
              </Button>
              <Button 
                onClick={() => setShowCreateForm(false)}
                variant="ghost"
                className="text-gray-300 hover:text-white"
              >
                Zrušit
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Formulář pro vytvoření pozvánky */}
      {showInviteForm && (
        <Card className="backdrop-blur-lg bg-white/10 border-green-500/20">
          <CardHeader>
            <CardTitle className="text-white">Pozvat nového uživatele</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-purple-200">Platnost pozvánky (hodiny)</Label>
              <Input
                type="number"
                value={newInvite.expiresInHours}
                onChange={(e) => setNewInvite(prev => ({ ...prev, expiresInHours: parseInt(e.target.value) }))}
                className="bg-white/10 border-purple-500/30 text-white"
                min="1"
                max="168"
              />
            </div>

            <div>
              <Label className="text-purple-200 mb-3 block">Oprávnění pro nového uživatele</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availablePermissions.map(permission => (
                  <div
                    key={permission.id}
                    onClick={() => toggleUserPermission(permission.id, true)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      newInvite.permissions.includes(permission.id)
                        ? 'bg-green-500/20 border-green-500/50'
                        : 'bg-white/5 border-white/20 hover:border-green-500/30'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-4 h-4 rounded border-2 ${
                        newInvite.permissions.includes(permission.id)
                          ? 'bg-green-500 border-green-500'
                          : 'border-gray-400'
                      }`}>
                        {newInvite.permissions.includes(permission.id) && (
                          <Check className="w-3 h-3 text-white" />
                        )}
                      </div>
                      <div>
                        <div className="text-white font-medium">{permission.label}</div>
                        <div className="text-xs text-purple-300">{permission.description}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button onClick={generateInvite} className="bg-green-500/20 hover:bg-green-500/30 text-green-300">
                <Key className="w-4 h-4 mr-2" />
                Vytvořit pozvánku
              </Button>
              <Button 
                onClick={() => setShowInviteForm(false)}
                variant="ghost"
                className="text-gray-300 hover:text-white"
              >
                Zrušit
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Vygenerovaná pozvánka */}
      {generatedInvite && (
        <Card className="backdrop-blur-lg bg-white/10 border-green-500/20">
          <CardHeader>
            <CardTitle className="text-white">Pozvánka byla vytvořena!</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-purple-200">Pozvánkový odkaz</Label>
              <div className="flex space-x-2">
                <Input
                  value={generatedInvite.inviteUrl}
                  readOnly
                  className="bg-white/10 border-purple-500/30 text-white font-mono text-sm"
                />
                <Button
                  onClick={copyInviteUrl}
                  className="bg-green-500/20 hover:bg-green-500/30 text-green-300"
                >
                  {copiedInvite ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </Button>
              </div>
            </div>
            <div className="text-sm text-purple-300">
              <p>Pozvánka vyprší: {new Date(generatedInvite.expiresAt).toLocaleString('cs-CZ')}</p>
              <p>Oprávnění: {generatedInvite.permissions.join(', ')}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Seznam uživatelů */}
      <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Registrovaní uživatelé ({users.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8 text-purple-300">Načítám uživatele...</div>
          ) : users.length === 0 ? (
            <div className="text-center py-8 text-purple-300">Žádní uživatelé</div>
          ) : (
            <div className="space-y-3">
              {users.map(user => (
                <div
                  key={user.id}
                  className={`p-4 rounded-lg border ${
                    user.isActive 
                      ? 'bg-white/5 border-white/20' 
                      : 'bg-red-500/10 border-red-500/30'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-white font-medium">{user.username}</h3>
                        {!user.isActive && (
                          <Badge className="bg-red-500/20 text-red-300 border-red-500/30">
                            Neaktivní
                          </Badge>
                        )}
                      </div>
                      
                      {user.email && (
                        <p className="text-purple-300 text-sm mt-1">{user.email}</p>
                      )}
                      
                      <div className="flex flex-wrap gap-1 mt-2">
                        {user.permissions.map(permission => (
                          <Badge
                            key={permission}
                            className={`text-xs ${getPermissionBadgeColor(permission)}`}
                          >
                            {availablePermissions.find(p => p.id === permission)?.label || permission}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="text-xs text-purple-400 mt-2">
                        Vytvořen: {new Date(user.createdAt).toLocaleString('cs-CZ')}
                        {user.lastLogin && (
                          <span className="ml-4">
                            Poslední přihlášení: {new Date(user.lastLogin).toLocaleString('cs-CZ')}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-blue-300 hover:text-blue-200"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      {user.id !== user?.id && ( // Nemůže smazat sám sebe
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-red-300 hover:text-red-200"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPanel;
