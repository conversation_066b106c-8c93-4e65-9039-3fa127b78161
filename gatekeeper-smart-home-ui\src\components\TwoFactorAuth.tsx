import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Smartphone, 
  Key, 
  QrCode, 
  CheckCircle, 
  XCircle,
  Copy,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';

interface TwoFactorSetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

const TwoFactorAuth: React.FC = () => {
  const { user, profile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [setupData, setSetupData] = useState<TwoFactorSetup | null>(null);
  const [isEnabled, setIsEnabled] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);

  useEffect(() => {
    checkTwoFactorStatus();
  }, [user]);

  const checkTwoFactorStatus = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_security')
        .select('two_factor_enabled, backup_codes_used')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Chyba při kontrole 2FA:', error);
        return;
      }

      setIsEnabled(data?.two_factor_enabled || false);
    } catch (error) {
      console.error('Chyba při kontrole 2FA:', error);
    }
  };

  const generateTwoFactorSetup = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Volání Supabase Edge Function pro generování 2FA
      const { data, error } = await supabase.functions.invoke('generate-2fa', {
        body: { 
          userId: user.id,
          email: user.email,
          appName: 'IoT Smart Control'
        }
      });

      if (error) throw error;

      setSetupData(data);
      toast({
        title: "2FA setup",
        description: "Naskenujte QR kód pomocí autentifikační aplikace"
      });

    } catch (error) {
      console.error('Chyba při generování 2FA:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se vygenerovat 2FA setup",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const verifyAndEnable2FA = async () => {
    if (!user || !setupData || !verificationCode) return;

    setLoading(true);
    try {
      // Ověření kódu pomocí Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('verify-2fa', {
        body: { 
          userId: user.id,
          token: verificationCode,
          secret: setupData.secret
        }
      });

      if (error) throw error;

      if (data.valid) {
        // Uložení 2FA nastavení do databáze
        const { error: dbError } = await supabase
          .from('user_security')
          .upsert({
            user_id: user.id,
            two_factor_enabled: true,
            two_factor_secret: setupData.secret,
            backup_codes: setupData.backupCodes,
            backup_codes_used: [],
            created_at: new Date().toISOString()
          });

        if (dbError) throw dbError;

        setIsEnabled(true);
        setShowBackupCodes(true);
        setSetupData(null);
        setVerificationCode('');

        toast({
          title: "2FA aktivováno",
          description: "Dvoufaktorové ověření bylo úspěšně aktivováno"
        });

        // Logování bezpečnostní události
        await supabase.from('security_logs').insert({
          event: '2FA_ENABLED',
          user_id: user.id,
          details: { timestamp: new Date().toISOString() }
        });

      } else {
        toast({
          title: "Neplatný kód",
          description: "Zadaný ověřovací kód není platný",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Chyba při ověřování 2FA:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se ověřit 2FA kód",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const disable2FA = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { error } = await supabase
        .from('user_security')
        .update({
          two_factor_enabled: false,
          two_factor_secret: null,
          backup_codes: null,
          backup_codes_used: null
        })
        .eq('user_id', user.id);

      if (error) throw error;

      setIsEnabled(false);
      setShowBackupCodes(false);

      toast({
        title: "2FA deaktivováno",
        description: "Dvoufaktorové ověření bylo deaktivováno"
      });

      // Logování bezpečnostní události
      await supabase.from('security_logs').insert({
        event: '2FA_DISABLED',
        user_id: user.id,
        details: { timestamp: new Date().toISOString() }
      });

    } catch (error) {
      console.error('Chyba při deaktivaci 2FA:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se deaktivovat 2FA",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Zkopírováno",
      description: "Text byl zkopírován do schránky"
    });
  };

  const generateNewBackupCodes = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-backup-codes', {
        body: { userId: user.id }
      });

      if (error) throw error;

      const { error: updateError } = await supabase
        .from('user_security')
        .update({
          backup_codes: data.backupCodes,
          backup_codes_used: []
        })
        .eq('user_id', user.id);

      if (updateError) throw updateError;

      setShowBackupCodes(true);

      toast({
        title: "Nové záložní kódy",
        description: "Byly vygenerovány nové záložní kódy"
      });

    } catch (error) {
      console.error('Chyba při generování záložních kódů:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se vygenerovat nové záložní kódy",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Pro nastavení 2FA se musíte přihlásit.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status 2FA */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Dvoufaktorové ověření (2FA)
            {isEnabled ? (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                Aktivní
              </Badge>
            ) : (
              <Badge variant="secondary" className="ml-2">
                <XCircle className="h-3 w-3 mr-1" />
                Neaktivní
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Dvoufaktorové ověření přidává další vrstvu zabezpečení k vašemu účtu.
            Kromě hesla budete potřebovat kód z autentifikační aplikace.
          </p>

          {!isEnabled && !setupData && (
            <Button onClick={generateTwoFactorSetup} disabled={loading}>
              <Smartphone className="h-4 w-4 mr-2" />
              Aktivovat 2FA
            </Button>
          )}

          {isEnabled && (
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Dvoufaktorové ověření je aktivní a chrání váš účet.
                </AlertDescription>
              </Alert>
              
              <div className="flex gap-2">
                <Button variant="outline" onClick={generateNewBackupCodes} disabled={loading}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Nové záložní kódy
                </Button>
                <Button variant="destructive" onClick={disable2FA} disabled={loading}>
                  <XCircle className="h-4 w-4 mr-2" />
                  Deaktivovat 2FA
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Setup 2FA */}
      {setupData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <QrCode className="h-5 w-5" />
              Nastavení 2FA
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">1. Naskenujte QR kód</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Použijte autentifikační aplikaci (Google Authenticator, Authy, atd.) k naskenování QR kódu:
              </p>
              
              {/* QR kód by se zde zobrazil - pro demonstraci použijeme placeholder */}
              <div className="bg-gray-100 p-4 rounded-lg text-center">
                <QrCode className="h-32 w-32 mx-auto text-gray-400" />
                <p className="text-sm text-gray-500 mt-2">QR kód pro {setupData.secret}</p>
              </div>

              <div className="mt-4">
                <Label>Nebo zadejte manuálně:</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input value={setupData.secret} readOnly className="font-mono text-sm" />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => copyToClipboard(setupData.secret)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">2. Zadejte ověřovací kód</h4>
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Zadejte 6-místný kód"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  maxLength={6}
                  className="font-mono"
                />
                <Button 
                  onClick={verifyAndEnable2FA} 
                  disabled={loading || verificationCode.length !== 6}
                >
                  <Key className="h-4 w-4 mr-2" />
                  Ověřit
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Záložní kódy */}
      {showBackupCodes && setupData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Záložní kódy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4">
              <Shield className="h-4 w-4" />
              <AlertDescription>
                Uložte si tyto záložní kódy na bezpečné místo. Můžete je použít pro přihlášení, 
                pokud ztratíte přístup k autentifikační aplikaci.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-2 font-mono text-sm">
              {setupData.backupCodes.map((code, index) => (
                <div key={index} className="bg-gray-100 p-2 rounded flex justify-between items-center">
                  <span>{code}</span>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => copyToClipboard(code)}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>

            <Button 
              className="mt-4" 
              onClick={() => copyToClipboard(setupData.backupCodes.join('\n'))}
            >
              <Copy className="h-4 w-4 mr-2" />
              Zkopírovat všechny kódy
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TwoFactorAuth;
