import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Settings, LogOut, Menu, User, Shield, Wifi, WifiOff } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

interface NavigationMenuProps {
  user: any;
  onLogout: () => void;
  connected: boolean;
}

const NavigationMenu = ({ user, onLogout, connected }: NavigationMenuProps) => {
  const [open, setOpen] = useState(false);
  const location = useLocation();

  const menuItems = [
    {
      label: 'Dashboard',
      path: '/',
      icon: Shield,
      show: true,
    },
    {
      label: 'Admin',
      path: '/admin',
      icon: Settings,
      show: user?.permissions?.includes('admin'),
    },
  ];

  // DEBUG: zobrazit profil v menu
  useEffect(() => {
    console.log('[NavigationMenu] user:', user);
  }, [user]);

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex fixed top-0 left-0 right-0 z-40 backdrop-blur-lg bg-white/5 border-b border-purple-500/20">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-2">
              <Shield className="w-8 h-8 text-purple-400" />
              <span className="text-xl font-bold text-white">IoT Control</span>
            </div>
            
            <div className="flex space-x-1">
              {menuItems.filter(item => item.show).map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    location.pathname === item.path
                      ? 'bg-purple-500/20 text-purple-300'
                      : 'text-purple-200 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <item.icon className="w-4 h-4 inline mr-2" />
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-purple-200">
              <User className="w-4 h-4" />
              <span className="text-sm">{user?.username}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onLogout}
              className="text-purple-200 hover:text-white hover:bg-red-500/20"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Odhlásit
            </Button>
          </div>
        </div>
      </nav>      {/* Mobile Navigation */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 h-20 backdrop-blur-lg bg-purple-900/80 border-b border-purple-500/20">
        <div className="flex items-center justify-between px-4 py-3 h-full">
          <div className="flex items-center space-x-2">
            <Shield className="w-6 h-6 text-purple-400" />
            <span className="text-lg font-bold text-white">IoT Control</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Status indikátory */}
            <div className="flex items-center space-x-2">
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full backdrop-blur-sm ${
                connected ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
              }`}>
                {connected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                <span className="text-xs font-medium">
                  {connected ? 'Připojeno' : 'Odpojeno'}
                </span>
              </div>              <div className="flex items-center space-x-1 px-2 py-1 rounded-full backdrop-blur-sm bg-green-500/20 text-green-300">
                <Shield className="w-3 h-3" />
                <span className="text-xs font-medium">Zabezpečeno</span>
              </div>
            </div>
            
            <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="text-purple-200">
                <Menu className="w-5 h-5" />
              </Button>
            </SheetTrigger>
            <SheetContent className="backdrop-blur-lg bg-slate-900/95 border-purple-500/20">
              <div className="flex flex-col space-y-4 mt-8">
                {menuItems.filter(item => item.show).map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    onClick={() => setOpen(false)}
                    className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      location.pathname === item.path
                        ? 'bg-purple-500/20 text-purple-300'
                        : 'text-purple-200 hover:text-white hover:bg-white/10'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                ))}
                
                <div className="border-t border-purple-500/20 pt-4 mt-8">
                  <div className="flex items-center space-x-3 px-4 py-2 text-purple-200">
                    <User className="w-5 h-5" />
                    <span>{user?.username}</span>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      onLogout();
                      setOpen(false);
                    }}
                    className="w-full justify-start text-purple-200 hover:text-white hover:bg-red-500/20 mt-2"
                  >
                    <LogOut className="w-5 h-5 mr-3" />
                    Odhlásit se
                  </Button>
                </div>
              </div>            </SheetContent>
          </Sheet>
          </div>
        </div>
      </div>
    </>
  );
};

export default NavigationMenu;
