import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  UserPlus, 
  Shield, 
  Eye, 
  EyeOff, 
  Check, 
  AlertTriangle,
  Mail
} from 'lucide-react';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';

const SupabaseRegisterPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { signUp } = useAuth();
  const { toast } = useToast();
  const inviteToken = searchParams.get('invite');
  
  const [inviteData, setInviteData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    email: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  const availablePermissions = [
    { id: 'user', label: 'Uživatel', description: 'Základní přístup k systému' },
    { id: 'gate', label: 'Ovládání brány', description: 'Může ovládat posuvnou bránu' },
    { id: 'camera', label: 'Kamera', description: 'Může zobrazovat kamerový přenos' },
    { id: 'garage', label: 'Garáž', description: 'Může ovládat garáž' },
    { id: 'admin', label: 'Administrátor', description: 'Může spravovat uživatele a systém' }
  ];

  useEffect(() => {
    if (!inviteToken) {
      navigate('/');
      return;
    }
    
    validateInvite();
  }, [inviteToken]);

  const validateInvite = async () => {
    try {
      const { data: invite, error } = await supabase
        .from('invites')
        .select('*')
        .eq('token', inviteToken)
        .is('used_by', null)
        .gt('expires_at', new Date().toISOString())
        .single();

      if (error || !invite) {
        toast({
          title: "Neplatná pozvánka",
          description: "Pozvánka je neplatná, použitá nebo vypršela.",
          variant: "destructive",
        });
        navigate('/');
        return;
      }

      setInviteData(invite);
    } catch (error) {
      console.error('Chyba při validaci pozvánky:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se ověřit pozvánku.",
        variant: "destructive",
      });
      navigate('/');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Uživatelské jméno je povinné';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Uživatelské jméno musí mít alespoň 3 znaky';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email je povinný';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Neplatný formát emailu';
    }

    if (!formData.password) {
      newErrors.password = 'Heslo je povinné';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Heslo musí mít alespoň 6 znaků';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Hesla se neshodují';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      await signUp(formData.email, formData.password, formData.username, inviteToken!);
      
      toast({
        title: "Registrace úspěšná",
        description: "Váš účet byl vytvořen. Můžete se nyní přihlásit.",
      });
      
      navigate('/');
    } catch (error) {
      console.error('Chyba při registraci:', error);
      toast({
        title: "Chyba registrace",
        description: error instanceof Error ? error.message : "Nepodařilo se vytvořit účet",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatExpirationDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('cs-CZ');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    );
  }

  if (!inviteData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
      
      <Card className="w-full max-w-md backdrop-blur-lg bg-white/10 border-purple-500/20 shadow-2xl">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
            <UserPlus className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-white">
            Dokončení registrace
          </CardTitle>
          <p className="text-purple-200">Vytvořte si účet pomocí pozvánky</p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Informace o pozvánce */}
          <Card className="bg-white/5 border-purple-500/20">
            <CardContent className="p-4 space-y-3">
              <div className="flex items-center gap-2 text-purple-200">
                <Shield className="w-4 h-4" />
                <span className="text-sm font-medium">Oprávnění</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {inviteData.permissions.map((permission: string) => {
                  const permData = availablePermissions.find(p => p.id === permission);
                  return (
                    <Badge 
                      key={permission} 
                      variant={permission === 'admin' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {permData?.label || permission}
                    </Badge>
                  );
                })}
              </div>
              <p className="text-xs text-purple-300">
                Platnost do: {formatExpirationDate(inviteData.expires_at)}
              </p>
            </CardContent>
          </Card>

          {/* Registrační formulář */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-purple-200">
                Uživatelské jméno
              </Label>
              <Input
                id="username"
                type="text"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300"
                placeholder="Vaše uživatelské jméno"
                required
              />
              {errors.username && (
                <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.username}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-purple-200">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="pl-10 bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.email}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password" className="text-purple-200">
                Heslo
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="pr-10 bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300"
                  placeholder="Minimálně 6 znaků"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-purple-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-purple-400" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.password}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-purple-200">
                Potvrzení hesla
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className="pr-10 bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300"
                  placeholder="Zadejte heslo znovu"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-purple-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-purple-400" />
                  )}
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-400 flex items-center gap-1">
                  <AlertTriangle className="w-4 h-4" />
                  {errors.confirmPassword}
                </p>
              )}
            </div>
            
            <Button
              type="submit"
              disabled={submitting}
              className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-semibold py-3 rounded-lg transition-all duration-200 transform hover:scale-105"
            >
              {submitting ? 'Vytváří se účet...' : 'Dokončit registraci'}
            </Button>
          </form>
          
          <div className="text-center text-sm text-purple-300">
            <p>Již máte účet? <a href="/" className="text-purple-400 hover:underline">Přihlásit se</a></p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SupabaseRegisterPage;
