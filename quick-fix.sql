-- KRITICKÁ OPRAVA: Řešení nekonečné rekurze v RLS policies
-- Spusťte tento skript OKAMŽITĚ v Supabase SQL editoru

-- 1. Vytvoření SECURITY DEFINER funkce pro kontrolu admin oprávnění
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM profiles 
    WHERE id = auth.uid() 
    AND (
      permissions @> '["admin"]'::jsonb 
      OR email = '<EMAIL>'
    )
  );
$$;

-- 2. Odstraněn<PERSON> problematických RLS policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;

-- 3. Vyt<PERSON>ření nových bezpečných RLS policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all profiles" ON profiles
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can insert profiles" ON profiles
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admins can delete profiles" ON profiles
  FOR DELETE USING (is_admin());

-- 4. Nastavení admin oprávnění pro váš účet
UPDATE profiles 
SET permissions = '["admin", "user", "gate_control", "camera_view", "device_control", "user_management", "invite_users", "security_logs", "system_settings", "reports_view", "reports_export", "api_access", "api_admin"]'::jsonb
WHERE email = '<EMAIL>';

-- 5. Pokud profil neexistuje, vytvoříme ho
INSERT INTO profiles (id, email, username, permissions)
SELECT 
  auth.uid(),
  '<EMAIL>',
  'Admin',
  '["admin", "user", "gate_control", "camera_view", "device_control", "user_management", "invite_users", "security_logs", "system_settings", "reports_view", "reports_export", "api_access", "api_admin"]'::jsonb
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
) AND auth.uid() IS NOT NULL;

-- 6. Ověření opravy
SELECT 
  'RLS policies fixed' as status,
  email,
  username,
  permissions
FROM profiles 
WHERE email = '<EMAIL>';

-- 7. Test admin funkce
SELECT 
  'Admin function test' as test,
  is_admin() as is_admin_result;
