import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Home, Play, Square, Lock, StopCircle } from 'lucide-react';

interface GateControlProps {
  status: 'open' | 'closed' | 'moving';
  statusMessage?: string;  // Přidáme prop pro skutečnou MQTT zprávu
  onCommand: (command: string) => void;
  hasPermission?: boolean;
}

const GateControl = ({ status, statusMessage, onCommand, hasPermission = false }: GateControlProps) => {
  const [autoCloseTimer, setAutoCloseTimer] = useState(0);  const [buttonPressed, setButtonPressed] = useState<string | null>(null);
  const [commandSent, setCommandSent] = useState<string | null>(null);  const [movementStartTime, setMovementStartTime] = useState<number | null>(null);
  const [remainingTime, setRemainingTime] = useState(0);
  const [isOpening, setIsOpening] = useState(true);

  // Audio a vibrace feedback
  const playClickSound = () => {
    try {
      // Jednoduchý beep zvuk
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.log('Audio nedostupné:', error);
    }
  };

  const triggerVibration = () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(100); // 100ms vibrace
    }
  };
  const handleButtonPress = (buttonType: string, command: string) => {
    // Vizuální feedback
    setButtonPressed(buttonType);
    setCommandSent(command);
      // Pokud je brána v pohybu a mění se směr, přepočítáme
    if (status === 'moving' && movementStartTime) {
      // Pokud se mění směr, obrátíme logiku a resetujeme čas
      if ((command === '0' && isOpening) || (command === '1' && !isOpening)) {
        // Resetujeme čas ale zachováme aktuální progress jako výchozí bod
        setMovementStartTime(Date.now());
        setRemainingTime(30); // Resetujeme na plný čas
        setIsOpening(command === '1');
      }
    }
    
    // Audio a vibrace
    playClickSound();
    triggerVibration();
    
    // Zavolej původní command
    onCommand(command);
    
    // Zruš vizuální feedback po 2 sekundách
    setTimeout(() => {
      setButtonPressed(null);
      setCommandSent(null);
    }, 2000);
  };useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (status === 'moving') {      // Když začne pohyb, zaznamename čas
      if (!movementStartTime) {
        setMovementStartTime(Date.now());
        setRemainingTime(30); // 30 sekund na plný pohyb
        
        // Určíme směr podle MQTT zprávy
        if (statusMessage?.includes('Otevírá') || statusMessage?.includes('Otevírám')) {
          setIsOpening(true);
        } else if (statusMessage?.includes('Zavírá') || statusMessage?.includes('Zavírám')) {
          setIsOpening(false);
        }
      }
      
      // Každou sekundu aktualizujeme zbývající čas a progress
      interval = setInterval(() => {
        if (movementStartTime) {
          const elapsed = Math.floor((Date.now() - movementStartTime) / 1000);
          const maxTime = 30; // sekund na plný pohyb
          const timeLeft = Math.max(maxTime - elapsed, 0);
            setRemainingTime(timeLeft);
          
          // Pokud čas vypršel, zastavíme
          if (timeLeft <= 0) {
            clearInterval(interval);
          }
        }
      }, 1000);    } else {
      // Pohyb skončil - vyčistime časovače
      setMovementStartTime(null);
      setRemainingTime(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [status, statusMessage, movementStartTime, isOpening]);

  // Dodatečná kontrola - vyčistime pohybové stavy když přijde finální zpráva
  useEffect(() => {
    if (statusMessage?.includes('Brána zavřena') || statusMessage?.includes('Brána otevřena')) {
      setMovementStartTime(null);
      setRemainingTime(0);
    }
  }, [statusMessage]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    
    if (status === 'open') {
      setAutoCloseTimer(260); // 4:20 minut = 260 sekund
      timer = setInterval(() => {        setAutoCloseTimer(prev => {
          if (prev <= 1) {
            onCommand('0'); // Auto zavření
            // Toast odstraněn - zbytečný
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      setAutoCloseTimer(0);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [status, onCommand]);  const handleMainCommand = () => {
    if (!hasPermission) {
      // Toast odstraněn - není potřeba, uživatel uvidí že tlačítko nefunguje
      return;
    }

    let command = '0';
    let action = 'Zavření';

    if (status === 'closed') {
      command = '1';
      action = 'Otevření';
    } else if (status === 'open') {
      command = '0';
      action = 'Zavření';
    } else if (status === 'moving') {
      command = '0';
      action = 'Zastavení a zavření';
    }    handleButtonPress('main', command);
    // Toast odstraněn - zbytečný, uživatel má už vizuální feedback
  };  const handleStopCommand = () => {
    if (!hasPermission) {
      // Toast odstraněn - není potřeba, uživatel uvidí že tlačítko nefunguje
      return;
    }handleButtonPress('stop', '6');
    // Toast odstraněn - zbytečný, uživatel má už vizuální feedback
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (status) {
      case 'open': return 'text-green-400';
      case 'moving': return 'text-orange-400';
      default: return 'text-red-400';
    }
  };  const getStatusText = () => {
    // Zobrazujeme skutečnou MQTT zprávu, pokud je k dispozici
    if (statusMessage) {
      return statusMessage;
    }
    
    // Fallback pro případ, že zpráva není k dispozici
    switch (status) {
      case 'open': return 'Otevřeno';
      case 'moving': return 'Pohyb';
      default: return 'Zavřeno';
    }
  };

  const getMainButtonText = () => {
    switch (status) {
      case 'closed': return 'Otevřít bránu';
      case 'open': return 'Zavřít bránu';
      case 'moving': return 'Zastavit a zavřít';
      default: return 'Ovládání brány';
    }
  };

  const getMainButtonIcon = () => {
    switch (status) {
      case 'closed': return <Play className="w-6 h-6 mr-2" />;
      case 'open': return <Square className="w-6 h-6 mr-2" />;
      case 'moving': return <Square className="w-6 h-6 mr-2" />;
      default: return <Play className="w-6 h-6 mr-2" />;
    }
  };
  const getMainButtonColor = () => {
    const baseClass = 'transition-all duration-200 transform hover:scale-105';
    const pressedClass = buttonPressed === 'main' ? 'animate-pulse ring-4 ring-yellow-400 shadow-lg shadow-yellow-400/50' : '';
    
    switch (status) {
      case 'closed': return `${baseClass} ${pressedClass} bg-green-500/20 hover:bg-green-500/30 border-green-500/30 text-green-300 hover:text-green-200`;
      case 'open': return `${baseClass} ${pressedClass} bg-red-500/20 hover:bg-red-500/30 border-red-500/30 text-red-300 hover:text-red-200`;
      case 'moving': return `${baseClass} ${pressedClass} bg-orange-500/20 hover:bg-orange-500/30 border-orange-500/30 text-orange-300 hover:text-orange-200`;
      default: return `${baseClass} ${pressedClass} bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 text-blue-300 hover:text-blue-200`;
    }
  };

  const getStopButtonColor = () => {
    const baseClass = 'transition-all duration-200 transform hover:scale-105';
    const pressedClass = buttonPressed === 'stop' ? 'animate-pulse ring-4 ring-yellow-400 shadow-lg shadow-yellow-400/50' : '';
    return `${baseClass} ${pressedClass} bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-300 hover:text-blue-200`;
  };

  return (
    <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20 shadow-2xl">
      <CardHeader>
        <CardTitle className="text-white flex items-center space-x-2">
          <Home className="w-6 h-6" />
          <span>Ovládání posuvné brány</span>
        </CardTitle>
      </CardHeader>
        <CardContent className="space-y-6">
        {/* Status Display */}
        <div className="text-center space-y-4">
          <div className={`text-4xl font-bold ${getStatusColor()}`}>
            {getStatusText()}
          </div>
        </div>
        
        {/* Výstražné zprávy */}
        {status === 'moving' && statusMessage && !statusMessage.includes('zavřena') && !statusMessage.includes('otevřena') && (
          <div className="bg-yellow-500/30 border-2 border-yellow-400 rounded-lg p-4 warning-animation shadow-lg shadow-yellow-500/20">
            <div className="text-center">
              <p className="text-yellow-100 text-2xl font-bold mb-2">
                ⚠️ BRÁNA V POHYBU ⚠️
              </p>
              <p className="text-yellow-200 text-xl">
                Zbývá: <span className="font-mono font-bold bg-yellow-600/50 px-2 py-1 rounded border border-yellow-400">
                  {remainingTime}s
                </span>
              </p>
              <p className="text-yellow-300 text-sm mt-2">
                Prosím čekejte, dokud se pohyb nedokončí
              </p>
            </div>
          </div>
        )}
        
        {status === 'open' && autoCloseTimer > 0 && (
          <div className="bg-orange-500/30 border-2 border-orange-400 rounded-lg p-4 warning-animation shadow-lg shadow-orange-500/20">
            <div className="text-center">
              <p className="text-orange-100 text-2xl font-bold mb-2">
                ⚠️ AUTOMATICKÉ ZAVŘENÍ ⚠️
              </p>
              <p className="text-orange-200 text-xl font-mono">
                <span className="bg-orange-600/50 px-3 py-1 rounded-lg border border-orange-400">
                  {formatTime(autoCloseTimer)}
                </span>
              </p>
              <p className="text-orange-300 text-sm mt-2">
                Brána se automaticky zavře za uvedený čas
              </p>
            </div>
          </div>
        )}
        
        {/* Control Buttons */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Button
            onClick={handleMainCommand}
            disabled={!hasPermission}
            className={`h-16 border ${getMainButtonColor()}`}
          >
            {getMainButtonIcon()}
            {getMainButtonText()}
          </Button>
          
          <Button
            onClick={handleStopCommand}
            disabled={!hasPermission || status === 'moving'}
            className={`h-16 ${getStopButtonColor()}`}
          >
            <StopCircle className="w-6 h-6 mr-2" />
            Otevřít STOP
          </Button>
        </div>

        {/* Feedback o odeslaném příkazu */}
        {commandSent && (
          <div className="bg-yellow-500/20 border border-yellow-400 rounded-lg p-3 animate-pulse">
            <p className="text-yellow-300 text-sm text-center">
              ⚡ Příkaz "{commandSent}" odeslán - čekám na odpověď...
            </p>
          </div>
        )}

        {!hasPermission && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
            <p className="text-red-300 text-sm text-center">
              <Lock className="w-4 h-4 inline mr-1" />
              Nemáte oprávnění k ovládání brány
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GateControl;
