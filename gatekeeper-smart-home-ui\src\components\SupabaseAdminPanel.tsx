import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { useAdmin } from '@/hooks/useAdmin';
import { 
  Users, 
  UserPlus, 
  Mail,
  Key,
  Copy,
  Check,
  Eye,
  EyeOff,
  Trash2,
  Edit,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Tables } from '@/lib/supabase';

const AdminPanel = () => {
  const { profile, loading: authLoading } = useAuth();
  const { 
    users, 
    invites, 
    loginLogs, 
    loading,
    fetchUsers,
    createUser,
    updateUser,
    deactivateUser,
    fetchInvites,
    createInvite,
    fetchLoginLogs
  } = useAdmin();
  
  const { toast } = useToast();
  
  // States pro nový uživatel
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [newUserData, setNewUserData] = useState({
    email: '',
    password: '',
    username: '',
    permissions: [] as string[]
  });
  
  // States pro pozvánku
  const [isCreatingInvite, setIsCreatingInvite] = useState(false);
  const [invitePermissions, setInvitePermissions] = useState<string[]>(['user']);
  const [inviteHours, setInviteHours] = useState(24);
  const [generatedInvite, setGeneratedInvite] = useState<{
    token: string;
    url: string;
    expiresAt: string;
  } | null>(null);
  const [copiedInvite, setCopiedInvite] = useState(false);

  const availablePermissions = [
    { id: 'user', label: 'Uživatel', description: 'Základní přístup k systému' },
    { id: 'gate', label: 'Ovládání brány', description: 'Může ovládat bránu' },
    { id: 'camera', label: 'Kamery', description: 'Může zobrazovat kamery' },
    { id: 'garage', label: 'Garáž', description: 'Může ovládat garáž' },
    { id: 'admin', label: 'Administrátor', description: 'Plný přístup k systému' }
  ];

  useEffect(() => {
    if (profile?.permissions.includes('admin')) {
      fetchUsers();
      fetchInvites();
      fetchLoginLogs();
    }
  }, [profile]);

  // Kontrola admin oprávnění
  if (authLoading) {
    return <div className="flex items-center justify-center min-h-screen">
      <RefreshCw className="animate-spin h-8 w-8" />
    </div>;
  }

  if (!profile?.permissions.includes('admin')) {
    return <div className="flex items-center justify-center min-h-screen">
      <Card className="p-6">
        <CardContent>
          <p className="text-center text-red-600">Nemáte oprávnění k přístupu do administrace.</p>
        </CardContent>
      </Card>
    </div>;
  }

  const handleCreateUser = async () => {
    try {
      setIsCreatingUser(true);
      await createUser(newUserData);
      
      toast({
        title: "Uživatel vytvořen",
        description: `Uživatel ${newUserData.username} byl úspěšně vytvořen`,
      });
      
      setNewUserData({ email: '', password: '', username: '', permissions: [] });
    } catch (error) {
      toast({
        title: "Chyba při vytváření uživatele",
        description: error instanceof Error ? error.message : "Neočekávaná chyba",
        variant: "destructive",
      });
    } finally {
      setIsCreatingUser(false);
    }
  };

  const handleCreateInvite = async () => {
    try {
      setIsCreatingInvite(true);
      const invite = await createInvite(invitePermissions, inviteHours);
      setGeneratedInvite(invite);
      
      toast({
        title: "Pozvánka vytvořena",
        description: "Pozvánkový odkaz byl úspěšně vygenerován",
      });
    } catch (error) {
      toast({
        title: "Chyba při vytváření pozvánky",
        description: error instanceof Error ? error.message : "Neočekávaná chyba",
        variant: "destructive",
      });
    } finally {
      setIsCreatingInvite(false);
    }
  };

  const handleTogglePermission = (permission: string, isUser: boolean = false) => {
    if (isUser) {
      setNewUserData(prev => ({
        ...prev,
        permissions: prev.permissions.includes(permission)
          ? prev.permissions.filter(p => p !== permission)
          : [...prev.permissions, permission]
      }));
    } else {
      setInvitePermissions(prev => 
        prev.includes(permission)
          ? prev.filter(p => p !== permission)
          : [...prev, permission]
      );
    }
  };

  const copyInviteUrl = async () => {
    if (generatedInvite) {
      await navigator.clipboard.writeText(generatedInvite.url);
      setCopiedInvite(true);
      setTimeout(() => setCopiedInvite(false), 2000);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('cs-CZ');
  };

  const getPermissionBadges = (permissions: string[]) => {
    return permissions.map(permission => {
      const permData = availablePermissions.find(p => p.id === permission);
      return (
        <Badge 
          key={permission} 
          variant={permission === 'admin' ? 'destructive' : 'secondary'}
          className="text-xs"
        >
          {permData?.label || permission}
        </Badge>
      );
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Administrace uživatelů</h1>
        <Button onClick={() => window.location.reload()} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Obnovit
        </Button>
      </div>

      <Tabs defaultValue="users" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Vytvořit uživatele
          </TabsTrigger>
          <TabsTrigger value="invites" className="flex items-center gap-2">
            <Mail className="w-4 h-4" />
            Pozvat uživatele
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            Registrování uživatelé
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          {/* Vytvoření nového uživatele */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Pozvat nového uživatele
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUserData.email}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="username">Uživatelské jméno</Label>
                  <Input
                    id="username"
                    value={newUserData.username}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, username: e.target.value }))}
                    placeholder="johndoe"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="password">Heslo</Label>
                <Input
                  id="password"
                  type="password"
                  value={newUserData.password}
                  onChange={(e) => setNewUserData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Minimálně 6 znaků"
                />
              </div>

              <div>
                <Label>Oprávnění pro nového uživatele</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {availablePermissions.map(permission => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`user-${permission.id}`}
                        checked={newUserData.permissions.includes(permission.id)}
                        onCheckedChange={() => handleTogglePermission(permission.id, true)}
                      />
                      <Label htmlFor={`user-${permission.id}`} className="text-sm">
                        {permission.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <Button 
                onClick={handleCreateUser} 
                disabled={isCreatingUser || !newUserData.email || !newUserData.password || !newUserData.username}
                className="w-full"
              >
                {isCreatingUser ? 'Vytváří se...' : 'Vytvořit uživatele'}
              </Button>
            </CardContent>
          </Card>

          {/* Seznam uživatelů */}
          <Card>
            <CardHeader>
              <CardTitle>Registrovaní uživatelé ({users.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center">
                  <RefreshCw className="animate-spin h-6 w-6" />
                </div>
              ) : (
                <div className="space-y-3">
                  {users.map((user) => (
                    <div key={user.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">{user.username}</h3>
                          <p className="text-sm text-gray-600">{user.email}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={user.is_active ? 'default' : 'secondary'}>
                            {user.is_active ? 'Aktivní' : 'Neaktivní'}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {getPermissionBadges(user.permissions)}
                      </div>
                      <div className="text-xs text-gray-500">
                        Vytvořen: {formatDate(user.created_at)}
                        {user.last_login && ` • Poslední přihlášení: ${formatDate(user.last_login)}`}
                      </div>
                    </div>
                  ))}
                  {users.length === 0 && (
                    <p className="text-center text-gray-500 py-8">Žádní uživatelé</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invites" className="space-y-6">
          {/* Vytvoření pozvánky */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Vytvořit pozvánku
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Oprávnění pro nového uživatele</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {availablePermissions.map(permission => (
                    <div key={permission.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`invite-${permission.id}`}
                        checked={invitePermissions.includes(permission.id)}
                        onCheckedChange={() => handleTogglePermission(permission.id, false)}
                      />
                      <Label htmlFor={`invite-${permission.id}`} className="text-sm">
                        {permission.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label htmlFor="hours">Platnost pozvanky (hodiny)</Label>
                <Input
                  id="hours"
                  type="number"
                  value={inviteHours}
                  onChange={(e) => setInviteHours(parseInt(e.target.value) || 24)}
                  min="1"
                  max="168"
                />
              </div>

              <Button 
                onClick={handleCreateInvite} 
                disabled={isCreatingInvite || invitePermissions.length === 0}
                className="w-full"
              >
                {isCreatingInvite ? 'Generuje se...' : 'Vytvořit pozvánku'}
              </Button>

              {generatedInvite && (
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-lg">Pozvánka vytvořena!</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <Label>Pozvánkový odkaz:</Label>
                      <div className="flex gap-2 mt-1">
                        <Input value={generatedInvite.url} readOnly className="flex-1" />
                        <Button onClick={copyInviteUrl} variant="outline">
                          {copiedInvite ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">
                      Platnost do: {formatDate(generatedInvite.expiresAt)}
                    </p>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>

          {/* Seznam pozvánek */}
          <Card>
            <CardHeader>
              <CardTitle>Vygenerované pozvánky</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center">
                  <RefreshCw className="animate-spin h-6 w-6" />
                </div>
              ) : (
                <div className="space-y-3">
                  {invites.map((invite) => (
                    <div key={invite.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-mono text-sm">{invite.token.substring(0, 8)}...</p>
                          <p className="text-xs text-gray-600">
                            Platnost do: {formatDate(invite.expires_at)}
                          </p>
                        </div>
                        <Badge variant={invite.used_by ? 'secondary' : 'default'}>
                          {invite.used_by ? 'Použito' : 'Aktivní'}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {getPermissionBadges(invite.permissions)}
                      </div>
                    </div>
                  ))}
                  {invites.length === 0 && (
                    <p className="text-center text-gray-500 py-8">Žádné pozvánky</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>Login logy</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center">
                  <RefreshCw className="animate-spin h-6 w-6" />
                </div>
              ) : (
                <div className="space-y-2">
                  {loginLogs.map((log) => (
                    <div key={log.id} className="border rounded p-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span>{log.user_id}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant={log.success ? 'default' : 'destructive'}>
                            {log.success ? 'Úspěch' : 'Chyba'}
                          </Badge>
                          <span className="text-gray-500">{formatDate(log.timestamp)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                  {loginLogs.length === 0 && (
                    <p className="text-center text-gray-500 py-8">Žádné logy</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPanel;
