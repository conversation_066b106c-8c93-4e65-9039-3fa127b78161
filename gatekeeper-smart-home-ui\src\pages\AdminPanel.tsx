import { useAuth } from '@/hooks/useAuth';
import { Navigate } from 'react-router-dom';
import NavigationMenu from '@/components/NavigationMenu';
import { useMQTT } from '@/hooks/useMQTT';
import AdminPanel from '@/components/AdminPanel';

const AdminPanelPage = () => {
  const { user, logout } = useAuth();
  const { connected } = useMQTT();

  if (!user?.permissions?.includes('admin')) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
      
      {/* Navigation */}
      <NavigationMenu user={user} onLogout={logout} connected={connected} />

      {/* Admin Panel Content */}
      <div className="relative z-10 pt-24">
        <AdminPanel />
      </div>
    </div>
  );
};

export default AdminPanelPage;
