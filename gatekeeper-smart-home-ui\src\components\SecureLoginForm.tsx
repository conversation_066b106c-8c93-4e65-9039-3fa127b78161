import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { 
  Shield, 
  Lock, 
  Mail, 
  Loader2, 
  Eye, 
  EyeOff, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, type LoginFormData, getPasswordStrength } from '@/lib/validation';

const SecureLoginForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0);
  const { login } = useAuth();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setError,
    clearErrors
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false
    }
  });

  const watchedEmail = watch('email');
  const watchedPassword = watch('password');

  // Kontrola síly hesla pro vizuální feedback
  const passwordStrength = watchedPassword ? getPasswordStrength(watchedPassword) : { score: 0, feedback: [] };

  const onSubmit = async (data: LoginFormData) => {
    try {
      // Kontrola blokování
      if (isBlocked) {
        toast({
          title: "Účet je dočasně zablokován",
          description: `Zkuste to znovu za ${Math.ceil(blockTimeRemaining / 60)} minut`,
          variant: "destructive",
        });
        return;
      }

      // Kontrola počtu pokusů
      if (loginAttempts >= 5) {
        setIsBlocked(true);
        setBlockTimeRemaining(15 * 60); // 15 minut
        
        // Odpočítávání
        const interval = setInterval(() => {
          setBlockTimeRemaining(prev => {
            if (prev <= 1) {
              setIsBlocked(false);
              setLoginAttempts(0);
              clearInterval(interval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        toast({
          title: "Příliš mnoho pokusů",
          description: "Účet byl dočasně zablokován na 15 minut",
          variant: "destructive",
        });
        return;
      }

      await login(data.email, data.password);
      
      // Reset při úspěšném přihlášení
      setLoginAttempts(0);
      setIsBlocked(false);
      clearErrors();
      
      toast({
        title: "Přihlášení úspěšné",
        description: "Vítejte v IoT Smart Control System",
      });
    } catch (error) {
      // Zvýšení počtu pokusů
      setLoginAttempts(prev => prev + 1);
      
      const errorMessage = error instanceof Error ? error.message : "Neočekávaná chyba";
      
      // Specifické chybové zprávy
      if (errorMessage.includes('Invalid login credentials') || 
          errorMessage.includes('Invalid email or password')) {
        setError('email', { message: 'Neplatné přihlašovací údaje' });
        setError('password', { message: 'Neplatné přihlašovací údaje' });
      } else if (errorMessage.includes('rate limit') || 
                 errorMessage.includes('Příliš mnoho pokusů')) {
        setError('email', { message: 'Příliš mnoho pokusů. Zkuste to později.' });
      } else if (errorMessage.includes('Email not confirmed')) {
        setError('email', { message: 'Email nebyl potvrzen. Zkontrolujte svou poštu.' });
      }
      
      toast({
        title: "Chyba přihlášení",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const getSecurityLevel = () => {
    let level = 0;
    if (watchedEmail && !errors.email) level++;
    if (watchedPassword && passwordStrength.score >= 3) level++;
    return level;
  };

  const securityLevel = getSecurityLevel();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-2xl border-slate-700">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
              <Shield className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Zabezpečené přihlášení
          </CardTitle>
          <p className="text-center text-muted-foreground">
            IoT Smart Control System
          </p>
          
          {/* Bezpečnostní indikátor */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Úroveň zabezpečení:</span>
              <Badge variant={securityLevel === 2 ? "default" : securityLevel === 1 ? "secondary" : "outline"}>
                {securityLevel === 2 ? "Vysoká" : securityLevel === 1 ? "Střední" : "Nízká"}
              </Badge>
            </div>
            <Progress value={(securityLevel / 2) * 100} className="h-2" />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Upozornění na pokusy */}
          {loginAttempts > 0 && (
            <Alert variant={loginAttempts >= 3 ? "destructive" : "default"}>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {loginAttempts >= 3 
                  ? `Varování: ${loginAttempts}/5 neúspěšných pokusů. Účet bude zablokován.`
                  : `Neúspěšné pokusy: ${loginAttempts}/5`
                }
              </AlertDescription>
            </Alert>
          )}

          {/* Blokování účtu */}
          {isBlocked && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                Účet je zablokován na {Math.ceil(blockTimeRemaining / 60)} minut kvůli příliš mnoha neúspěšným pokusům.
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                  disabled={isSubmitting || isBlocked}
                />
                {watchedEmail && !errors.email && (
                  <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                )}
              </div>
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            {/* Heslo */}
            <div className="space-y-2">
              <Label htmlFor="password" className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Heslo
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Vaše heslo"
                  {...register('password')}
                  className={errors.password ? 'border-red-500' : ''}
                  disabled={isSubmitting || isBlocked}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isSubmitting || isBlocked}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
              
              {/* Indikátor síly hesla */}
              {watchedPassword && (
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>Síla hesla:</span>
                    <span className={
                      passwordStrength.score >= 4 ? 'text-green-500' :
                      passwordStrength.score >= 3 ? 'text-yellow-500' :
                      'text-red-500'
                    }>
                      {passwordStrength.score >= 4 ? 'Silné' :
                       passwordStrength.score >= 3 ? 'Střední' :
                       'Slabé'}
                    </span>
                  </div>
                  <Progress 
                    value={(passwordStrength.score / 5) * 100} 
                    className="h-1"
                  />
                </div>
              )}
            </div>

            {/* Zapamatovat si */}
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="rememberMe" 
                {...register('rememberMe')}
                disabled={isSubmitting || isBlocked}
              />
              <Label htmlFor="rememberMe" className="text-sm">
                Zapamatovat si přihlášení
              </Label>
            </div>

            {/* Tlačítko přihlášení */}
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isSubmitting || isBlocked}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Přihlašování...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-4 w-4" />
                  Přihlásit se
                </>
              )}
            </Button>
          </form>

          {/* Bezpečnostní informace */}
          <div className="text-center text-xs text-muted-foreground space-y-1">
            <p>🔒 Vaše data jsou chráněna end-to-end šifrováním</p>
            <p>🛡️ Všechny přihlašovací pokusy jsou monitorovány</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecureLoginForm;
