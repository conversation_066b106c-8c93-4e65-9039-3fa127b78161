"use strict";var mqtt=(()=>{var hs=Object.defineProperty;var $g=Object.getOwnPropertyDescriptor;var Hg=Object.getOwnPropertyNames;var Vg=Object.prototype.hasOwnProperty;var Ae=(t,e)=>()=>(t&&(e=t(t=0)),e);var O=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Ir=(t,e)=>{for(var r in e)hs(t,r,{get:e[r],enumerable:!0})},zg=(t,e,r,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Hg(e))!Vg.call(t,n)&&n!==r&&hs(t,n,{get:()=>e[n],enumerable:!(i=$g(e,n))||i.enumerable});return t};var Q=t=>zg(hs({},"__esModule",{value:!0}),t);var _=Ae(()=>{});var R={};Ir(R,{_debugEnd:()=>fu,_debugProcess:()=>cu,_events:()=>Ru,_eventsCount:()=>Cu,_exiting:()=>Vl,_fatalExceptions:()=>ou,_getActiveHandles:()=>Ql,_getActiveRequests:()=>Gl,_kill:()=>Jl,_linkedBinding:()=>$l,_maxListeners:()=>Tu,_preload_modules:()=>Su,_rawDebug:()=>Fl,_startProfilerIdleNotifier:()=>hu,_stopProfilerIdleNotifier:()=>du,_tickCallback:()=>uu,abort:()=>yu,addListener:()=>Pu,allowedNodeEnvironmentFlags:()=>iu,arch:()=>Cl,argv:()=>Bl,argv0:()=>Eu,assert:()=>nu,binding:()=>ql,chdir:()=>Dl,config:()=>zl,cpuUsage:()=>ji,cwd:()=>Nl,debugPort:()=>vu,default:()=>Nu,dlopen:()=>Kl,domain:()=>Hl,emit:()=>Mu,emitWarning:()=>Ll,env:()=>kl,execArgv:()=>xl,execPath:()=>mu,exit:()=>tu,features:()=>su,hasUncaughtExceptionCaptureCallback:()=>lu,hrtime:()=>Di,kill:()=>eu,listeners:()=>Uu,memoryUsage:()=>Zl,moduleLoadList:()=>Wl,nextTick:()=>Il,off:()=>Bu,on:()=>bt,once:()=>ku,openStdin:()=>ru,pid:()=>wu,platform:()=>Pl,ppid:()=>_u,prependListener:()=>Lu,prependOnceListener:()=>qu,reallyExit:()=>Yl,release:()=>jl,removeAllListeners:()=>Ou,removeListener:()=>xu,resourceUsage:()=>Xl,setSourceMapsEnabled:()=>Au,setUncaughtExceptionCaptureCallback:()=>au,stderr:()=>gu,stdin:()=>bu,stdout:()=>pu,title:()=>Rl,umask:()=>Ul,uptime:()=>Iu,version:()=>Ol,versions:()=>Ml});function gs(t){throw new Error("Node.js process "+t+" is not supported by JSPM core outside of Node.js")}function Kg(){!Tr||!Gt||(Tr=!1,Gt.length?gt=Gt.concat(gt):Ni=-1,gt.length&&Al())}function Al(){if(!Tr){var t=setTimeout(Kg,0);Tr=!0;for(var e=gt.length;e;){for(Gt=gt,gt=[];++Ni<e;)Gt&&Gt[Ni].run();Ni=-1,e=gt.length}Gt=null,Tr=!1,clearTimeout(t)}}function Il(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];gt.push(new Tl(t,e)),gt.length===1&&!Tr&&setTimeout(Al,0)}function Tl(t,e){this.fun=t,this.array=e}function _e(){}function $l(t){gs("_linkedBinding")}function Kl(t){gs("dlopen")}function Gl(){return[]}function Ql(){return[]}function nu(t,e){if(!t)throw new Error(e||"assertion error")}function lu(){return!1}function Iu(){return kt.now()/1e3}function Di(t){var e=Math.floor((Date.now()-kt.now())*.001),r=kt.now()*.001,i=Math.floor(r)+e,n=Math.floor(r%1*1e9);return t&&(i=i-t[0],n=n-t[1],n<0&&(i--,n+=ps)),[i,n]}function bt(){return Nu}function Uu(t){return[]}var gt,Tr,Gt,Ni,Rl,Cl,Pl,kl,Bl,xl,Ol,Ml,Ll,ql,Ul,Nl,Dl,jl,Fl,Wl,Hl,Vl,zl,Yl,Jl,ji,Xl,Zl,eu,tu,ru,iu,su,ou,au,uu,cu,fu,hu,du,pu,gu,bu,yu,wu,_u,mu,vu,Eu,Su,Au,kt,ds,ps,Tu,Ru,Cu,Pu,ku,Bu,xu,Ou,Mu,Lu,qu,Nu,Du=Ae(()=>{_();v();m();gt=[],Tr=!1,Ni=-1;Tl.prototype.run=function(){this.fun.apply(null,this.array)};Rl="browser",Cl="x64",Pl="browser",kl={PATH:"/usr/bin",LANG:navigator.language+".UTF-8",PWD:"/",HOME:"/home",TMP:"/tmp"},Bl=["/usr/bin/node"],xl=[],Ol="v16.8.0",Ml={},Ll=function(t,e){console.warn((e?e+": ":"")+t)},ql=function(t){gs("binding")},Ul=function(t){return 0},Nl=function(){return"/"},Dl=function(t){},jl={name:"node",sourceUrl:"",headersUrl:"",libUrl:""};Fl=_e,Wl=[];Hl={},Vl=!1,zl={};Yl=_e,Jl=_e,ji=function(){return{}},Xl=ji,Zl=ji,eu=_e,tu=_e,ru=_e,iu={};su={inspector:!1,debug:!1,uv:!1,ipv6:!1,tls_alpn:!1,tls_sni:!1,tls_ocsp:!1,tls:!1,cached_builtins:!0},ou=_e,au=_e;uu=_e,cu=_e,fu=_e,hu=_e,du=_e,pu=void 0,gu=void 0,bu=void 0,yu=_e,wu=2,_u=1,mu="/bin/usr/node",vu=9229,Eu="node",Su=[],Au=_e,kt={now:typeof performance<"u"?performance.now.bind(performance):void 0,timing:typeof performance<"u"?performance.timing:void 0};kt.now===void 0&&(ds=Date.now(),kt.timing&&kt.timing.navigationStart&&(ds=kt.timing.navigationStart),kt.now=()=>Date.now()-ds);ps=1e9;Di.bigint=function(t){var e=Di(t);return typeof BigInt>"u"?e[0]*ps+e[1]:BigInt(e[0]*ps)+BigInt(e[1])};Tu=10,Ru={},Cu=0;Pu=bt,ku=bt,Bu=bt,xu=bt,Ou=bt,Mu=_e,Lu=bt,qu=bt;Nu={version:Ol,versions:Ml,arch:Cl,platform:Pl,release:jl,_rawDebug:Fl,moduleLoadList:Wl,binding:ql,_linkedBinding:$l,_events:Ru,_eventsCount:Cu,_maxListeners:Tu,on:bt,addListener:Pu,once:ku,off:Bu,removeListener:xu,removeAllListeners:Ou,emit:Mu,prependListener:Lu,prependOnceListener:qu,listeners:Uu,domain:Hl,_exiting:Vl,config:zl,dlopen:Kl,uptime:Iu,_getActiveRequests:Gl,_getActiveHandles:Ql,reallyExit:Yl,_kill:Jl,cpuUsage:ji,resourceUsage:Xl,memoryUsage:Zl,kill:eu,exit:tu,openStdin:ru,allowedNodeEnvironmentFlags:iu,assert:nu,features:su,_fatalExceptions:ou,setUncaughtExceptionCaptureCallback:au,hasUncaughtExceptionCaptureCallback:lu,emitWarning:Ll,nextTick:Il,_tickCallback:uu,_debugProcess:cu,_debugEnd:fu,_startProfilerIdleNotifier:hu,_stopProfilerIdleNotifier:du,stdout:pu,stdin:bu,stderr:gu,abort:yu,umask:Ul,chdir:Dl,cwd:Nl,env:kl,title:Rl,argv:Bl,execArgv:xl,pid:wu,ppid:_u,execPath:mu,debugPort:vu,hrtime:Di,argv0:Eu,_preload_modules:Su,setSourceMapsEnabled:Au}});var m=Ae(()=>{Du()});var ye={};Ir(ye,{Buffer:()=>x,INSPECT_MAX_BYTES:()=>Jg,default:()=>Bt,kMaxLength:()=>Xg});function Gg(){if(ju)return oi;ju=!0,oi.byteLength=a,oi.toByteArray=f,oi.fromByteArray=g;for(var t=[],e=[],r=typeof Uint8Array<"u"?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=0,o=i.length;n<o;++n)t[n]=i[n],e[i.charCodeAt(n)]=n;e[45]=62,e[95]=63;function s(b){var E=b.length;if(E%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var w=b.indexOf("=");w===-1&&(w=E);var S=w===E?0:4-w%4;return[w,S]}function a(b){var E=s(b),w=E[0],S=E[1];return(w+S)*3/4-S}function u(b,E,w){return(E+w)*3/4-w}function f(b){var E,w=s(b),S=w[0],I=w[1],P=new r(u(b,S,I)),C=0,M=I>0?S-4:S,q;for(q=0;q<M;q+=4)E=e[b.charCodeAt(q)]<<18|e[b.charCodeAt(q+1)]<<12|e[b.charCodeAt(q+2)]<<6|e[b.charCodeAt(q+3)],P[C++]=E>>16&255,P[C++]=E>>8&255,P[C++]=E&255;return I===2&&(E=e[b.charCodeAt(q)]<<2|e[b.charCodeAt(q+1)]>>4,P[C++]=E&255),I===1&&(E=e[b.charCodeAt(q)]<<10|e[b.charCodeAt(q+1)]<<4|e[b.charCodeAt(q+2)]>>2,P[C++]=E>>8&255,P[C++]=E&255),P}function d(b){return t[b>>18&63]+t[b>>12&63]+t[b>>6&63]+t[b&63]}function h(b,E,w){for(var S,I=[],P=E;P<w;P+=3)S=(b[P]<<16&16711680)+(b[P+1]<<8&65280)+(b[P+2]&255),I.push(d(S));return I.join("")}function g(b){for(var E,w=b.length,S=w%3,I=[],P=16383,C=0,M=w-S;C<M;C+=P)I.push(h(b,C,C+P>M?M:C+P));return S===1?(E=b[w-1],I.push(t[E>>2]+t[E<<4&63]+"==")):S===2&&(E=(b[w-2]<<8)+b[w-1],I.push(t[E>>10]+t[E>>4&63]+t[E<<2&63]+"=")),I.join("")}return oi}function Qg(){if(Fu)return Fi;Fu=!0;return Fi.read=function(t,e,r,i,n){var o,s,a=n*8-i-1,u=(1<<a)-1,f=u>>1,d=-7,h=r?n-1:0,g=r?-1:1,b=t[e+h];for(h+=g,o=b&(1<<-d)-1,b>>=-d,d+=a;d>0;o=o*256+t[e+h],h+=g,d-=8);for(s=o&(1<<-d)-1,o>>=-d,d+=i;d>0;s=s*256+t[e+h],h+=g,d-=8);if(o===0)o=1-f;else{if(o===u)return s?NaN:(b?-1:1)*(1/0);s=s+Math.pow(2,i),o=o-f}return(b?-1:1)*s*Math.pow(2,o-i)},Fi.write=function(t,e,r,i,n,o){var s,a,u,f=o*8-n-1,d=(1<<f)-1,h=d>>1,g=n===23?Math.pow(2,-24)-Math.pow(2,-77):0,b=i?0:o-1,E=i?1:-1,w=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=d):(s=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-s))<1&&(s--,u*=2),s+h>=1?e+=g/u:e+=g*Math.pow(2,1-h),e*u>=2&&(s++,u/=2),s+h>=d?(a=0,s=d):s+h>=1?(a=(e*u-1)*Math.pow(2,n),s=s+h):(a=e*Math.pow(2,h-1)*Math.pow(2,n),s=0));n>=8;t[r+b]=a&255,b+=E,a/=256,n-=8);for(s=s<<n|a,f+=n;f>0;t[r+b]=s&255,b+=E,s/=256,f-=8);t[r+b-E]|=w*128},Fi}function Yg(){if(Wu)return Qt;Wu=!0;let t=Gg(),e=Qg(),r=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;Qt.Buffer=s,Qt.SlowBuffer=I,Qt.INSPECT_MAX_BYTES=50;let i=2147483647;Qt.kMaxLength=i,s.TYPED_ARRAY_SUPPORT=n(),!s.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function n(){try{let p=new Uint8Array(1),l={foo:function(){return 42}};return Object.setPrototypeOf(l,Uint8Array.prototype),Object.setPrototypeOf(p,l),p.foo()===42}catch{return!1}}Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}});function o(p){if(p>i)throw new RangeError('The value "'+p+'" is invalid for option "size"');let l=new Uint8Array(p);return Object.setPrototypeOf(l,s.prototype),l}function s(p,l,c){if(typeof p=="number"){if(typeof l=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return d(p)}return a(p,l,c)}s.poolSize=8192;function a(p,l,c){if(typeof p=="string")return h(p,l);if(ArrayBuffer.isView(p))return b(p);if(p==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p);if(Qe(p,ArrayBuffer)||p&&Qe(p.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Qe(p,SharedArrayBuffer)||p&&Qe(p.buffer,SharedArrayBuffer)))return E(p,l,c);if(typeof p=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let y=p.valueOf&&p.valueOf();if(y!=null&&y!==p)return s.from(y,l,c);let A=w(p);if(A)return A;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof p[Symbol.toPrimitive]=="function")return s.from(p[Symbol.toPrimitive]("string"),l,c);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof p)}s.from=function(p,l,c){return a(p,l,c)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array);function u(p){if(typeof p!="number")throw new TypeError('"size" argument must be of type number');if(p<0)throw new RangeError('The value "'+p+'" is invalid for option "size"')}function f(p,l,c){return u(p),p<=0?o(p):l!==void 0?typeof c=="string"?o(p).fill(l,c):o(p).fill(l):o(p)}s.alloc=function(p,l,c){return f(p,l,c)};function d(p){return u(p),o(p<0?0:S(p)|0)}s.allocUnsafe=function(p){return d(p)},s.allocUnsafeSlow=function(p){return d(p)};function h(p,l){if((typeof l!="string"||l==="")&&(l="utf8"),!s.isEncoding(l))throw new TypeError("Unknown encoding: "+l);let c=P(p,l)|0,y=o(c),A=y.write(p,l);return A!==c&&(y=y.slice(0,A)),y}function g(p){let l=p.length<0?0:S(p.length)|0,c=o(l);for(let y=0;y<l;y+=1)c[y]=p[y]&255;return c}function b(p){if(Qe(p,Uint8Array)){let l=new Uint8Array(p);return E(l.buffer,l.byteOffset,l.byteLength)}return g(p)}function E(p,l,c){if(l<0||p.byteLength<l)throw new RangeError('"offset" is outside of buffer bounds');if(p.byteLength<l+(c||0))throw new RangeError('"length" is outside of buffer bounds');let y;return l===void 0&&c===void 0?y=new Uint8Array(p):c===void 0?y=new Uint8Array(p,l):y=new Uint8Array(p,l,c),Object.setPrototypeOf(y,s.prototype),y}function w(p){if(s.isBuffer(p)){let l=S(p.length)|0,c=o(l);return c.length===0||p.copy(c,0,0,l),c}if(p.length!==void 0)return typeof p.length!="number"||fs(p.length)?o(0):g(p);if(p.type==="Buffer"&&Array.isArray(p.data))return g(p.data)}function S(p){if(p>=i)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i.toString(16)+" bytes");return p|0}function I(p){return+p!=p&&(p=0),s.alloc(+p)}s.isBuffer=function(l){return l!=null&&l._isBuffer===!0&&l!==s.prototype},s.compare=function(l,c){if(Qe(l,Uint8Array)&&(l=s.from(l,l.offset,l.byteLength)),Qe(c,Uint8Array)&&(c=s.from(c,c.offset,c.byteLength)),!s.isBuffer(l)||!s.isBuffer(c))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(l===c)return 0;let y=l.length,A=c.length;for(let T=0,k=Math.min(y,A);T<k;++T)if(l[T]!==c[T]){y=l[T],A=c[T];break}return y<A?-1:A<y?1:0},s.isEncoding=function(l){switch(String(l).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(l,c){if(!Array.isArray(l))throw new TypeError('"list" argument must be an Array of Buffers');if(l.length===0)return s.alloc(0);let y;if(c===void 0)for(c=0,y=0;y<l.length;++y)c+=l[y].length;let A=s.allocUnsafe(c),T=0;for(y=0;y<l.length;++y){let k=l[y];if(Qe(k,Uint8Array))T+k.length>A.length?(s.isBuffer(k)||(k=s.from(k)),k.copy(A,T)):Uint8Array.prototype.set.call(A,k,T);else if(s.isBuffer(k))k.copy(A,T);else throw new TypeError('"list" argument must be an Array of Buffers');T+=k.length}return A};function P(p,l){if(s.isBuffer(p))return p.length;if(ArrayBuffer.isView(p)||Qe(p,ArrayBuffer))return p.byteLength;if(typeof p!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof p);let c=p.length,y=arguments.length>2&&arguments[2]===!0;if(!y&&c===0)return 0;let A=!1;for(;;)switch(l){case"ascii":case"latin1":case"binary":return c;case"utf8":case"utf-8":return cs(p).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c*2;case"hex":return c>>>1;case"base64":return Sl(p).length;default:if(A)return y?-1:cs(p).length;l=(""+l).toLowerCase(),A=!0}}s.byteLength=P;function C(p,l,c){let y=!1;if((l===void 0||l<0)&&(l=0),l>this.length||((c===void 0||c>this.length)&&(c=this.length),c<=0)||(c>>>=0,l>>>=0,c<=l))return"";for(p||(p="utf8");;)switch(p){case"hex":return Mg(this,l,c);case"utf8":case"utf-8":return Se(this,l,c);case"ascii":return ls(this,l,c);case"latin1":case"binary":return Og(this,l,c);case"base64":return Fe(this,l,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Lg(this,l,c);default:if(y)throw new TypeError("Unknown encoding: "+p);p=(p+"").toLowerCase(),y=!0}}s.prototype._isBuffer=!0;function M(p,l,c){let y=p[l];p[l]=p[c],p[c]=y}s.prototype.swap16=function(){let l=this.length;if(l%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let c=0;c<l;c+=2)M(this,c,c+1);return this},s.prototype.swap32=function(){let l=this.length;if(l%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let c=0;c<l;c+=4)M(this,c,c+3),M(this,c+1,c+2);return this},s.prototype.swap64=function(){let l=this.length;if(l%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let c=0;c<l;c+=8)M(this,c,c+7),M(this,c+1,c+6),M(this,c+2,c+5),M(this,c+3,c+4);return this},s.prototype.toString=function(){let l=this.length;return l===0?"":arguments.length===0?Se(this,0,l):C.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(l){if(!s.isBuffer(l))throw new TypeError("Argument must be a Buffer");return this===l?!0:s.compare(this,l)===0},s.prototype.inspect=function(){let l="",c=Qt.INSPECT_MAX_BYTES;return l=this.toString("hex",0,c).replace(/(.{2})/g,"$1 ").trim(),this.length>c&&(l+=" ... "),"<Buffer "+l+">"},r&&(s.prototype[r]=s.prototype.inspect),s.prototype.compare=function(l,c,y,A,T){if(Qe(l,Uint8Array)&&(l=s.from(l,l.offset,l.byteLength)),!s.isBuffer(l))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof l);if(c===void 0&&(c=0),y===void 0&&(y=l?l.length:0),A===void 0&&(A=0),T===void 0&&(T=this.length),c<0||y>l.length||A<0||T>this.length)throw new RangeError("out of range index");if(A>=T&&c>=y)return 0;if(A>=T)return-1;if(c>=y)return 1;if(c>>>=0,y>>>=0,A>>>=0,T>>>=0,this===l)return 0;let k=T-A,W=y-c,ae=Math.min(k,W),re=this.slice(A,T),le=l.slice(c,y);for(let J=0;J<ae;++J)if(re[J]!==le[J]){k=re[J],W=le[J];break}return k<W?-1:W<k?1:0};function q(p,l,c,y,A){if(p.length===0)return-1;if(typeof c=="string"?(y=c,c=0):c>2147483647?c=2147483647:c<-2147483648&&(c=-2147483648),c=+c,fs(c)&&(c=A?0:p.length-1),c<0&&(c=p.length+c),c>=p.length){if(A)return-1;c=p.length-1}else if(c<0)if(A)c=0;else return-1;if(typeof l=="string"&&(l=s.from(l,y)),s.isBuffer(l))return l.length===0?-1:z(p,l,c,y,A);if(typeof l=="number")return l=l&255,typeof Uint8Array.prototype.indexOf=="function"?A?Uint8Array.prototype.indexOf.call(p,l,c):Uint8Array.prototype.lastIndexOf.call(p,l,c):z(p,[l],c,y,A);throw new TypeError("val must be string, number or Buffer")}function z(p,l,c,y,A){let T=1,k=p.length,W=l.length;if(y!==void 0&&(y=String(y).toLowerCase(),y==="ucs2"||y==="ucs-2"||y==="utf16le"||y==="utf-16le")){if(p.length<2||l.length<2)return-1;T=2,k/=2,W/=2,c/=2}function ae(le,J){return T===1?le[J]:le.readUInt16BE(J*T)}let re;if(A){let le=-1;for(re=c;re<k;re++)if(ae(p,re)===ae(l,le===-1?0:re-le)){if(le===-1&&(le=re),re-le+1===W)return le*T}else le!==-1&&(re-=re-le),le=-1}else for(c+W>k&&(c=k-W),re=c;re>=0;re--){let le=!0;for(let J=0;J<W;J++)if(ae(p,re+J)!==ae(l,J)){le=!1;break}if(le)return re}return-1}s.prototype.includes=function(l,c,y){return this.indexOf(l,c,y)!==-1},s.prototype.indexOf=function(l,c,y){return q(this,l,c,y,!0)},s.prototype.lastIndexOf=function(l,c,y){return q(this,l,c,y,!1)};function j(p,l,c,y){c=Number(c)||0;let A=p.length-c;y?(y=Number(y),y>A&&(y=A)):y=A;let T=l.length;y>T/2&&(y=T/2);let k;for(k=0;k<y;++k){let W=parseInt(l.substr(k*2,2),16);if(fs(W))return k;p[c+k]=W}return k}function G(p,l,c,y){return Ui(cs(l,p.length-c),p,c,y)}function $(p,l,c,y){return Ui(Dg(l),p,c,y)}function te(p,l,c,y){return Ui(Sl(l),p,c,y)}function pt(p,l,c,y){return Ui(jg(l,p.length-c),p,c,y)}s.prototype.write=function(l,c,y,A){if(c===void 0)A="utf8",y=this.length,c=0;else if(y===void 0&&typeof c=="string")A=c,y=this.length,c=0;else if(isFinite(c))c=c>>>0,isFinite(y)?(y=y>>>0,A===void 0&&(A="utf8")):(A=y,y=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let T=this.length-c;if((y===void 0||y>T)&&(y=T),l.length>0&&(y<0||c<0)||c>this.length)throw new RangeError("Attempt to write outside buffer bounds");A||(A="utf8");let k=!1;for(;;)switch(A){case"hex":return j(this,l,c,y);case"utf8":case"utf-8":return G(this,l,c,y);case"ascii":case"latin1":case"binary":return $(this,l,c,y);case"base64":return te(this,l,c,y);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return pt(this,l,c,y);default:if(k)throw new TypeError("Unknown encoding: "+A);A=(""+A).toLowerCase(),k=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Fe(p,l,c){return l===0&&c===p.length?t.fromByteArray(p):t.fromByteArray(p.slice(l,c))}function Se(p,l,c){c=Math.min(p.length,c);let y=[],A=l;for(;A<c;){let T=p[A],k=null,W=T>239?4:T>223?3:T>191?2:1;if(A+W<=c){let ae,re,le,J;switch(W){case 1:T<128&&(k=T);break;case 2:ae=p[A+1],(ae&192)===128&&(J=(T&31)<<6|ae&63,J>127&&(k=J));break;case 3:ae=p[A+1],re=p[A+2],(ae&192)===128&&(re&192)===128&&(J=(T&15)<<12|(ae&63)<<6|re&63,J>2047&&(J<55296||J>57343)&&(k=J));break;case 4:ae=p[A+1],re=p[A+2],le=p[A+3],(ae&192)===128&&(re&192)===128&&(le&192)===128&&(J=(T&15)<<18|(ae&63)<<12|(re&63)<<6|le&63,J>65535&&J<1114112&&(k=J))}}k===null?(k=65533,W=1):k>65535&&(k-=65536,y.push(k>>>10&1023|55296),k=56320|k&1023),y.push(k),A+=W}return Er(y)}let vr=4096;function Er(p){let l=p.length;if(l<=vr)return String.fromCharCode.apply(String,p);let c="",y=0;for(;y<l;)c+=String.fromCharCode.apply(String,p.slice(y,y+=vr));return c}function ls(p,l,c){let y="";c=Math.min(p.length,c);for(let A=l;A<c;++A)y+=String.fromCharCode(p[A]&127);return y}function Og(p,l,c){let y="";c=Math.min(p.length,c);for(let A=l;A<c;++A)y+=String.fromCharCode(p[A]);return y}function Mg(p,l,c){let y=p.length;(!l||l<0)&&(l=0),(!c||c<0||c>y)&&(c=y);let A="";for(let T=l;T<c;++T)A+=Fg[p[T]];return A}function Lg(p,l,c){let y=p.slice(l,c),A="";for(let T=0;T<y.length-1;T+=2)A+=String.fromCharCode(y[T]+y[T+1]*256);return A}s.prototype.slice=function(l,c){let y=this.length;l=~~l,c=c===void 0?y:~~c,l<0?(l+=y,l<0&&(l=0)):l>y&&(l=y),c<0?(c+=y,c<0&&(c=0)):c>y&&(c=y),c<l&&(c=l);let A=this.subarray(l,c);return Object.setPrototypeOf(A,s.prototype),A};function be(p,l,c){if(p%1!==0||p<0)throw new RangeError("offset is not uint");if(p+l>c)throw new RangeError("Trying to access beyond buffer length")}s.prototype.readUintLE=s.prototype.readUIntLE=function(l,c,y){l=l>>>0,c=c>>>0,y||be(l,c,this.length);let A=this[l],T=1,k=0;for(;++k<c&&(T*=256);)A+=this[l+k]*T;return A},s.prototype.readUintBE=s.prototype.readUIntBE=function(l,c,y){l=l>>>0,c=c>>>0,y||be(l,c,this.length);let A=this[l+--c],T=1;for(;c>0&&(T*=256);)A+=this[l+--c]*T;return A},s.prototype.readUint8=s.prototype.readUInt8=function(l,c){return l=l>>>0,c||be(l,1,this.length),this[l]},s.prototype.readUint16LE=s.prototype.readUInt16LE=function(l,c){return l=l>>>0,c||be(l,2,this.length),this[l]|this[l+1]<<8},s.prototype.readUint16BE=s.prototype.readUInt16BE=function(l,c){return l=l>>>0,c||be(l,2,this.length),this[l]<<8|this[l+1]},s.prototype.readUint32LE=s.prototype.readUInt32LE=function(l,c){return l=l>>>0,c||be(l,4,this.length),(this[l]|this[l+1]<<8|this[l+2]<<16)+this[l+3]*16777216},s.prototype.readUint32BE=s.prototype.readUInt32BE=function(l,c){return l=l>>>0,c||be(l,4,this.length),this[l]*16777216+(this[l+1]<<16|this[l+2]<<8|this[l+3])},s.prototype.readBigUInt64LE=Pt(function(l){l=l>>>0,Ar(l,"offset");let c=this[l],y=this[l+7];(c===void 0||y===void 0)&&si(l,this.length-8);let A=c+this[++l]*2**8+this[++l]*2**16+this[++l]*2**24,T=this[++l]+this[++l]*2**8+this[++l]*2**16+y*2**24;return BigInt(A)+(BigInt(T)<<BigInt(32))}),s.prototype.readBigUInt64BE=Pt(function(l){l=l>>>0,Ar(l,"offset");let c=this[l],y=this[l+7];(c===void 0||y===void 0)&&si(l,this.length-8);let A=c*2**24+this[++l]*2**16+this[++l]*2**8+this[++l],T=this[++l]*2**24+this[++l]*2**16+this[++l]*2**8+y;return(BigInt(A)<<BigInt(32))+BigInt(T)}),s.prototype.readIntLE=function(l,c,y){l=l>>>0,c=c>>>0,y||be(l,c,this.length);let A=this[l],T=1,k=0;for(;++k<c&&(T*=256);)A+=this[l+k]*T;return T*=128,A>=T&&(A-=Math.pow(2,8*c)),A},s.prototype.readIntBE=function(l,c,y){l=l>>>0,c=c>>>0,y||be(l,c,this.length);let A=c,T=1,k=this[l+--A];for(;A>0&&(T*=256);)k+=this[l+--A]*T;return T*=128,k>=T&&(k-=Math.pow(2,8*c)),k},s.prototype.readInt8=function(l,c){return l=l>>>0,c||be(l,1,this.length),this[l]&128?(255-this[l]+1)*-1:this[l]},s.prototype.readInt16LE=function(l,c){l=l>>>0,c||be(l,2,this.length);let y=this[l]|this[l+1]<<8;return y&32768?y|4294901760:y},s.prototype.readInt16BE=function(l,c){l=l>>>0,c||be(l,2,this.length);let y=this[l+1]|this[l]<<8;return y&32768?y|4294901760:y},s.prototype.readInt32LE=function(l,c){return l=l>>>0,c||be(l,4,this.length),this[l]|this[l+1]<<8|this[l+2]<<16|this[l+3]<<24},s.prototype.readInt32BE=function(l,c){return l=l>>>0,c||be(l,4,this.length),this[l]<<24|this[l+1]<<16|this[l+2]<<8|this[l+3]},s.prototype.readBigInt64LE=Pt(function(l){l=l>>>0,Ar(l,"offset");let c=this[l],y=this[l+7];(c===void 0||y===void 0)&&si(l,this.length-8);let A=this[l+4]+this[l+5]*2**8+this[l+6]*2**16+(y<<24);return(BigInt(A)<<BigInt(32))+BigInt(c+this[++l]*2**8+this[++l]*2**16+this[++l]*2**24)}),s.prototype.readBigInt64BE=Pt(function(l){l=l>>>0,Ar(l,"offset");let c=this[l],y=this[l+7];(c===void 0||y===void 0)&&si(l,this.length-8);let A=(c<<24)+this[++l]*2**16+this[++l]*2**8+this[++l];return(BigInt(A)<<BigInt(32))+BigInt(this[++l]*2**24+this[++l]*2**16+this[++l]*2**8+y)}),s.prototype.readFloatLE=function(l,c){return l=l>>>0,c||be(l,4,this.length),e.read(this,l,!0,23,4)},s.prototype.readFloatBE=function(l,c){return l=l>>>0,c||be(l,4,this.length),e.read(this,l,!1,23,4)},s.prototype.readDoubleLE=function(l,c){return l=l>>>0,c||be(l,8,this.length),e.read(this,l,!0,52,8)},s.prototype.readDoubleBE=function(l,c){return l=l>>>0,c||be(l,8,this.length),e.read(this,l,!1,52,8)};function Ce(p,l,c,y,A,T){if(!s.isBuffer(p))throw new TypeError('"buffer" argument must be a Buffer instance');if(l>A||l<T)throw new RangeError('"value" argument is out of bounds');if(c+y>p.length)throw new RangeError("Index out of range")}s.prototype.writeUintLE=s.prototype.writeUIntLE=function(l,c,y,A){if(l=+l,c=c>>>0,y=y>>>0,!A){let W=Math.pow(2,8*y)-1;Ce(this,l,c,y,W,0)}let T=1,k=0;for(this[c]=l&255;++k<y&&(T*=256);)this[c+k]=l/T&255;return c+y},s.prototype.writeUintBE=s.prototype.writeUIntBE=function(l,c,y,A){if(l=+l,c=c>>>0,y=y>>>0,!A){let W=Math.pow(2,8*y)-1;Ce(this,l,c,y,W,0)}let T=y-1,k=1;for(this[c+T]=l&255;--T>=0&&(k*=256);)this[c+T]=l/k&255;return c+y},s.prototype.writeUint8=s.prototype.writeUInt8=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,1,255,0),this[c]=l&255,c+1},s.prototype.writeUint16LE=s.prototype.writeUInt16LE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,2,65535,0),this[c]=l&255,this[c+1]=l>>>8,c+2},s.prototype.writeUint16BE=s.prototype.writeUInt16BE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,2,65535,0),this[c]=l>>>8,this[c+1]=l&255,c+2},s.prototype.writeUint32LE=s.prototype.writeUInt32LE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,4,4294967295,0),this[c+3]=l>>>24,this[c+2]=l>>>16,this[c+1]=l>>>8,this[c]=l&255,c+4},s.prototype.writeUint32BE=s.prototype.writeUInt32BE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,4,4294967295,0),this[c]=l>>>24,this[c+1]=l>>>16,this[c+2]=l>>>8,this[c+3]=l&255,c+4};function bl(p,l,c,y,A){El(l,y,A,p,c,7);let T=Number(l&BigInt(4294967295));p[c++]=T,T=T>>8,p[c++]=T,T=T>>8,p[c++]=T,T=T>>8,p[c++]=T;let k=Number(l>>BigInt(32)&BigInt(4294967295));return p[c++]=k,k=k>>8,p[c++]=k,k=k>>8,p[c++]=k,k=k>>8,p[c++]=k,c}function yl(p,l,c,y,A){El(l,y,A,p,c,7);let T=Number(l&BigInt(4294967295));p[c+7]=T,T=T>>8,p[c+6]=T,T=T>>8,p[c+5]=T,T=T>>8,p[c+4]=T;let k=Number(l>>BigInt(32)&BigInt(4294967295));return p[c+3]=k,k=k>>8,p[c+2]=k,k=k>>8,p[c+1]=k,k=k>>8,p[c]=k,c+8}s.prototype.writeBigUInt64LE=Pt(function(l,c=0){return bl(this,l,c,BigInt(0),BigInt("0xffffffffffffffff"))}),s.prototype.writeBigUInt64BE=Pt(function(l,c=0){return yl(this,l,c,BigInt(0),BigInt("0xffffffffffffffff"))}),s.prototype.writeIntLE=function(l,c,y,A){if(l=+l,c=c>>>0,!A){let ae=Math.pow(2,8*y-1);Ce(this,l,c,y,ae-1,-ae)}let T=0,k=1,W=0;for(this[c]=l&255;++T<y&&(k*=256);)l<0&&W===0&&this[c+T-1]!==0&&(W=1),this[c+T]=(l/k>>0)-W&255;return c+y},s.prototype.writeIntBE=function(l,c,y,A){if(l=+l,c=c>>>0,!A){let ae=Math.pow(2,8*y-1);Ce(this,l,c,y,ae-1,-ae)}let T=y-1,k=1,W=0;for(this[c+T]=l&255;--T>=0&&(k*=256);)l<0&&W===0&&this[c+T+1]!==0&&(W=1),this[c+T]=(l/k>>0)-W&255;return c+y},s.prototype.writeInt8=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,1,127,-128),l<0&&(l=255+l+1),this[c]=l&255,c+1},s.prototype.writeInt16LE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,2,32767,-32768),this[c]=l&255,this[c+1]=l>>>8,c+2},s.prototype.writeInt16BE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,2,32767,-32768),this[c]=l>>>8,this[c+1]=l&255,c+2},s.prototype.writeInt32LE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,4,2147483647,-2147483648),this[c]=l&255,this[c+1]=l>>>8,this[c+2]=l>>>16,this[c+3]=l>>>24,c+4},s.prototype.writeInt32BE=function(l,c,y){return l=+l,c=c>>>0,y||Ce(this,l,c,4,2147483647,-2147483648),l<0&&(l=4294967295+l+1),this[c]=l>>>24,this[c+1]=l>>>16,this[c+2]=l>>>8,this[c+3]=l&255,c+4},s.prototype.writeBigInt64LE=Pt(function(l,c=0){return bl(this,l,c,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),s.prototype.writeBigInt64BE=Pt(function(l,c=0){return yl(this,l,c,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function wl(p,l,c,y,A,T){if(c+y>p.length)throw new RangeError("Index out of range");if(c<0)throw new RangeError("Index out of range")}function _l(p,l,c,y,A){return l=+l,c=c>>>0,A||wl(p,l,c,4),e.write(p,l,c,y,23,4),c+4}s.prototype.writeFloatLE=function(l,c,y){return _l(this,l,c,!0,y)},s.prototype.writeFloatBE=function(l,c,y){return _l(this,l,c,!1,y)};function ml(p,l,c,y,A){return l=+l,c=c>>>0,A||wl(p,l,c,8),e.write(p,l,c,y,52,8),c+8}s.prototype.writeDoubleLE=function(l,c,y){return ml(this,l,c,!0,y)},s.prototype.writeDoubleBE=function(l,c,y){return ml(this,l,c,!1,y)},s.prototype.copy=function(l,c,y,A){if(!s.isBuffer(l))throw new TypeError("argument should be a Buffer");if(y||(y=0),!A&&A!==0&&(A=this.length),c>=l.length&&(c=l.length),c||(c=0),A>0&&A<y&&(A=y),A===y||l.length===0||this.length===0)return 0;if(c<0)throw new RangeError("targetStart out of bounds");if(y<0||y>=this.length)throw new RangeError("Index out of range");if(A<0)throw new RangeError("sourceEnd out of bounds");A>this.length&&(A=this.length),l.length-c<A-y&&(A=l.length-c+y);let T=A-y;return this===l&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(c,y,A):Uint8Array.prototype.set.call(l,this.subarray(y,A),c),T},s.prototype.fill=function(l,c,y,A){if(typeof l=="string"){if(typeof c=="string"?(A=c,c=0,y=this.length):typeof y=="string"&&(A=y,y=this.length),A!==void 0&&typeof A!="string")throw new TypeError("encoding must be a string");if(typeof A=="string"&&!s.isEncoding(A))throw new TypeError("Unknown encoding: "+A);if(l.length===1){let k=l.charCodeAt(0);(A==="utf8"&&k<128||A==="latin1")&&(l=k)}}else typeof l=="number"?l=l&255:typeof l=="boolean"&&(l=Number(l));if(c<0||this.length<c||this.length<y)throw new RangeError("Out of range index");if(y<=c)return this;c=c>>>0,y=y===void 0?this.length:y>>>0,l||(l=0);let T;if(typeof l=="number")for(T=c;T<y;++T)this[T]=l;else{let k=s.isBuffer(l)?l:s.from(l,A),W=k.length;if(W===0)throw new TypeError('The value "'+l+'" is invalid for argument "value"');for(T=0;T<y-c;++T)this[T+c]=k[T%W]}return this};let Sr={};function us(p,l,c){Sr[p]=class extends c{constructor(){super(),Object.defineProperty(this,"message",{value:l.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${p}]`,this.stack,delete this.name}get code(){return p}set code(A){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:A,writable:!0})}toString(){return`${this.name} [${p}]: ${this.message}`}}}us("ERR_BUFFER_OUT_OF_BOUNDS",function(p){return p?`${p} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),us("ERR_INVALID_ARG_TYPE",function(p,l){return`The "${p}" argument must be of type number. Received type ${typeof l}`},TypeError),us("ERR_OUT_OF_RANGE",function(p,l,c){let y=`The value of "${p}" is out of range.`,A=c;return Number.isInteger(c)&&Math.abs(c)>2**32?A=vl(String(c)):typeof c=="bigint"&&(A=String(c),(c>BigInt(2)**BigInt(32)||c<-(BigInt(2)**BigInt(32)))&&(A=vl(A)),A+="n"),y+=` It must be ${l}. Received ${A}`,y},RangeError);function vl(p){let l="",c=p.length,y=p[0]==="-"?1:0;for(;c>=y+4;c-=3)l=`_${p.slice(c-3,c)}${l}`;return`${p.slice(0,c)}${l}`}function qg(p,l,c){Ar(l,"offset"),(p[l]===void 0||p[l+c]===void 0)&&si(l,p.length-(c+1))}function El(p,l,c,y,A,T){if(p>c||p<l){let k=typeof l=="bigint"?"n":"",W;throw T>3?l===0||l===BigInt(0)?W=`>= 0${k} and < 2${k} ** ${(T+1)*8}${k}`:W=`>= -(2${k} ** ${(T+1)*8-1}${k}) and < 2 ** ${(T+1)*8-1}${k}`:W=`>= ${l}${k} and <= ${c}${k}`,new Sr.ERR_OUT_OF_RANGE("value",W,p)}qg(y,A,T)}function Ar(p,l){if(typeof p!="number")throw new Sr.ERR_INVALID_ARG_TYPE(l,"number",p)}function si(p,l,c){throw Math.floor(p)!==p?(Ar(p,c),new Sr.ERR_OUT_OF_RANGE(c||"offset","an integer",p)):l<0?new Sr.ERR_BUFFER_OUT_OF_BOUNDS:new Sr.ERR_OUT_OF_RANGE(c||"offset",`>= ${c?1:0} and <= ${l}`,p)}let Ug=/[^+/0-9A-Za-z-_]/g;function Ng(p){if(p=p.split("=")[0],p=p.trim().replace(Ug,""),p.length<2)return"";for(;p.length%4!==0;)p=p+"=";return p}function cs(p,l){l=l||1/0;let c,y=p.length,A=null,T=[];for(let k=0;k<y;++k){if(c=p.charCodeAt(k),c>55295&&c<57344){if(!A){if(c>56319){(l-=3)>-1&&T.push(239,191,189);continue}else if(k+1===y){(l-=3)>-1&&T.push(239,191,189);continue}A=c;continue}if(c<56320){(l-=3)>-1&&T.push(239,191,189),A=c;continue}c=(A-55296<<10|c-56320)+65536}else A&&(l-=3)>-1&&T.push(239,191,189);if(A=null,c<128){if((l-=1)<0)break;T.push(c)}else if(c<2048){if((l-=2)<0)break;T.push(c>>6|192,c&63|128)}else if(c<65536){if((l-=3)<0)break;T.push(c>>12|224,c>>6&63|128,c&63|128)}else if(c<1114112){if((l-=4)<0)break;T.push(c>>18|240,c>>12&63|128,c>>6&63|128,c&63|128)}else throw new Error("Invalid code point")}return T}function Dg(p){let l=[];for(let c=0;c<p.length;++c)l.push(p.charCodeAt(c)&255);return l}function jg(p,l){let c,y,A,T=[];for(let k=0;k<p.length&&!((l-=2)<0);++k)c=p.charCodeAt(k),y=c>>8,A=c%256,T.push(A),T.push(y);return T}function Sl(p){return t.toByteArray(Ng(p))}function Ui(p,l,c,y){let A;for(A=0;A<y&&!(A+c>=l.length||A>=p.length);++A)l[A+c]=p[A];return A}function Qe(p,l){return p instanceof l||p!=null&&p.constructor!=null&&p.constructor.name!=null&&p.constructor.name===l.name}function fs(p){return p!==p}let Fg=function(){let p="0123456789abcdef",l=new Array(256);for(let c=0;c<16;++c){let y=c*16;for(let A=0;A<16;++A)l[y+A]=p[c]+p[A]}return l}();function Pt(p){return typeof BigInt>"u"?Wg:p}function Wg(){throw new Error("BigInt not supported")}return Qt}var oi,ju,Fi,Fu,Qt,Wu,Bt,x,Jg,Xg,he=Ae(()=>{_();v();m();oi={},ju=!1;Fi={},Fu=!1;Qt={},Wu=!1;Bt=Yg();Bt.Buffer;Bt.SlowBuffer;Bt.INSPECT_MAX_BYTES;Bt.kMaxLength;x=Bt.Buffer,Jg=Bt.INSPECT_MAX_BYTES,Xg=Bt.kMaxLength});var v=Ae(()=>{he()});var $u=O(ys=>{"use strict";_();v();m();Object.defineProperty(ys,"__esModule",{value:!0});var bs=class{constructor(e){this.aliasToTopic={},this.max=e}put(e,r){return r===0||r>this.max?!1:(this.aliasToTopic[r]=e,this.length=Object.keys(this.aliasToTopic).length,!0)}getTopicByAlias(e){return this.aliasToTopic[e]}clear(){this.aliasToTopic={}}};ys.default=bs});var ie=O((UA,Hu)=>{"use strict";_();v();m();var ws=class extends Error{constructor(e){if(!Array.isArray(e))throw new TypeError(`Expected input to be an Array, got ${typeof e}`);let r="";for(let i=0;i<e.length;i++)r+=`    ${e[i].stack}
`;super(r),this.name="AggregateError",this.errors=e}};Hu.exports={AggregateError:ws,ArrayIsArray(t){return Array.isArray(t)},ArrayPrototypeIncludes(t,e){return t.includes(e)},ArrayPrototypeIndexOf(t,e){return t.indexOf(e)},ArrayPrototypeJoin(t,e){return t.join(e)},ArrayPrototypeMap(t,e){return t.map(e)},ArrayPrototypePop(t,e){return t.pop(e)},ArrayPrototypePush(t,e){return t.push(e)},ArrayPrototypeSlice(t,e,r){return t.slice(e,r)},Error,FunctionPrototypeCall(t,e,...r){return t.call(e,...r)},FunctionPrototypeSymbolHasInstance(t,e){return Function.prototype[Symbol.hasInstance].call(t,e)},MathFloor:Math.floor,Number,NumberIsInteger:Number.isInteger,NumberIsNaN:Number.isNaN,NumberMAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER,NumberMIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER,NumberParseInt:Number.parseInt,ObjectDefineProperties(t,e){return Object.defineProperties(t,e)},ObjectDefineProperty(t,e,r){return Object.defineProperty(t,e,r)},ObjectGetOwnPropertyDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)},ObjectKeys(t){return Object.keys(t)},ObjectSetPrototypeOf(t,e){return Object.setPrototypeOf(t,e)},Promise,PromisePrototypeCatch(t,e){return t.catch(e)},PromisePrototypeThen(t,e,r){return t.then(e,r)},PromiseReject(t){return Promise.reject(t)},PromiseResolve(t){return Promise.resolve(t)},ReflectApply:Reflect.apply,RegExpPrototypeTest(t,e){return t.test(e)},SafeSet:Set,String,StringPrototypeSlice(t,e,r){return t.slice(e,r)},StringPrototypeToLowerCase(t){return t.toLowerCase()},StringPrototypeToUpperCase(t){return t.toUpperCase()},StringPrototypeTrim(t){return t.trim()},Symbol,SymbolFor:Symbol.for,SymbolAsyncIterator:Symbol.asyncIterator,SymbolHasInstance:Symbol.hasInstance,SymbolIterator:Symbol.iterator,SymbolDispose:Symbol.dispose||Symbol("Symbol.dispose"),SymbolAsyncDispose:Symbol.asyncDispose||Symbol("Symbol.asyncDispose"),TypedArrayPrototypeSet(t,e,r){return t.set(e,r)},Boolean,Uint8Array}});var _s=O((HA,Vu)=>{"use strict";_();v();m();Vu.exports={format(t,...e){return t.replace(/%([sdifj])/g,function(...[r,i]){let n=e.shift();return i==="f"?n.toFixed(6):i==="j"?JSON.stringify(n):i==="s"&&typeof n=="object"?`${n.constructor!==Object?n.constructor.name:""} {}`.trim():n.toString()})},inspect(t){switch(typeof t){case"string":if(t.includes("'"))if(t.includes('"')){if(!t.includes("`")&&!t.includes("${"))return`\`${t}\``}else return`"${t}"`;return`'${t}'`;case"number":return isNaN(t)?"NaN":Object.is(t,-0)?String(t):t;case"bigint":return`${String(t)}n`;case"boolean":case"undefined":return String(t);case"object":return"{}"}}}});var me=O((JA,Gu)=>{"use strict";_();v();m();var{format:Zg,inspect:Wi}=_s(),{AggregateError:eb}=ie(),tb=globalThis.AggregateError||eb,rb=Symbol("kIsNodeError"),ib=["string","function","number","object","Function","Object","boolean","bigint","symbol"],nb=/^([A-Z][a-z0-9]*)+$/,sb="__node_internal_",$i={};function Yt(t,e){if(!t)throw new $i.ERR_INTERNAL_ASSERTION(e)}function zu(t){let e="",r=t.length,i=t[0]==="-"?1:0;for(;r>=i+4;r-=3)e=`_${t.slice(r-3,r)}${e}`;return`${t.slice(0,r)}${e}`}function ob(t,e,r){if(typeof e=="function")return Yt(e.length<=r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${e.length}).`),e(...r);let i=(e.match(/%[dfijoOs]/g)||[]).length;return Yt(i===r.length,`Code: ${t}; The provided arguments length (${r.length}) does not match the required ones (${i}).`),r.length===0?e:Zg(e,...r)}function we(t,e,r){r||(r=Error);class i extends r{constructor(...o){super(ob(t,e,o))}toString(){return`${this.name} [${t}]: ${this.message}`}}Object.defineProperties(i.prototype,{name:{value:r.name,writable:!0,enumerable:!1,configurable:!0},toString:{value(){return`${this.name} [${t}]: ${this.message}`},writable:!0,enumerable:!1,configurable:!0}}),i.prototype.code=t,i.prototype[rb]=!0,$i[t]=i}function Ku(t){let e=sb+t.name;return Object.defineProperty(t,"name",{value:e}),t}function ab(t,e){if(t&&e&&t!==e){if(Array.isArray(e.errors))return e.errors.push(t),e;let r=new tb([e,t],e.message);return r.code=e.code,r}return t||e}var ms=class extends Error{constructor(e="The operation was aborted",r=void 0){if(r!==void 0&&typeof r!="object")throw new $i.ERR_INVALID_ARG_TYPE("options","Object",r);super(e,r),this.code="ABORT_ERR",this.name="AbortError"}};we("ERR_ASSERTION","%s",Error);we("ERR_INVALID_ARG_TYPE",(t,e,r)=>{Yt(typeof t=="string","'name' must be a string"),Array.isArray(e)||(e=[e]);let i="The ";t.endsWith(" argument")?i+=`${t} `:i+=`"${t}" ${t.includes(".")?"property":"argument"} `,i+="must be ";let n=[],o=[],s=[];for(let u of e)Yt(typeof u=="string","All expected entries have to be of type string"),ib.includes(u)?n.push(u.toLowerCase()):nb.test(u)?o.push(u):(Yt(u!=="object",'The value "object" should be written as "Object"'),s.push(u));if(o.length>0){let u=n.indexOf("object");u!==-1&&(n.splice(n,u,1),o.push("Object"))}if(n.length>0){switch(n.length){case 1:i+=`of type ${n[0]}`;break;case 2:i+=`one of type ${n[0]} or ${n[1]}`;break;default:{let u=n.pop();i+=`one of type ${n.join(", ")}, or ${u}`}}(o.length>0||s.length>0)&&(i+=" or ")}if(o.length>0){switch(o.length){case 1:i+=`an instance of ${o[0]}`;break;case 2:i+=`an instance of ${o[0]} or ${o[1]}`;break;default:{let u=o.pop();i+=`an instance of ${o.join(", ")}, or ${u}`}}s.length>0&&(i+=" or ")}switch(s.length){case 0:break;case 1:s[0].toLowerCase()!==s[0]&&(i+="an "),i+=`${s[0]}`;break;case 2:i+=`one of ${s[0]} or ${s[1]}`;break;default:{let u=s.pop();i+=`one of ${s.join(", ")}, or ${u}`}}if(r==null)i+=`. Received ${r}`;else if(typeof r=="function"&&r.name)i+=`. Received function ${r.name}`;else if(typeof r=="object"){var a;if((a=r.constructor)!==null&&a!==void 0&&a.name)i+=`. Received an instance of ${r.constructor.name}`;else{let u=Wi(r,{depth:-1});i+=`. Received ${u}`}}else{let u=Wi(r,{colors:!1});u.length>25&&(u=`${u.slice(0,25)}...`),i+=`. Received type ${typeof r} (${u})`}return i},TypeError);we("ERR_INVALID_ARG_VALUE",(t,e,r="is invalid")=>{let i=Wi(e);return i.length>128&&(i=i.slice(0,128)+"..."),`The ${t.includes(".")?"property":"argument"} '${t}' ${r}. Received ${i}`},TypeError);we("ERR_INVALID_RETURN_VALUE",(t,e,r)=>{var i;let n=r!=null&&(i=r.constructor)!==null&&i!==void 0&&i.name?`instance of ${r.constructor.name}`:`type ${typeof r}`;return`Expected ${t} to be returned from the "${e}" function but got ${n}.`},TypeError);we("ERR_MISSING_ARGS",(...t)=>{Yt(t.length>0,"At least one arg needs to be specified");let e,r=t.length;switch(t=(Array.isArray(t)?t:[t]).map(i=>`"${i}"`).join(" or "),r){case 1:e+=`The ${t[0]} argument`;break;case 2:e+=`The ${t[0]} and ${t[1]} arguments`;break;default:{let i=t.pop();e+=`The ${t.join(", ")}, and ${i} arguments`}break}return`${e} must be specified`},TypeError);we("ERR_OUT_OF_RANGE",(t,e,r)=>{Yt(e,'Missing "range" argument');let i;if(Number.isInteger(r)&&Math.abs(r)>2**32)i=zu(String(r));else if(typeof r=="bigint"){i=String(r);let n=BigInt(2)**BigInt(32);(r>n||r<-n)&&(i=zu(i)),i+="n"}else i=Wi(r);return`The value of "${t}" is out of range. It must be ${e}. Received ${i}`},RangeError);we("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error);we("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error);we("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error);we("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error);we("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error);we("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);we("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error);we("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error);we("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error);we("ERR_STREAM_WRITE_AFTER_END","write after end",Error);we("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError);Gu.exports={AbortError:ms,aggregateTwoErrors:Ku(ab),hideStackFrames:Ku,codes:$i}});var Rr=O((nI,Hi)=>{"use strict";_();v();m();var{AbortController:Qu,AbortSignal:lb}=typeof self<"u"?self:typeof window<"u"?window:void 0;Hi.exports=Qu;Hi.exports.AbortSignal=lb;Hi.exports.default=Qu});function Y(){Y.init.call(this)}function Vi(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function sc(t){return t._maxListeners===void 0?Y.defaultMaxListeners:t._maxListeners}function Zu(t,e,r,i){var n,o,s,a;if(Vi(r),(o=t._events)===void 0?(o=t._events=Object.create(null),t._eventsCount=0):(o.newListener!==void 0&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),s=o[e]),s===void 0)s=o[e]=r,++t._eventsCount;else if(typeof s=="function"?s=o[e]=i?[r,s]:[s,r]:i?s.unshift(r):s.push(r),(n=sc(t))>0&&s.length>n&&!s.warned){s.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=e,u.count=s.length,a=u,console&&console.warn&&console.warn(a)}return t}function ub(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function ec(t,e,r){var i={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},n=ub.bind(i);return n.listener=r,i.wrapFn=n,n}function tc(t,e,r){var i=t._events;if(i===void 0)return[];var n=i[e];return n===void 0?[]:typeof n=="function"?r?[n.listener||n]:[n]:r?function(o){for(var s=new Array(o.length),a=0;a<s.length;++a)s[a]=o[a].listener||o[a];return s}(n):oc(n,n.length)}function rc(t){var e=this._events;if(e!==void 0){var r=e[t];if(typeof r=="function")return 1;if(r!==void 0)return r.length}return 0}function oc(t,e){for(var r=new Array(e),i=0;i<e;++i)r[i]=t[i];return r}var ic,nc,Cr,Yu,Ju,Xu,Pe,vs=Ae(()=>{_();v();m();Cr=typeof Reflect=="object"?Reflect:null,Yu=Cr&&typeof Cr.apply=="function"?Cr.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};nc=Cr&&typeof Cr.ownKeys=="function"?Cr.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};Ju=Number.isNaN||function(t){return t!=t};ic=Y,Y.EventEmitter=Y,Y.prototype._events=void 0,Y.prototype._eventsCount=0,Y.prototype._maxListeners=void 0;Xu=10;Object.defineProperty(Y,"defaultMaxListeners",{enumerable:!0,get:function(){return Xu},set:function(t){if(typeof t!="number"||t<0||Ju(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");Xu=t}}),Y.init=function(){this._events!==void 0&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},Y.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||Ju(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},Y.prototype.getMaxListeners=function(){return sc(this)},Y.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i=t==="error",n=this._events;if(n!==void 0)i=i&&n.error===void 0;else if(!i)return!1;if(i){var o;if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var a=n[t];if(a===void 0)return!1;if(typeof a=="function")Yu(a,this,e);else{var u=a.length,f=oc(a,u);for(r=0;r<u;++r)Yu(f[r],this,e)}return!0},Y.prototype.addListener=function(t,e){return Zu(this,t,e,!1)},Y.prototype.on=Y.prototype.addListener,Y.prototype.prependListener=function(t,e){return Zu(this,t,e,!0)},Y.prototype.once=function(t,e){return Vi(e),this.on(t,ec(this,t,e)),this},Y.prototype.prependOnceListener=function(t,e){return Vi(e),this.prependListener(t,ec(this,t,e)),this},Y.prototype.removeListener=function(t,e){var r,i,n,o,s;if(Vi(e),(i=this._events)===void 0)return this;if((r=i[t])===void 0)return this;if(r===e||r.listener===e)--this._eventsCount==0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||e));else if(typeof r!="function"){for(n=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,n=o;break}if(n<0)return this;n===0?r.shift():function(a,u){for(;u+1<a.length;u++)a[u]=a[u+1];a.pop()}(r,n),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,s||e)}return this},Y.prototype.off=Y.prototype.removeListener,Y.prototype.removeAllListeners=function(t){var e,r,i;if((r=this._events)===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount==0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var n,o=Object.keys(r);for(i=0;i<o.length;++i)(n=o[i])!=="removeListener"&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(typeof(e=r[t])=="function")this.removeListener(t,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(t,e[i]);return this},Y.prototype.listeners=function(t){return tc(this,t,!0)},Y.prototype.rawListeners=function(t){return tc(this,t,!1)},Y.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):rc.call(t,e)},Y.prototype.listenerCount=rc,Y.prototype.eventNames=function(){return this._eventsCount>0?nc(this._events):[]};Pe=ic;Pe.EventEmitter;Pe.defaultMaxListeners;Pe.init;Pe.listenerCount;Pe.EventEmitter;Pe.defaultMaxListeners;Pe.init;Pe.listenerCount});var xt={};Ir(xt,{EventEmitter:()=>cb,default:()=>Pe,defaultMaxListeners:()=>fb,init:()=>hb,listenerCount:()=>db,on:()=>pb,once:()=>gb});var cb,fb,hb,db,pb,gb,Ot=Ae(()=>{_();v();m();vs();vs();Pe.once=function(t,e){return new Promise((r,i)=>{function n(...s){o!==void 0&&t.removeListener("error",o),r(s)}let o;e!=="error"&&(o=s=>{t.removeListener(name,n),i(s)},t.once("error",o)),t.once(e,n)})};Pe.on=function(t,e){let r=[],i=[],n=null,o=!1,s={async next(){let f=r.shift();if(f)return createIterResult(f,!1);if(n){let d=Promise.reject(n);return n=null,d}return o?createIterResult(void 0,!0):new Promise((d,h)=>i.push({resolve:d,reject:h}))},async return(){t.removeListener(e,a),t.removeListener("error",u),o=!0;for(let f of i)f.resolve(createIterResult(void 0,!0));return createIterResult(void 0,!0)},throw(f){n=f,t.removeListener(e,a),t.removeListener("error",u)},[Symbol.asyncIterator](){return this}};return t.on(e,a),t.on("error",u),s;function a(...f){let d=i.shift();d?d.resolve(createIterResult(f,!1)):r.push(f)}function u(f){o=!0;let d=i.shift();d?d.reject(f):n=f,s.return()}};({EventEmitter:cb,defaultMaxListeners:fb,init:hb,listenerCount:db,on:pb,once:gb}=Pe)});var Ie=O((TI,Ss)=>{"use strict";_();v();m();var bb=(he(),Q(ye)),{format:yb,inspect:wb}=_s(),{codes:{ERR_INVALID_ARG_TYPE:Es}}=me(),{kResistStopPropagation:_b,AggregateError:mb,SymbolDispose:vb}=ie(),Eb=globalThis.AbortSignal||Rr().AbortSignal,Sb=globalThis.AbortController||Rr().AbortController,Ab=Object.getPrototypeOf(async function(){}).constructor,ac=globalThis.Blob||bb.Blob,Ib=typeof ac<"u"?function(e){return e instanceof ac}:function(e){return!1},lc=(t,e)=>{if(t!==void 0&&(t===null||typeof t!="object"||!("aborted"in t)))throw new Es(e,"AbortSignal",t)},Tb=(t,e)=>{if(typeof t!="function")throw new Es(e,"Function",t)};Ss.exports={AggregateError:mb,kEmptyObject:Object.freeze({}),once(t){let e=!1;return function(...r){e||(e=!0,t.apply(this,r))}},createDeferredPromise:function(){let t,e;return{promise:new Promise((i,n)=>{t=i,e=n}),resolve:t,reject:e}},promisify(t){return new Promise((e,r)=>{t((i,...n)=>i?r(i):e(...n))})},debuglog(){return function(){}},format:yb,inspect:wb,types:{isAsyncFunction(t){return t instanceof Ab},isArrayBufferView(t){return ArrayBuffer.isView(t)}},isBlob:Ib,deprecate(t,e){return t},addAbortListener:(Ot(),Q(xt)).addAbortListener||function(e,r){if(e===void 0)throw new Es("signal","AbortSignal",e);lc(e,"signal"),Tb(r,"listener");let i;return e.aborted?queueMicrotask(()=>r()):(e.addEventListener("abort",r,{__proto__:null,once:!0,[_b]:!0}),i=()=>{e.removeEventListener("abort",r)}),{__proto__:null,[vb](){var n;(n=i)===null||n===void 0||n()}}},AbortSignalAny:Eb.any||function(e){if(e.length===1)return e[0];let r=new Sb,i=()=>r.abort();return e.forEach(n=>{lc(n,"signals"),n.addEventListener("abort",i,{once:!0})}),r.signal.addEventListener("abort",()=>{e.forEach(n=>n.removeEventListener("abort",i))},{once:!0}),r.signal}};Ss.exports.promisify.custom=Symbol.for("nodejs.util.promisify.custom")});var kr=O((OI,wc)=>{"use strict";_();v();m();var{ArrayIsArray:Is,ArrayPrototypeIncludes:hc,ArrayPrototypeJoin:dc,ArrayPrototypeMap:Rb,NumberIsInteger:Ts,NumberIsNaN:Cb,NumberMAX_SAFE_INTEGER:Pb,NumberMIN_SAFE_INTEGER:kb,NumberParseInt:Bb,ObjectPrototypeHasOwnProperty:xb,RegExpPrototypeExec:pc,String:Ob,StringPrototypeToUpperCase:Mb,StringPrototypeTrim:Lb}=ie(),{hideStackFrames:Me,codes:{ERR_SOCKET_BAD_PORT:qb,ERR_INVALID_ARG_TYPE:ve,ERR_INVALID_ARG_VALUE:Pr,ERR_OUT_OF_RANGE:Jt,ERR_UNKNOWN_SIGNAL:uc}}=me(),{normalizeEncoding:Ub}=Ie(),{isAsyncFunction:Nb,isArrayBufferView:Db}=Ie().types,cc={};function jb(t){return t===(t|0)}function Fb(t){return t===t>>>0}var Wb=/^[0-7]+$/,$b="must be a 32-bit unsigned integer or an octal string";function Hb(t,e,r){if(typeof t>"u"&&(t=r),typeof t=="string"){if(pc(Wb,t)===null)throw new Pr(e,t,$b);t=Bb(t,8)}return gc(t,e),t}var Vb=Me((t,e,r=kb,i=Pb)=>{if(typeof t!="number")throw new ve(e,"number",t);if(!Ts(t))throw new Jt(e,"an integer",t);if(t<r||t>i)throw new Jt(e,`>= ${r} && <= ${i}`,t)}),zb=Me((t,e,r=-2147483648,i=2147483647)=>{if(typeof t!="number")throw new ve(e,"number",t);if(!Ts(t))throw new Jt(e,"an integer",t);if(t<r||t>i)throw new Jt(e,`>= ${r} && <= ${i}`,t)}),gc=Me((t,e,r=!1)=>{if(typeof t!="number")throw new ve(e,"number",t);if(!Ts(t))throw new Jt(e,"an integer",t);let i=r?1:0,n=4294967295;if(t<i||t>n)throw new Jt(e,`>= ${i} && <= ${n}`,t)});function Rs(t,e){if(typeof t!="string")throw new ve(e,"string",t)}function Kb(t,e,r=void 0,i){if(typeof t!="number")throw new ve(e,"number",t);if(r!=null&&t<r||i!=null&&t>i||(r!=null||i!=null)&&Cb(t))throw new Jt(e,`${r!=null?`>= ${r}`:""}${r!=null&&i!=null?" && ":""}${i!=null?`<= ${i}`:""}`,t)}var Gb=Me((t,e,r)=>{if(!hc(r,t)){let n="must be one of: "+dc(Rb(r,o=>typeof o=="string"?`'${o}'`:Ob(o)),", ");throw new Pr(e,t,n)}});function bc(t,e){if(typeof t!="boolean")throw new ve(e,"boolean",t)}function As(t,e,r){return t==null||!xb(t,e)?r:t[e]}var Qb=Me((t,e,r=null)=>{let i=As(r,"allowArray",!1),n=As(r,"allowFunction",!1);if(!As(r,"nullable",!1)&&t===null||!i&&Is(t)||typeof t!="object"&&(!n||typeof t!="function"))throw new ve(e,"Object",t)}),Yb=Me((t,e)=>{if(t!=null&&typeof t!="object"&&typeof t!="function")throw new ve(e,"a dictionary",t)}),zi=Me((t,e,r=0)=>{if(!Is(t))throw new ve(e,"Array",t);if(t.length<r){let i=`must be longer than ${r}`;throw new Pr(e,t,i)}});function Jb(t,e){zi(t,e);for(let r=0;r<t.length;r++)Rs(t[r],`${e}[${r}]`)}function Xb(t,e){zi(t,e);for(let r=0;r<t.length;r++)bc(t[r],`${e}[${r}]`)}function Zb(t,e){zi(t,e);for(let r=0;r<t.length;r++){let i=t[r],n=`${e}[${r}]`;if(i==null)throw new ve(n,"AbortSignal",i);yc(i,n)}}function ey(t,e="signal"){if(Rs(t,e),cc[t]===void 0)throw cc[Mb(t)]!==void 0?new uc(t+" (signals must use all capital letters)"):new uc(t)}var ty=Me((t,e="buffer")=>{if(!Db(t))throw new ve(e,["Buffer","TypedArray","DataView"],t)});function ry(t,e){let r=Ub(e),i=t.length;if(r==="hex"&&i%2!==0)throw new Pr("encoding",e,`is invalid for data of length ${i}`)}function iy(t,e="Port",r=!0){if(typeof t!="number"&&typeof t!="string"||typeof t=="string"&&Lb(t).length===0||+t!==+t>>>0||t>65535||t===0&&!r)throw new qb(e,t,r);return t|0}var yc=Me((t,e)=>{if(t!==void 0&&(t===null||typeof t!="object"||!("aborted"in t)))throw new ve(e,"AbortSignal",t)}),ny=Me((t,e)=>{if(typeof t!="function")throw new ve(e,"Function",t)}),sy=Me((t,e)=>{if(typeof t!="function"||Nb(t))throw new ve(e,"Function",t)}),oy=Me((t,e)=>{if(t!==void 0)throw new ve(e,"undefined",t)});function ay(t,e,r){if(!hc(r,t))throw new ve(e,`('${dc(r,"|")}')`,t)}var ly=/^(?:<[^>]*>)(?:\s*;\s*[^;"\s]+(?:=(")?[^;"\s]*\1)?)*$/;function fc(t,e){if(typeof t>"u"||!pc(ly,t))throw new Pr(e,t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}function uy(t){if(typeof t=="string")return fc(t,"hints"),t;if(Is(t)){let e=t.length,r="";if(e===0)return r;for(let i=0;i<e;i++){let n=t[i];fc(n,"hints"),r+=n,i!==e-1&&(r+=", ")}return r}throw new Pr("hints",t,'must be an array or string of format "</styles.css>; rel=preload; as=style"')}wc.exports={isInt32:jb,isUint32:Fb,parseFileMode:Hb,validateArray:zi,validateStringArray:Jb,validateBooleanArray:Xb,validateAbortSignalArray:Zb,validateBoolean:bc,validateBuffer:ty,validateDictionary:Yb,validateEncoding:ry,validateFunction:ny,validateInt32:zb,validateInteger:Vb,validateNumber:Kb,validateObject:Qb,validateOneOf:Gb,validatePlainFunction:sy,validatePort:iy,validateSignalName:ey,validateString:Rs,validateUint32:gc,validateUndefined:oy,validateUnion:ay,validateAbortSignal:yc,validateLinkHeaderValue:uy}});var Mt=O((jI,Ec)=>{_();v();m();var ue=Ec.exports={},Ye,Je;function Cs(){throw new Error("setTimeout has not been defined")}function Ps(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?Ye=setTimeout:Ye=Cs}catch{Ye=Cs}try{typeof clearTimeout=="function"?Je=clearTimeout:Je=Ps}catch{Je=Ps}})();function _c(t){if(Ye===setTimeout)return setTimeout(t,0);if((Ye===Cs||!Ye)&&setTimeout)return Ye=setTimeout,setTimeout(t,0);try{return Ye(t,0)}catch{try{return Ye.call(null,t,0)}catch{return Ye.call(this,t,0)}}}function cy(t){if(Je===clearTimeout)return clearTimeout(t);if((Je===Ps||!Je)&&clearTimeout)return Je=clearTimeout,clearTimeout(t);try{return Je(t)}catch{try{return Je.call(null,t)}catch{return Je.call(this,t)}}}var yt=[],Br=!1,Xt,Ki=-1;function fy(){!Br||!Xt||(Br=!1,Xt.length?yt=Xt.concat(yt):Ki=-1,yt.length&&mc())}function mc(){if(!Br){var t=_c(fy);Br=!0;for(var e=yt.length;e;){for(Xt=yt,yt=[];++Ki<e;)Xt&&Xt[Ki].run();Ki=-1,e=yt.length}Xt=null,Br=!1,cy(t)}}ue.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];yt.push(new vc(t,e)),yt.length===1&&!Br&&_c(mc)};function vc(t,e){this.fun=t,this.array=e}vc.prototype.run=function(){this.fun.apply(null,this.array)};ue.title="browser";ue.browser=!0;ue.env={};ue.argv=[];ue.version="";ue.versions={};function wt(){}ue.on=wt;ue.addListener=wt;ue.once=wt;ue.off=wt;ue.removeListener=wt;ue.removeAllListeners=wt;ue.emit=wt;ue.prependListener=wt;ue.prependOnceListener=wt;ue.listeners=function(t){return[]};ue.binding=function(t){throw new Error("process.binding is not supported")};ue.cwd=function(){return"/"};ue.chdir=function(t){throw new Error("process.chdir is not supported")};ue.umask=function(){return 0}});var Ze=O((KI,Uc)=>{"use strict";_();v();m();var{SymbolAsyncIterator:Sc,SymbolIterator:Ac,SymbolFor:Zt}=ie(),Ic=Zt("nodejs.stream.destroyed"),Tc=Zt("nodejs.stream.errored"),ks=Zt("nodejs.stream.readable"),Bs=Zt("nodejs.stream.writable"),Rc=Zt("nodejs.stream.disturbed"),hy=Zt("nodejs.webstream.isClosedPromise"),dy=Zt("nodejs.webstream.controllerErrorFunction");function Gi(t,e=!1){var r;return!!(t&&typeof t.pipe=="function"&&typeof t.on=="function"&&(!e||typeof t.pause=="function"&&typeof t.resume=="function")&&(!t._writableState||((r=t._readableState)===null||r===void 0?void 0:r.readable)!==!1)&&(!t._writableState||t._readableState))}function Qi(t){var e;return!!(t&&typeof t.write=="function"&&typeof t.on=="function"&&(!t._readableState||((e=t._writableState)===null||e===void 0?void 0:e.writable)!==!1))}function py(t){return!!(t&&typeof t.pipe=="function"&&t._readableState&&typeof t.on=="function"&&typeof t.write=="function")}function Xe(t){return t&&(t._readableState||t._writableState||typeof t.write=="function"&&typeof t.on=="function"||typeof t.pipe=="function"&&typeof t.on=="function")}function Cc(t){return!!(t&&!Xe(t)&&typeof t.pipeThrough=="function"&&typeof t.getReader=="function"&&typeof t.cancel=="function")}function Pc(t){return!!(t&&!Xe(t)&&typeof t.getWriter=="function"&&typeof t.abort=="function")}function kc(t){return!!(t&&!Xe(t)&&typeof t.readable=="object"&&typeof t.writable=="object")}function gy(t){return Cc(t)||Pc(t)||kc(t)}function by(t,e){return t==null?!1:e===!0?typeof t[Sc]=="function":e===!1?typeof t[Ac]=="function":typeof t[Sc]=="function"||typeof t[Ac]=="function"}function Yi(t){if(!Xe(t))return null;let e=t._writableState,r=t._readableState,i=e||r;return!!(t.destroyed||t[Ic]||i!=null&&i.destroyed)}function Bc(t){if(!Qi(t))return null;if(t.writableEnded===!0)return!0;let e=t._writableState;return e!=null&&e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function yy(t,e){if(!Qi(t))return null;if(t.writableFinished===!0)return!0;let r=t._writableState;return r!=null&&r.errored?!1:typeof r?.finished!="boolean"?null:!!(r.finished||e===!1&&r.ended===!0&&r.length===0)}function wy(t){if(!Gi(t))return null;if(t.readableEnded===!0)return!0;let e=t._readableState;return!e||e.errored?!1:typeof e?.ended!="boolean"?null:e.ended}function xc(t,e){if(!Gi(t))return null;let r=t._readableState;return r!=null&&r.errored?!1:typeof r?.endEmitted!="boolean"?null:!!(r.endEmitted||e===!1&&r.ended===!0&&r.length===0)}function Oc(t){return t&&t[ks]!=null?t[ks]:typeof t?.readable!="boolean"?null:Yi(t)?!1:Gi(t)&&t.readable&&!xc(t)}function Mc(t){return t&&t[Bs]!=null?t[Bs]:typeof t?.writable!="boolean"?null:Yi(t)?!1:Qi(t)&&t.writable&&!Bc(t)}function _y(t,e){return Xe(t)?Yi(t)?!0:!(e?.readable!==!1&&Oc(t)||e?.writable!==!1&&Mc(t)):null}function my(t){var e,r;return Xe(t)?t.writableErrored?t.writableErrored:(e=(r=t._writableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function vy(t){var e,r;return Xe(t)?t.readableErrored?t.readableErrored:(e=(r=t._readableState)===null||r===void 0?void 0:r.errored)!==null&&e!==void 0?e:null:null}function Ey(t){if(!Xe(t))return null;if(typeof t.closed=="boolean")return t.closed;let e=t._writableState,r=t._readableState;return typeof e?.closed=="boolean"||typeof r?.closed=="boolean"?e?.closed||r?.closed:typeof t._closed=="boolean"&&Lc(t)?t._closed:null}function Lc(t){return typeof t._closed=="boolean"&&typeof t._defaultKeepAlive=="boolean"&&typeof t._removedConnection=="boolean"&&typeof t._removedContLen=="boolean"}function qc(t){return typeof t._sent100=="boolean"&&Lc(t)}function Sy(t){var e;return typeof t._consuming=="boolean"&&typeof t._dumped=="boolean"&&((e=t.req)===null||e===void 0?void 0:e.upgradeOrConnect)===void 0}function Ay(t){if(!Xe(t))return null;let e=t._writableState,r=t._readableState,i=e||r;return!i&&qc(t)||!!(i&&i.autoDestroy&&i.emitClose&&i.closed===!1)}function Iy(t){var e;return!!(t&&((e=t[Rc])!==null&&e!==void 0?e:t.readableDidRead||t.readableAborted))}function Ty(t){var e,r,i,n,o,s,a,u,f,d;return!!(t&&((e=(r=(i=(n=(o=(s=t[Tc])!==null&&s!==void 0?s:t.readableErrored)!==null&&o!==void 0?o:t.writableErrored)!==null&&n!==void 0?n:(a=t._readableState)===null||a===void 0?void 0:a.errorEmitted)!==null&&i!==void 0?i:(u=t._writableState)===null||u===void 0?void 0:u.errorEmitted)!==null&&r!==void 0?r:(f=t._readableState)===null||f===void 0?void 0:f.errored)!==null&&e!==void 0?e:!((d=t._writableState)===null||d===void 0)&&d.errored))}Uc.exports={isDestroyed:Yi,kIsDestroyed:Ic,isDisturbed:Iy,kIsDisturbed:Rc,isErrored:Ty,kIsErrored:Tc,isReadable:Oc,kIsReadable:ks,kIsClosedPromise:hy,kControllerErrorFunction:dy,kIsWritable:Bs,isClosed:Ey,isDuplexNodeStream:py,isFinished:_y,isIterable:by,isReadableNodeStream:Gi,isReadableStream:Cc,isReadableEnded:wy,isReadableFinished:xc,isReadableErrored:vy,isNodeStream:Xe,isWebStream:gy,isWritable:Mc,isWritableNodeStream:Qi,isWritableStream:Pc,isWritableEnded:Bc,isWritableFinished:yy,isWritableErrored:my,isServerRequest:Sy,isServerResponse:qc,willEmitClose:Ay,isTransformStream:kc}});var _t=O((eT,qs)=>{"use strict";_();v();m();var Lt=Mt(),{AbortError:zc,codes:Ry}=me(),{ERR_INVALID_ARG_TYPE:Cy,ERR_STREAM_PREMATURE_CLOSE:Nc}=Ry,{kEmptyObject:Os,once:Ms}=Ie(),{validateAbortSignal:Py,validateFunction:ky,validateObject:By,validateBoolean:xy}=kr(),{Promise:Oy,PromisePrototypeThen:My,SymbolDispose:Kc}=ie(),{isClosed:Ly,isReadable:Dc,isReadableNodeStream:xs,isReadableStream:qy,isReadableFinished:jc,isReadableErrored:Fc,isWritable:Wc,isWritableNodeStream:$c,isWritableStream:Uy,isWritableFinished:Hc,isWritableErrored:Vc,isNodeStream:Ny,willEmitClose:Dy,kIsClosedPromise:jy}=Ze(),xr;function Fy(t){return t.setHeader&&typeof t.abort=="function"}var Ls=()=>{};function Gc(t,e,r){var i,n;if(arguments.length===2?(r=e,e=Os):e==null?e=Os:By(e,"options"),ky(r,"callback"),Py(e.signal,"options.signal"),r=Ms(r),qy(t)||Uy(t))return Wy(t,e,r);if(!Ny(t))throw new Cy("stream",["ReadableStream","WritableStream","Stream"],t);let o=(i=e.readable)!==null&&i!==void 0?i:xs(t),s=(n=e.writable)!==null&&n!==void 0?n:$c(t),a=t._writableState,u=t._readableState,f=()=>{t.writable||g()},d=Dy(t)&&xs(t)===o&&$c(t)===s,h=Hc(t,!1),g=()=>{h=!0,t.destroyed&&(d=!1),!(d&&(!t.readable||o))&&(!o||b)&&r.call(t)},b=jc(t,!1),E=()=>{b=!0,t.destroyed&&(d=!1),!(d&&(!t.writable||s))&&(!s||h)&&r.call(t)},w=q=>{r.call(t,q)},S=Ly(t),I=()=>{S=!0;let q=Vc(t)||Fc(t);if(q&&typeof q!="boolean")return r.call(t,q);if(o&&!b&&xs(t,!0)&&!jc(t,!1))return r.call(t,new Nc);if(s&&!h&&!Hc(t,!1))return r.call(t,new Nc);r.call(t)},P=()=>{S=!0;let q=Vc(t)||Fc(t);if(q&&typeof q!="boolean")return r.call(t,q);r.call(t)},C=()=>{t.req.on("finish",g)};Fy(t)?(t.on("complete",g),d||t.on("abort",I),t.req?C():t.on("request",C)):s&&!a&&(t.on("end",f),t.on("close",f)),!d&&typeof t.aborted=="boolean"&&t.on("aborted",I),t.on("end",E),t.on("finish",g),e.error!==!1&&t.on("error",w),t.on("close",I),S?Lt.nextTick(I):a!=null&&a.errorEmitted||u!=null&&u.errorEmitted?d||Lt.nextTick(P):(!o&&(!d||Dc(t))&&(h||Wc(t)===!1)||!s&&(!d||Wc(t))&&(b||Dc(t)===!1)||u&&t.req&&t.aborted)&&Lt.nextTick(P);let M=()=>{r=Ls,t.removeListener("aborted",I),t.removeListener("complete",g),t.removeListener("abort",I),t.removeListener("request",C),t.req&&t.req.removeListener("finish",g),t.removeListener("end",f),t.removeListener("close",f),t.removeListener("finish",g),t.removeListener("end",E),t.removeListener("error",w),t.removeListener("close",I)};if(e.signal&&!S){let q=()=>{let z=r;M(),z.call(t,new zc(void 0,{cause:e.signal.reason}))};if(e.signal.aborted)Lt.nextTick(q);else{xr=xr||Ie().addAbortListener;let z=xr(e.signal,q),j=r;r=Ms((...G)=>{z[Kc](),j.apply(t,G)})}}return M}function Wy(t,e,r){let i=!1,n=Ls;if(e.signal)if(n=()=>{i=!0,r.call(t,new zc(void 0,{cause:e.signal.reason}))},e.signal.aborted)Lt.nextTick(n);else{xr=xr||Ie().addAbortListener;let s=xr(e.signal,n),a=r;r=Ms((...u)=>{s[Kc](),a.apply(t,u)})}let o=(...s)=>{i||Lt.nextTick(()=>r.apply(t,s))};return My(t[jy].promise,o,o),Ls}function $y(t,e){var r;let i=!1;return e===null&&(e=Os),(r=e)!==null&&r!==void 0&&r.cleanup&&(xy(e.cleanup,"cleanup"),i=e.cleanup),new Oy((n,o)=>{let s=Gc(t,e,a=>{i&&s(),a?o(a):n()})})}qs.exports=Gc;qs.exports.finished=$y});var er=O((aT,rf)=>{"use strict";_();v();m();var et=Mt(),{aggregateTwoErrors:Hy,codes:{ERR_MULTIPLE_CALLBACK:Vy},AbortError:zy}=me(),{Symbol:Jc}=ie(),{kIsDestroyed:Ky,isDestroyed:Gy,isFinished:Qy,isServerRequest:Yy}=Ze(),Xc=Jc("kDestroy"),Us=Jc("kConstruct");function Zc(t,e,r){t&&(t.stack,e&&!e.errored&&(e.errored=t),r&&!r.errored&&(r.errored=t))}function Jy(t,e){let r=this._readableState,i=this._writableState,n=i||r;return i!=null&&i.destroyed||r!=null&&r.destroyed?(typeof e=="function"&&e(),this):(Zc(t,i,r),i&&(i.destroyed=!0),r&&(r.destroyed=!0),n.constructed?Qc(this,t,e):this.once(Xc,function(o){Qc(this,Hy(o,t),e)}),this)}function Qc(t,e,r){let i=!1;function n(o){if(i)return;i=!0;let s=t._readableState,a=t._writableState;Zc(o,a,s),a&&(a.closed=!0),s&&(s.closed=!0),typeof r=="function"&&r(o),o?et.nextTick(Xy,t,o):et.nextTick(ef,t)}try{t._destroy(e||null,n)}catch(o){n(o)}}function Xy(t,e){Ns(t,e),ef(t)}function ef(t){let e=t._readableState,r=t._writableState;r&&(r.closeEmitted=!0),e&&(e.closeEmitted=!0),(r!=null&&r.emitClose||e!=null&&e.emitClose)&&t.emit("close")}function Ns(t,e){let r=t._readableState,i=t._writableState;i!=null&&i.errorEmitted||r!=null&&r.errorEmitted||(i&&(i.errorEmitted=!0),r&&(r.errorEmitted=!0),t.emit("error",e))}function Zy(){let t=this._readableState,e=this._writableState;t&&(t.constructed=!0,t.closed=!1,t.closeEmitted=!1,t.destroyed=!1,t.errored=null,t.errorEmitted=!1,t.reading=!1,t.ended=t.readable===!1,t.endEmitted=t.readable===!1),e&&(e.constructed=!0,e.destroyed=!1,e.closed=!1,e.closeEmitted=!1,e.errored=null,e.errorEmitted=!1,e.finalCalled=!1,e.prefinished=!1,e.ended=e.writable===!1,e.ending=e.writable===!1,e.finished=e.writable===!1)}function Ds(t,e,r){let i=t._readableState,n=t._writableState;if(n!=null&&n.destroyed||i!=null&&i.destroyed)return this;i!=null&&i.autoDestroy||n!=null&&n.autoDestroy?t.destroy(e):e&&(e.stack,n&&!n.errored&&(n.errored=e),i&&!i.errored&&(i.errored=e),r?et.nextTick(Ns,t,e):Ns(t,e))}function ew(t,e){if(typeof t._construct!="function")return;let r=t._readableState,i=t._writableState;r&&(r.constructed=!1),i&&(i.constructed=!1),t.once(Us,e),!(t.listenerCount(Us)>1)&&et.nextTick(tw,t)}function tw(t){let e=!1;function r(i){if(e){Ds(t,i??new Vy);return}e=!0;let n=t._readableState,o=t._writableState,s=o||n;n&&(n.constructed=!0),o&&(o.constructed=!0),s.destroyed?t.emit(Xc,i):i?Ds(t,i,!0):et.nextTick(rw,t)}try{t._construct(i=>{et.nextTick(r,i)})}catch(i){et.nextTick(r,i)}}function rw(t){t.emit(Us)}function Yc(t){return t?.setHeader&&typeof t.abort=="function"}function tf(t){t.emit("close")}function iw(t,e){t.emit("error",e),et.nextTick(tf,t)}function nw(t,e){!t||Gy(t)||(!e&&!Qy(t)&&(e=new zy),Yy(t)?(t.socket=null,t.destroy(e)):Yc(t)?t.abort():Yc(t.req)?t.req.abort():typeof t.destroy=="function"?t.destroy(e):typeof t.close=="function"?t.close():e?et.nextTick(iw,t,e):et.nextTick(tf,t),t.destroyed||(t[Ky]=!0))}rf.exports={construct:ew,destroyer:nw,destroy:Jy,undestroy:Zy,errorOrDestroy:Ds}});var Zi=O((pT,sf)=>{"use strict";_();v();m();var{ArrayIsArray:sw,ObjectSetPrototypeOf:nf}=ie(),{EventEmitter:Ji}=(Ot(),Q(xt));function Xi(t){Ji.call(this,t)}nf(Xi.prototype,Ji.prototype);nf(Xi,Ji);Xi.prototype.pipe=function(t,e){let r=this;function i(d){t.writable&&t.write(d)===!1&&r.pause&&r.pause()}r.on("data",i);function n(){r.readable&&r.resume&&r.resume()}t.on("drain",n),!t._isStdio&&(!e||e.end!==!1)&&(r.on("end",s),r.on("close",a));let o=!1;function s(){o||(o=!0,t.end())}function a(){o||(o=!0,typeof t.destroy=="function"&&t.destroy())}function u(d){f(),Ji.listenerCount(this,"error")===0&&this.emit("error",d)}js(r,"error",u),js(t,"error",u);function f(){r.removeListener("data",i),t.removeListener("drain",n),r.removeListener("end",s),r.removeListener("close",a),r.removeListener("error",u),t.removeListener("error",u),r.removeListener("end",f),r.removeListener("close",f),t.removeListener("close",f)}return r.on("end",f),r.on("close",f),t.on("close",f),t.emit("pipe",r),t};function js(t,e,r){if(typeof t.prependListener=="function")return t.prependListener(e,r);!t._events||!t._events[e]?t.on(e,r):sw(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]}sf.exports={Stream:Xi,prependListener:js}});var ai=O((vT,en)=>{"use strict";_();v();m();var{SymbolDispose:ow}=ie(),{AbortError:of,codes:aw}=me(),{isNodeStream:af,isWebStream:lw,kControllerErrorFunction:uw}=Ze(),cw=_t(),{ERR_INVALID_ARG_TYPE:lf}=aw,Fs,fw=(t,e)=>{if(typeof t!="object"||!("aborted"in t))throw new lf(e,"AbortSignal",t)};en.exports.addAbortSignal=function(e,r){if(fw(e,"signal"),!af(r)&&!lw(r))throw new lf("stream",["ReadableStream","WritableStream","Stream"],r);return en.exports.addAbortSignalNoValidate(e,r)};en.exports.addAbortSignalNoValidate=function(t,e){if(typeof t!="object"||!("aborted"in t))return e;let r=af(e)?()=>{e.destroy(new of(void 0,{cause:t.reason}))}:()=>{e[uw](new of(void 0,{cause:t.reason}))};if(t.aborted)r();else{Fs=Fs||Ie().addAbortListener;let i=Fs(t,r);cw(e,i[ow])}return e}});var ff=O((PT,cf)=>{"use strict";_();v();m();var{StringPrototypeSlice:uf,SymbolIterator:hw,TypedArrayPrototypeSet:tn,Uint8Array:dw}=ie(),{Buffer:Ws}=(he(),Q(ye)),{inspect:pw}=Ie();cf.exports=class{constructor(){this.head=null,this.tail=null,this.length=0}push(e){let r={data:e,next:null};this.length>0?this.tail.next=r:this.head=r,this.tail=r,++this.length}unshift(e){let r={data:e,next:this.head};this.length===0&&(this.tail=r),this.head=r,++this.length}shift(){if(this.length===0)return;let e=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,e}clear(){this.head=this.tail=null,this.length=0}join(e){if(this.length===0)return"";let r=this.head,i=""+r.data;for(;(r=r.next)!==null;)i+=e+r.data;return i}concat(e){if(this.length===0)return Ws.alloc(0);let r=Ws.allocUnsafe(e>>>0),i=this.head,n=0;for(;i;)tn(r,i.data,n),n+=i.data.length,i=i.next;return r}consume(e,r){let i=this.head.data;if(e<i.length){let n=i.slice(0,e);return this.head.data=i.slice(e),n}return e===i.length?this.shift():r?this._getString(e):this._getBuffer(e)}first(){return this.head.data}*[hw](){for(let e=this.head;e;e=e.next)yield e.data}_getString(e){let r="",i=this.head,n=0;do{let o=i.data;if(e>o.length)r+=o,e-=o.length;else{e===o.length?(r+=o,++n,i.next?this.head=i.next:this.head=this.tail=null):(r+=uf(o,0,e),this.head=i,i.data=uf(o,e));break}++n}while((i=i.next)!==null);return this.length-=n,r}_getBuffer(e){let r=Ws.allocUnsafe(e),i=e,n=this.head,o=0;do{let s=n.data;if(e>s.length)tn(r,s,i-e),e-=s.length;else{e===s.length?(tn(r,s,i-e),++o,n.next?this.head=n.next:this.head=this.tail=null):(tn(r,new dw(s.buffer,s.byteOffset,e),i-e),this.head=n,n.data=s.slice(e));break}++o}while((n=n.next)!==null);return this.length-=o,r}[Symbol.for("nodejs.util.inspect.custom")](e,r){return pw(this,{...r,depth:0,customInspect:!1})}}});var li=O((qT,gf)=>{"use strict";_();v();m();var{MathFloor:gw,NumberIsInteger:bw}=ie(),{validateInteger:yw}=kr(),{ERR_INVALID_ARG_VALUE:ww}=me().codes,hf=16*1024,df=16;function _w(t,e,r){return t.highWaterMark!=null?t.highWaterMark:e?t[r]:null}function pf(t){return t?df:hf}function mw(t,e){yw(e,"value",0),t?df=e:hf=e}function vw(t,e,r,i){let n=_w(e,i,r);if(n!=null){if(!bw(n)||n<0){let o=i?`options.${r}`:"options.highWaterMark";throw new ww(o,n)}return gw(n)}return pf(t.objectMode)}gf.exports={getHighWaterMark:vw,getDefaultHighWaterMark:pf,setDefaultHighWaterMark:mw}});var wf=O(($s,yf)=>{_();v();m();var rn=(he(),Q(ye)),tt=rn.Buffer;function bf(t,e){for(var r in t)e[r]=t[r]}tt.from&&tt.alloc&&tt.allocUnsafe&&tt.allocUnsafeSlow?yf.exports=rn:(bf(rn,$s),$s.Buffer=tr);function tr(t,e,r){return tt(t,e,r)}tr.prototype=Object.create(tt.prototype);bf(tt,tr);tr.from=function(t,e,r){if(typeof t=="number")throw new TypeError("Argument must not be a number");return tt(t,e,r)};tr.alloc=function(t,e,r){if(typeof t!="number")throw new TypeError("Argument must be a number");var i=tt(t);return e!==void 0?typeof r=="string"?i.fill(e,r):i.fill(e):i.fill(0),i};tr.allocUnsafe=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return tt(t)};tr.allocUnsafeSlow=function(t){if(typeof t!="number")throw new TypeError("Argument must be a number");return rn.SlowBuffer(t)}});var vf=O(mf=>{"use strict";_();v();m();var Vs=wf().Buffer,_f=Vs.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function Ew(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function Sw(t){var e=Ew(t);if(typeof e!="string"&&(Vs.isEncoding===_f||!_f(t)))throw new Error("Unknown encoding: "+t);return e||t}mf.StringDecoder=ui;function ui(t){this.encoding=Sw(t);var e;switch(this.encoding){case"utf16le":this.text=Pw,this.end=kw,e=4;break;case"utf8":this.fillLast=Tw,e=4;break;case"base64":this.text=Bw,this.end=xw,e=3;break;default:this.write=Ow,this.end=Mw;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=Vs.allocUnsafe(e)}ui.prototype.write=function(t){if(t.length===0)return"";var e,r;if(this.lastNeed){if(e=this.fillLast(t),e===void 0)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""};ui.prototype.end=Cw;ui.prototype.text=Rw;ui.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length};function Hs(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function Aw(t,e,r){var i=e.length-1;if(i<r)return 0;var n=Hs(e[i]);return n>=0?(n>0&&(t.lastNeed=n-1),n):--i<r||n===-2?0:(n=Hs(e[i]),n>=0?(n>0&&(t.lastNeed=n-2),n):--i<r||n===-2?0:(n=Hs(e[i]),n>=0?(n>0&&(n===2?n=0:t.lastNeed=n-3),n):0))}function Iw(t,e,r){if((e[0]&192)!==128)return t.lastNeed=0,"\uFFFD";if(t.lastNeed>1&&e.length>1){if((e[1]&192)!==128)return t.lastNeed=1,"\uFFFD";if(t.lastNeed>2&&e.length>2&&(e[2]&192)!==128)return t.lastNeed=2,"\uFFFD"}}function Tw(t){var e=this.lastTotal-this.lastNeed,r=Iw(this,t,e);if(r!==void 0)return r;if(this.lastNeed<=t.length)return t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,e,0,t.length),this.lastNeed-=t.length}function Rw(t,e){var r=Aw(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var i=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,i),t.toString("utf8",e,i)}function Cw(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"\uFFFD":e}function Pw(t,e){if((t.length-e)%2===0){var r=t.toString("utf16le",e);if(r){var i=r.charCodeAt(r.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function kw(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function Bw(t,e){var r=(t.length-e)%3;return r===0?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,r===1?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function xw(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function Ow(t){return t.toString(this.encoding)}function Mw(t){return t&&t.length?this.write(t):""}});var zs=O((rR,If)=>{"use strict";_();v();m();var Ef=Mt(),{PromisePrototypeThen:Lw,SymbolAsyncIterator:Sf,SymbolIterator:Af}=ie(),{Buffer:qw}=(he(),Q(ye)),{ERR_INVALID_ARG_TYPE:Uw,ERR_STREAM_NULL_VALUES:Nw}=me().codes;function Dw(t,e,r){let i;if(typeof e=="string"||e instanceof qw)return new t({objectMode:!0,...r,read(){this.push(e),this.push(null)}});let n;if(e&&e[Sf])n=!0,i=e[Sf]();else if(e&&e[Af])n=!1,i=e[Af]();else throw new Uw("iterable",["Iterable"],e);let o=new t({objectMode:!0,highWaterMark:1,...r}),s=!1;o._read=function(){s||(s=!0,u())},o._destroy=function(f,d){Lw(a(f),()=>Ef.nextTick(d,f),h=>Ef.nextTick(d,h||f))};async function a(f){let d=f!=null,h=typeof i.throw=="function";if(d&&h){let{value:g,done:b}=await i.throw(f);if(await g,b)return}if(typeof i.return=="function"){let{value:g}=await i.return();await g}}async function u(){for(;;){try{let{value:f,done:d}=n?await i.next():i.next();if(d)o.push(null);else{let h=f&&typeof f.then=="function"?await f:f;if(h===null)throw s=!1,new Nw;if(o.push(h))continue;s=!1}}catch(f){o.destroy(f)}break}}return o}If.exports=Dw});var fi=O((uR,$f)=>{"use strict";_();v();m();var We=Mt(),{ArrayPrototypeIndexOf:jw,NumberIsInteger:Fw,NumberIsNaN:Ww,NumberParseInt:$w,ObjectDefineProperties:eo,ObjectKeys:Hw,ObjectSetPrototypeOf:Cf,Promise:Pf,SafeSet:Vw,SymbolAsyncDispose:zw,SymbolAsyncIterator:Kw,Symbol:Gw}=ie();$f.exports=F;F.ReadableState=an;var{EventEmitter:Qw}=(Ot(),Q(xt)),{Stream:qt,prependListener:Yw}=Zi(),{Buffer:Ks}=(he(),Q(ye)),{addAbortSignal:Jw}=ai(),kf=_t(),H=Ie().debuglog("stream",t=>{H=t}),Xw=ff(),Lr=er(),{getHighWaterMark:Zw,getDefaultHighWaterMark:e_}=li(),{aggregateTwoErrors:Tf,codes:{ERR_INVALID_ARG_TYPE:t_,ERR_METHOD_NOT_IMPLEMENTED:r_,ERR_OUT_OF_RANGE:i_,ERR_STREAM_PUSH_AFTER_EOF:n_,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:s_},AbortError:o_}=me(),{validateObject:a_}=kr(),rr=Gw("kPaused"),{StringDecoder:Bf}=vf(),l_=zs();Cf(F.prototype,qt.prototype);Cf(F,qt);var Gs=()=>{},{errorOrDestroy:Or}=Lr,Mr=1,u_=2,xf=4,ci=8,Of=16,nn=32,sn=64,Mf=128,c_=256,f_=512,h_=1024,Xs=2048,Zs=4096,d_=8192,p_=16384,g_=32768,Lf=65536,b_=1<<17,y_=1<<18;function de(t){return{enumerable:!1,get(){return(this.state&t)!==0},set(e){e?this.state|=t:this.state&=~t}}}eo(an.prototype,{objectMode:de(Mr),ended:de(u_),endEmitted:de(xf),reading:de(ci),constructed:de(Of),sync:de(nn),needReadable:de(sn),emittedReadable:de(Mf),readableListening:de(c_),resumeScheduled:de(f_),errorEmitted:de(h_),emitClose:de(Xs),autoDestroy:de(Zs),destroyed:de(d_),closed:de(p_),closeEmitted:de(g_),multiAwaitDrain:de(Lf),readingMore:de(b_),dataEmitted:de(y_)});function an(t,e,r){typeof r!="boolean"&&(r=e instanceof rt()),this.state=Xs|Zs|Of|nn,t&&t.objectMode&&(this.state|=Mr),r&&t&&t.readableObjectMode&&(this.state|=Mr),this.highWaterMark=t?Zw(this,t,"readableHighWaterMark",r):e_(!1),this.buffer=new Xw,this.length=0,this.pipes=[],this.flowing=null,this[rr]=null,t&&t.emitClose===!1&&(this.state&=~Xs),t&&t.autoDestroy===!1&&(this.state&=~Zs),this.errored=null,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.decoder=null,this.encoding=null,t&&t.encoding&&(this.decoder=new Bf(t.encoding),this.encoding=t.encoding)}function F(t){if(!(this instanceof F))return new F(t);let e=this instanceof rt();this._readableState=new an(t,this,e),t&&(typeof t.read=="function"&&(this._read=t.read),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&!e&&Jw(t.signal,this)),qt.call(this,t),Lr.construct(this,()=>{this._readableState.needReadable&&on(this,this._readableState)})}F.prototype.destroy=Lr.destroy;F.prototype._undestroy=Lr.undestroy;F.prototype._destroy=function(t,e){e(t)};F.prototype[Qw.captureRejectionSymbol]=function(t){this.destroy(t)};F.prototype[zw]=function(){let t;return this.destroyed||(t=this.readableEnded?null:new o_,this.destroy(t)),new Pf((e,r)=>kf(this,i=>i&&i!==t?r(i):e(null)))};F.prototype.push=function(t,e){return qf(this,t,e,!1)};F.prototype.unshift=function(t,e){return qf(this,t,e,!0)};function qf(t,e,r,i){H("readableAddChunk",e);let n=t._readableState,o;if((n.state&Mr)===0&&(typeof e=="string"?(r=r||n.defaultEncoding,n.encoding!==r&&(i&&n.encoding?e=Ks.from(e,r).toString(n.encoding):(e=Ks.from(e,r),r=""))):e instanceof Ks?r="":qt._isUint8Array(e)?(e=qt._uint8ArrayToBuffer(e),r=""):e!=null&&(o=new t_("chunk",["string","Buffer","Uint8Array"],e))),o)Or(t,o);else if(e===null)n.state&=~ci,m_(t,n);else if((n.state&Mr)!==0||e&&e.length>0)if(i)if((n.state&xf)!==0)Or(t,new s_);else{if(n.destroyed||n.errored)return!1;Qs(t,n,e,!0)}else if(n.ended)Or(t,new n_);else{if(n.destroyed||n.errored)return!1;n.state&=~ci,n.decoder&&!r?(e=n.decoder.write(e),n.objectMode||e.length!==0?Qs(t,n,e,!1):on(t,n)):Qs(t,n,e,!1)}else i||(n.state&=~ci,on(t,n));return!n.ended&&(n.length<n.highWaterMark||n.length===0)}function Qs(t,e,r,i){e.flowing&&e.length===0&&!e.sync&&t.listenerCount("data")>0?((e.state&Lf)!==0?e.awaitDrainWriters.clear():e.awaitDrainWriters=null,e.dataEmitted=!0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,i?e.buffer.unshift(r):e.buffer.push(r),(e.state&sn)!==0&&ln(t)),on(t,e)}F.prototype.isPaused=function(){let t=this._readableState;return t[rr]===!0||t.flowing===!1};F.prototype.setEncoding=function(t){let e=new Bf(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;let r=this._readableState.buffer,i="";for(let n of r)i+=e.write(n);return r.clear(),i!==""&&r.push(i),this._readableState.length=i.length,this};var w_=1073741824;function __(t){if(t>w_)throw new i_("size","<= 1GiB",t);return t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++,t}function Rf(t,e){return t<=0||e.length===0&&e.ended?0:(e.state&Mr)!==0?1:Ww(t)?e.flowing&&e.length?e.buffer.first().length:e.length:t<=e.length?t:e.ended?e.length:0}F.prototype.read=function(t){H("read",t),t===void 0?t=NaN:Fw(t)||(t=$w(t,10));let e=this._readableState,r=t;if(t>e.highWaterMark&&(e.highWaterMark=__(t)),t!==0&&(e.state&=~Mf),t===0&&e.needReadable&&((e.highWaterMark!==0?e.length>=e.highWaterMark:e.length>0)||e.ended))return H("read: emitReadable",e.length,e.ended),e.length===0&&e.ended?Ys(this):ln(this),null;if(t=Rf(t,e),t===0&&e.ended)return e.length===0&&Ys(this),null;let i=(e.state&sn)!==0;if(H("need readable",i),(e.length===0||e.length-t<e.highWaterMark)&&(i=!0,H("length less than watermark",i)),e.ended||e.reading||e.destroyed||e.errored||!e.constructed)i=!1,H("reading, ended or constructing",i);else if(i){H("do read"),e.state|=ci|nn,e.length===0&&(e.state|=sn);try{this._read(e.highWaterMark)}catch(o){Or(this,o)}e.state&=~nn,e.reading||(t=Rf(r,e))}let n;return t>0?n=Ff(t,e):n=null,n===null?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.multiAwaitDrain?e.awaitDrainWriters.clear():e.awaitDrainWriters=null),e.length===0&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&Ys(this)),n!==null&&!e.errorEmitted&&!e.closeEmitted&&(e.dataEmitted=!0,this.emit("data",n)),n};function m_(t,e){if(H("onEofChunk"),!e.ended){if(e.decoder){let r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?ln(t):(e.needReadable=!1,e.emittedReadable=!0,Uf(t))}}function ln(t){let e=t._readableState;H("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(H("emitReadable",e.flowing),e.emittedReadable=!0,We.nextTick(Uf,t))}function Uf(t){let e=t._readableState;H("emitReadable_",e.destroyed,e.length,e.ended),!e.destroyed&&!e.errored&&(e.length||e.ended)&&(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,Df(t)}function on(t,e){!e.readingMore&&e.constructed&&(e.readingMore=!0,We.nextTick(v_,t,e))}function v_(t,e){for(;!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&e.length===0);){let r=e.length;if(H("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}F.prototype._read=function(t){throw new r_("_read()")};F.prototype.pipe=function(t,e){let r=this,i=this._readableState;i.pipes.length===1&&(i.multiAwaitDrain||(i.multiAwaitDrain=!0,i.awaitDrainWriters=new Vw(i.awaitDrainWriters?[i.awaitDrainWriters]:[]))),i.pipes.push(t),H("pipe count=%d opts=%j",i.pipes.length,e);let o=(!e||e.end!==!1)&&t!==We.stdout&&t!==We.stderr?a:S;i.endEmitted?We.nextTick(o):r.once("end",o),t.on("unpipe",s);function s(I,P){H("onunpipe"),I===r&&P&&P.hasUnpiped===!1&&(P.hasUnpiped=!0,d())}function a(){H("onend"),t.end()}let u,f=!1;function d(){H("cleanup"),t.removeListener("close",E),t.removeListener("finish",w),u&&t.removeListener("drain",u),t.removeListener("error",b),t.removeListener("unpipe",s),r.removeListener("end",a),r.removeListener("end",S),r.removeListener("data",g),f=!0,u&&i.awaitDrainWriters&&(!t._writableState||t._writableState.needDrain)&&u()}function h(){f||(i.pipes.length===1&&i.pipes[0]===t?(H("false write response, pause",0),i.awaitDrainWriters=t,i.multiAwaitDrain=!1):i.pipes.length>1&&i.pipes.includes(t)&&(H("false write response, pause",i.awaitDrainWriters.size),i.awaitDrainWriters.add(t)),r.pause()),u||(u=E_(r,t),t.on("drain",u))}r.on("data",g);function g(I){H("ondata");let P=t.write(I);H("dest.write",P),P===!1&&h()}function b(I){if(H("onerror",I),S(),t.removeListener("error",b),t.listenerCount("error")===0){let P=t._writableState||t._readableState;P&&!P.errorEmitted?Or(t,I):t.emit("error",I)}}Yw(t,"error",b);function E(){t.removeListener("finish",w),S()}t.once("close",E);function w(){H("onfinish"),t.removeListener("close",E),S()}t.once("finish",w);function S(){H("unpipe"),r.unpipe(t)}return t.emit("pipe",r),t.writableNeedDrain===!0?h():i.flowing||(H("pipe resume"),r.resume()),t};function E_(t,e){return function(){let i=t._readableState;i.awaitDrainWriters===e?(H("pipeOnDrain",1),i.awaitDrainWriters=null):i.multiAwaitDrain&&(H("pipeOnDrain",i.awaitDrainWriters.size),i.awaitDrainWriters.delete(e)),(!i.awaitDrainWriters||i.awaitDrainWriters.size===0)&&t.listenerCount("data")&&t.resume()}}F.prototype.unpipe=function(t){let e=this._readableState,r={hasUnpiped:!1};if(e.pipes.length===0)return this;if(!t){let n=e.pipes;e.pipes=[],this.pause();for(let o=0;o<n.length;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}let i=jw(e.pipes,t);return i===-1?this:(e.pipes.splice(i,1),e.pipes.length===0&&this.pause(),t.emit("unpipe",this,r),this)};F.prototype.on=function(t,e){let r=qt.prototype.on.call(this,t,e),i=this._readableState;return t==="data"?(i.readableListening=this.listenerCount("readable")>0,i.flowing!==!1&&this.resume()):t==="readable"&&!i.endEmitted&&!i.readableListening&&(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,H("on readable",i.length,i.reading),i.length?ln(this):i.reading||We.nextTick(S_,this)),r};F.prototype.addListener=F.prototype.on;F.prototype.removeListener=function(t,e){let r=qt.prototype.removeListener.call(this,t,e);return t==="readable"&&We.nextTick(Nf,this),r};F.prototype.off=F.prototype.removeListener;F.prototype.removeAllListeners=function(t){let e=qt.prototype.removeAllListeners.apply(this,arguments);return(t==="readable"||t===void 0)&&We.nextTick(Nf,this),e};function Nf(t){let e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&e[rr]===!1?e.flowing=!0:t.listenerCount("data")>0?t.resume():e.readableListening||(e.flowing=null)}function S_(t){H("readable nexttick read 0"),t.read(0)}F.prototype.resume=function(){let t=this._readableState;return t.flowing||(H("resume"),t.flowing=!t.readableListening,A_(this,t)),t[rr]=!1,this};function A_(t,e){e.resumeScheduled||(e.resumeScheduled=!0,We.nextTick(I_,t,e))}function I_(t,e){H("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),Df(t),e.flowing&&!e.reading&&t.read(0)}F.prototype.pause=function(){return H("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(H("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState[rr]=!0,this};function Df(t){let e=t._readableState;for(H("flow",e.flowing);e.flowing&&t.read()!==null;);}F.prototype.wrap=function(t){let e=!1;t.on("data",i=>{!this.push(i)&&t.pause&&(e=!0,t.pause())}),t.on("end",()=>{this.push(null)}),t.on("error",i=>{Or(this,i)}),t.on("close",()=>{this.destroy()}),t.on("destroy",()=>{this.destroy()}),this._read=()=>{e&&t.resume&&(e=!1,t.resume())};let r=Hw(t);for(let i=1;i<r.length;i++){let n=r[i];this[n]===void 0&&typeof t[n]=="function"&&(this[n]=t[n].bind(t))}return this};F.prototype[Kw]=function(){return jf(this)};F.prototype.iterator=function(t){return t!==void 0&&a_(t,"options"),jf(this,t)};function jf(t,e){typeof t.read!="function"&&(t=F.wrap(t,{objectMode:!0}));let r=T_(t,e);return r.stream=t,r}async function*T_(t,e){let r=Gs;function i(s){this===t?(r(),r=Gs):r=s}t.on("readable",i);let n,o=kf(t,{writable:!1},s=>{n=s?Tf(n,s):null,r(),r=Gs});try{for(;;){let s=t.destroyed?null:t.read();if(s!==null)yield s;else{if(n)throw n;if(n===null)return;await new Pf(i)}}}catch(s){throw n=Tf(n,s),n}finally{(n||e?.destroyOnReturn!==!1)&&(n===void 0||t._readableState.autoDestroy)?Lr.destroyer(t,null):(t.off("readable",i),o())}}eo(F.prototype,{readable:{__proto__:null,get(){let t=this._readableState;return!!t&&t.readable!==!1&&!t.destroyed&&!t.errorEmitted&&!t.endEmitted},set(t){this._readableState&&(this._readableState.readable=!!t)}},readableDidRead:{__proto__:null,enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._readableState.readable!==!1&&(this._readableState.destroyed||this._readableState.errored)&&!this._readableState.endEmitted)}},readableHighWaterMark:{__proto__:null,enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{__proto__:null,enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{__proto__:null,enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}},readableLength:{__proto__:null,enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.objectMode:!1}},readableEncoding:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},errored:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.errored:null}},closed:{__proto__:null,get(){return this._readableState?this._readableState.closed:!1}},destroyed:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.destroyed:!1},set(t){this._readableState&&(this._readableState.destroyed=t)}},readableEnded:{__proto__:null,enumerable:!1,get(){return this._readableState?this._readableState.endEmitted:!1}}});eo(an.prototype,{pipesCount:{__proto__:null,get(){return this.pipes.length}},paused:{__proto__:null,get(){return this[rr]!==!1},set(t){this[rr]=!!t}}});F._fromList=Ff;function Ff(t,e){if(e.length===0)return null;let r;return e.objectMode?r=e.buffer.shift():!t||t>=e.length?(e.decoder?r=e.buffer.join(""):e.buffer.length===1?r=e.buffer.first():r=e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r}function Ys(t){let e=t._readableState;H("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,We.nextTick(R_,e,t))}function R_(t,e){if(H("endReadableNT",t.endEmitted,t.length),!t.errored&&!t.closeEmitted&&!t.endEmitted&&t.length===0){if(t.endEmitted=!0,e.emit("end"),e.writable&&e.allowHalfOpen===!1)We.nextTick(C_,e);else if(t.autoDestroy){let r=e._writableState;(!r||r.autoDestroy&&(r.finished||r.writable===!1))&&e.destroy()}}}function C_(t){t.writable&&!t.writableEnded&&!t.destroyed&&t.end()}F.from=function(t,e){return l_(F,t,e)};var Js;function Wf(){return Js===void 0&&(Js={}),Js}F.fromWeb=function(t,e){return Wf().newStreamReadableFromReadableStream(t,e)};F.toWeb=function(t,e){return Wf().newReadableStreamFromStreamReadable(t,e)};F.wrap=function(t,e){var r,i;return new F({objectMode:(r=(i=t.readableObjectMode)!==null&&i!==void 0?i:t.objectMode)!==null&&r!==void 0?r:!0,...e,destroy(n,o){Lr.destroyer(t,n),o(n)}}).wrap(t)}});var dn=O((bR,th)=>{"use strict";_();v();m();var ir=Mt(),{ArrayPrototypeSlice:zf,Error:P_,FunctionPrototypeSymbolHasInstance:Kf,ObjectDefineProperty:Gf,ObjectDefineProperties:k_,ObjectSetPrototypeOf:Qf,StringPrototypeToLowerCase:B_,Symbol:x_,SymbolHasInstance:O_}=ie();th.exports=se;se.WritableState=pi;var{EventEmitter:M_}=(Ot(),Q(xt)),hi=Zi().Stream,{Buffer:un}=(he(),Q(ye)),hn=er(),{addAbortSignal:L_}=ai(),{getHighWaterMark:q_,getDefaultHighWaterMark:U_}=li(),{ERR_INVALID_ARG_TYPE:N_,ERR_METHOD_NOT_IMPLEMENTED:D_,ERR_MULTIPLE_CALLBACK:Yf,ERR_STREAM_CANNOT_PIPE:j_,ERR_STREAM_DESTROYED:di,ERR_STREAM_ALREADY_FINISHED:F_,ERR_STREAM_NULL_VALUES:W_,ERR_STREAM_WRITE_AFTER_END:$_,ERR_UNKNOWN_ENCODING:Jf}=me().codes,{errorOrDestroy:qr}=hn;Qf(se.prototype,hi.prototype);Qf(se,hi);function io(){}var Ur=x_("kOnFinished");function pi(t,e,r){typeof r!="boolean"&&(r=e instanceof rt()),this.objectMode=!!(t&&t.objectMode),r&&(this.objectMode=this.objectMode||!!(t&&t.writableObjectMode)),this.highWaterMark=t?q_(this,t,"writableHighWaterMark",r):U_(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;let i=!!(t&&t.decodeStrings===!1);this.decodeStrings=!i,this.defaultEncoding=t&&t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=V_.bind(void 0,e),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,fn(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!t||t.emitClose!==!1,this.autoDestroy=!t||t.autoDestroy!==!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[Ur]=[]}function fn(t){t.buffered=[],t.bufferedIndex=0,t.allBuffers=!0,t.allNoop=!0}pi.prototype.getBuffer=function(){return zf(this.buffered,this.bufferedIndex)};Gf(pi.prototype,"bufferedRequestCount",{__proto__:null,get(){return this.buffered.length-this.bufferedIndex}});function se(t){let e=this instanceof rt();if(!e&&!Kf(se,this))return new se(t);this._writableState=new pi(t,this,e),t&&(typeof t.write=="function"&&(this._write=t.write),typeof t.writev=="function"&&(this._writev=t.writev),typeof t.destroy=="function"&&(this._destroy=t.destroy),typeof t.final=="function"&&(this._final=t.final),typeof t.construct=="function"&&(this._construct=t.construct),t.signal&&L_(t.signal,this)),hi.call(this,t),hn.construct(this,()=>{let r=this._writableState;r.writing||so(this,r),oo(this,r)})}Gf(se,O_,{__proto__:null,value:function(t){return Kf(this,t)?!0:this!==se?!1:t&&t._writableState instanceof pi}});se.prototype.pipe=function(){qr(this,new j_)};function Xf(t,e,r,i){let n=t._writableState;if(typeof r=="function")i=r,r=n.defaultEncoding;else{if(!r)r=n.defaultEncoding;else if(r!=="buffer"&&!un.isEncoding(r))throw new Jf(r);typeof i!="function"&&(i=io)}if(e===null)throw new W_;if(!n.objectMode)if(typeof e=="string")n.decodeStrings!==!1&&(e=un.from(e,r),r="buffer");else if(e instanceof un)r="buffer";else if(hi._isUint8Array(e))e=hi._uint8ArrayToBuffer(e),r="buffer";else throw new N_("chunk",["string","Buffer","Uint8Array"],e);let o;return n.ending?o=new $_:n.destroyed&&(o=new di("write")),o?(ir.nextTick(i,o),qr(t,o,!0),o):(n.pendingcb++,H_(t,n,e,r,i))}se.prototype.write=function(t,e,r){return Xf(this,t,e,r)===!0};se.prototype.cork=function(){this._writableState.corked++};se.prototype.uncork=function(){let t=this._writableState;t.corked&&(t.corked--,t.writing||so(this,t))};se.prototype.setDefaultEncoding=function(e){if(typeof e=="string"&&(e=B_(e)),!un.isEncoding(e))throw new Jf(e);return this._writableState.defaultEncoding=e,this};function H_(t,e,r,i,n){let o=e.objectMode?1:r.length;e.length+=o;let s=e.length<e.highWaterMark;return s||(e.needDrain=!0),e.writing||e.corked||e.errored||!e.constructed?(e.buffered.push({chunk:r,encoding:i,callback:n}),e.allBuffers&&i!=="buffer"&&(e.allBuffers=!1),e.allNoop&&n!==io&&(e.allNoop=!1)):(e.writelen=o,e.writecb=n,e.writing=!0,e.sync=!0,t._write(r,i,e.onwrite),e.sync=!1),s&&!e.errored&&!e.destroyed}function Hf(t,e,r,i,n,o,s){e.writelen=i,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new di("write")):r?t._writev(n,e.onwrite):t._write(n,o,e.onwrite),e.sync=!1}function Vf(t,e,r,i){--e.pendingcb,i(r),no(e),qr(t,r)}function V_(t,e){let r=t._writableState,i=r.sync,n=r.writecb;if(typeof n!="function"){qr(t,new Yf);return}r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,e?(e.stack,r.errored||(r.errored=e),t._readableState&&!t._readableState.errored&&(t._readableState.errored=e),i?ir.nextTick(Vf,t,r,e,n):Vf(t,r,e,n)):(r.buffered.length>r.bufferedIndex&&so(t,r),i?r.afterWriteTickInfo!==null&&r.afterWriteTickInfo.cb===n?r.afterWriteTickInfo.count++:(r.afterWriteTickInfo={count:1,cb:n,stream:t,state:r},ir.nextTick(z_,r.afterWriteTickInfo)):Zf(t,r,1,n))}function z_({stream:t,state:e,count:r,cb:i}){return e.afterWriteTickInfo=null,Zf(t,e,r,i)}function Zf(t,e,r,i){for(!e.ending&&!t.destroyed&&e.length===0&&e.needDrain&&(e.needDrain=!1,t.emit("drain"));r-- >0;)e.pendingcb--,i();e.destroyed&&no(e),oo(t,e)}function no(t){if(t.writing)return;for(let n=t.bufferedIndex;n<t.buffered.length;++n){var e;let{chunk:o,callback:s}=t.buffered[n],a=t.objectMode?1:o.length;t.length-=a,s((e=t.errored)!==null&&e!==void 0?e:new di("write"))}let r=t[Ur].splice(0);for(let n=0;n<r.length;n++){var i;r[n]((i=t.errored)!==null&&i!==void 0?i:new di("end"))}fn(t)}function so(t,e){if(e.corked||e.bufferProcessing||e.destroyed||!e.constructed)return;let{buffered:r,bufferedIndex:i,objectMode:n}=e,o=r.length-i;if(!o)return;let s=i;if(e.bufferProcessing=!0,o>1&&t._writev){e.pendingcb-=o-1;let a=e.allNoop?io:f=>{for(let d=s;d<r.length;++d)r[d].callback(f)},u=e.allNoop&&s===0?r:zf(r,s);u.allBuffers=e.allBuffers,Hf(t,e,!0,e.length,u,"",a),fn(e)}else{do{let{chunk:a,encoding:u,callback:f}=r[s];r[s++]=null;let d=n?1:a.length;Hf(t,e,!1,d,a,u,f)}while(s<r.length&&!e.writing);s===r.length?fn(e):s>256?(r.splice(0,s),e.bufferedIndex=0):e.bufferedIndex=s}e.bufferProcessing=!1}se.prototype._write=function(t,e,r){if(this._writev)this._writev([{chunk:t,encoding:e}],r);else throw new D_("_write()")};se.prototype._writev=null;se.prototype.end=function(t,e,r){let i=this._writableState;typeof t=="function"?(r=t,t=null,e=null):typeof e=="function"&&(r=e,e=null);let n;if(t!=null){let o=Xf(this,t,e);o instanceof P_&&(n=o)}return i.corked&&(i.corked=1,this.uncork()),n||(!i.errored&&!i.ending?(i.ending=!0,oo(this,i,!0),i.ended=!0):i.finished?n=new F_("end"):i.destroyed&&(n=new di("end"))),typeof r=="function"&&(n||i.finished?ir.nextTick(r,n):i[Ur].push(r)),this};function cn(t){return t.ending&&!t.destroyed&&t.constructed&&t.length===0&&!t.errored&&t.buffered.length===0&&!t.finished&&!t.writing&&!t.errorEmitted&&!t.closeEmitted}function K_(t,e){let r=!1;function i(n){if(r){qr(t,n??Yf());return}if(r=!0,e.pendingcb--,n){let o=e[Ur].splice(0);for(let s=0;s<o.length;s++)o[s](n);qr(t,n,e.sync)}else cn(e)&&(e.prefinished=!0,t.emit("prefinish"),e.pendingcb++,ir.nextTick(ro,t,e))}e.sync=!0,e.pendingcb++;try{t._final(i)}catch(n){i(n)}e.sync=!1}function G_(t,e){!e.prefinished&&!e.finalCalled&&(typeof t._final=="function"&&!e.destroyed?(e.finalCalled=!0,K_(t,e)):(e.prefinished=!0,t.emit("prefinish")))}function oo(t,e,r){cn(e)&&(G_(t,e),e.pendingcb===0&&(r?(e.pendingcb++,ir.nextTick((i,n)=>{cn(n)?ro(i,n):n.pendingcb--},t,e)):cn(e)&&(e.pendingcb++,ro(t,e))))}function ro(t,e){e.pendingcb--,e.finished=!0;let r=e[Ur].splice(0);for(let i=0;i<r.length;i++)r[i]();if(t.emit("finish"),e.autoDestroy){let i=t._readableState;(!i||i.autoDestroy&&(i.endEmitted||i.readable===!1))&&t.destroy()}}k_(se.prototype,{closed:{__proto__:null,get(){return this._writableState?this._writableState.closed:!1}},destroyed:{__proto__:null,get(){return this._writableState?this._writableState.destroyed:!1},set(t){this._writableState&&(this._writableState.destroyed=t)}},writable:{__proto__:null,get(){let t=this._writableState;return!!t&&t.writable!==!1&&!t.destroyed&&!t.errored&&!t.ending&&!t.ended},set(t){this._writableState&&(this._writableState.writable=!!t)}},writableFinished:{__proto__:null,get(){return this._writableState?this._writableState.finished:!1}},writableObjectMode:{__proto__:null,get(){return this._writableState?this._writableState.objectMode:!1}},writableBuffer:{__proto__:null,get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{__proto__:null,get(){return this._writableState?this._writableState.ending:!1}},writableNeedDrain:{__proto__:null,get(){let t=this._writableState;return t?!t.destroyed&&!t.ending&&t.needDrain:!1}},writableHighWaterMark:{__proto__:null,get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{__proto__:null,get(){return this._writableState?this._writableState.corked:0}},writableLength:{__proto__:null,get(){return this._writableState&&this._writableState.length}},errored:{__proto__:null,enumerable:!1,get(){return this._writableState?this._writableState.errored:null}},writableAborted:{__proto__:null,enumerable:!1,get:function(){return!!(this._writableState.writable!==!1&&(this._writableState.destroyed||this._writableState.errored)&&!this._writableState.finished)}}});var Q_=hn.destroy;se.prototype.destroy=function(t,e){let r=this._writableState;return!r.destroyed&&(r.bufferedIndex<r.buffered.length||r[Ur].length)&&ir.nextTick(no,r),Q_.call(this,t,e),this};se.prototype._undestroy=hn.undestroy;se.prototype._destroy=function(t,e){e(t)};se.prototype[M_.captureRejectionSymbol]=function(t){this.destroy(t)};var to;function eh(){return to===void 0&&(to={}),to}se.fromWeb=function(t,e){return eh().newStreamWritableFromWritableStream(t,e)};se.toWeb=function(t){return eh().newWritableStreamFromStreamWritable(t)}});var bh=O((SR,gh)=>{_();v();m();var ao=Mt(),Y_=(he(),Q(ye)),{isReadable:J_,isWritable:X_,isIterable:rh,isNodeStream:Z_,isReadableNodeStream:ih,isWritableNodeStream:nh,isDuplexNodeStream:em,isReadableStream:sh,isWritableStream:oh}=Ze(),ah=_t(),{AbortError:dh,codes:{ERR_INVALID_ARG_TYPE:tm,ERR_INVALID_RETURN_VALUE:lh}}=me(),{destroyer:Dr}=er(),rm=rt(),ph=fi(),im=dn(),{createDeferredPromise:uh}=Ie(),ch=zs(),fh=globalThis.Blob||Y_.Blob,nm=typeof fh<"u"?function(e){return e instanceof fh}:function(e){return!1},sm=globalThis.AbortController||Rr().AbortController,{FunctionPrototypeCall:hh}=ie(),Ut=class extends rm{constructor(e){super(e),e?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),e?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}};gh.exports=function t(e,r){if(em(e))return e;if(ih(e))return Nr({readable:e});if(nh(e))return Nr({writable:e});if(Z_(e))return Nr({writable:!1,readable:!1});if(sh(e))return Nr({readable:ph.fromWeb(e)});if(oh(e))return Nr({writable:im.fromWeb(e)});if(typeof e=="function"){let{value:n,write:o,final:s,destroy:a}=om(e);if(rh(n))return ch(Ut,n,{objectMode:!0,write:o,final:s,destroy:a});let u=n?.then;if(typeof u=="function"){let f,d=hh(u,n,h=>{if(h!=null)throw new lh("nully","body",h)},h=>{Dr(f,h)});return f=new Ut({objectMode:!0,readable:!1,write:o,final(h){s(async()=>{try{await d,ao.nextTick(h,null)}catch(g){ao.nextTick(h,g)}})},destroy:a})}throw new lh("Iterable, AsyncIterable or AsyncFunction",r,n)}if(nm(e))return t(e.arrayBuffer());if(rh(e))return ch(Ut,e,{objectMode:!0,writable:!1});if(sh(e?.readable)&&oh(e?.writable))return Ut.fromWeb(e);if(typeof e?.writable=="object"||typeof e?.readable=="object"){let n=e!=null&&e.readable?ih(e?.readable)?e?.readable:t(e.readable):void 0,o=e!=null&&e.writable?nh(e?.writable)?e?.writable:t(e.writable):void 0;return Nr({readable:n,writable:o})}let i=e?.then;if(typeof i=="function"){let n;return hh(i,e,o=>{o!=null&&n.push(o),n.push(null)},o=>{Dr(n,o)}),n=new Ut({objectMode:!0,writable:!1,read(){}})}throw new tm(r,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],e)};function om(t){let{promise:e,resolve:r}=uh(),i=new sm,n=i.signal;return{value:t(async function*(){for(;;){let s=e;e=null;let{chunk:a,done:u,cb:f}=await s;if(ao.nextTick(f),u)return;if(n.aborted)throw new dh(void 0,{cause:n.reason});({promise:e,resolve:r}=uh()),yield a}}(),{signal:n}),write(s,a,u){let f=r;r=null,f({chunk:s,done:!1,cb:u})},final(s){let a=r;r=null,a({done:!0,cb:s})},destroy(s,a){i.abort(),a(s)}}}function Nr(t){let e=t.readable&&typeof t.readable.read!="function"?ph.wrap(t.readable):t.readable,r=t.writable,i=!!J_(e),n=!!X_(r),o,s,a,u,f;function d(h){let g=u;u=null,g?g(h):h&&f.destroy(h)}return f=new Ut({readableObjectMode:!!(e!=null&&e.readableObjectMode),writableObjectMode:!!(r!=null&&r.writableObjectMode),readable:i,writable:n}),n&&(ah(r,h=>{n=!1,h&&Dr(e,h),d(h)}),f._write=function(h,g,b){r.write(h,g)?b():o=b},f._final=function(h){r.end(),s=h},r.on("drain",function(){if(o){let h=o;o=null,h()}}),r.on("finish",function(){if(s){let h=s;s=null,h()}})),i&&(ah(e,h=>{i=!1,h&&Dr(e,h),d(h)}),e.on("readable",function(){if(a){let h=a;a=null,h()}}),e.on("end",function(){f.push(null)}),f._read=function(){for(;;){let h=e.read();if(h===null){a=f._read;return}if(!f.push(h))return}}),f._destroy=function(h,g){!h&&u!==null&&(h=new dh),a=null,o=null,s=null,u===null?g(h):(u=g,Dr(r,h),Dr(e,h))},f}});var rt=O((kR,_h)=>{"use strict";_();v();m();var{ObjectDefineProperties:am,ObjectGetOwnPropertyDescriptor:mt,ObjectKeys:lm,ObjectSetPrototypeOf:yh}=ie();_h.exports=$e;var co=fi(),Le=dn();yh($e.prototype,co.prototype);yh($e,co);{let t=lm(Le.prototype);for(let e=0;e<t.length;e++){let r=t[e];$e.prototype[r]||($e.prototype[r]=Le.prototype[r])}}function $e(t){if(!(this instanceof $e))return new $e(t);co.call(this,t),Le.call(this,t),t?(this.allowHalfOpen=t.allowHalfOpen!==!1,t.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),t.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}am($e.prototype,{writable:{__proto__:null,...mt(Le.prototype,"writable")},writableHighWaterMark:{__proto__:null,...mt(Le.prototype,"writableHighWaterMark")},writableObjectMode:{__proto__:null,...mt(Le.prototype,"writableObjectMode")},writableBuffer:{__proto__:null,...mt(Le.prototype,"writableBuffer")},writableLength:{__proto__:null,...mt(Le.prototype,"writableLength")},writableFinished:{__proto__:null,...mt(Le.prototype,"writableFinished")},writableCorked:{__proto__:null,...mt(Le.prototype,"writableCorked")},writableEnded:{__proto__:null,...mt(Le.prototype,"writableEnded")},writableNeedDrain:{__proto__:null,...mt(Le.prototype,"writableNeedDrain")},destroyed:{__proto__:null,get(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set(t){this._readableState&&this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}});var lo;function wh(){return lo===void 0&&(lo={}),lo}$e.fromWeb=function(t,e){return wh().newStreamDuplexFromReadableWritablePair(t,e)};$e.toWeb=function(t){return wh().newReadableWritablePairFromDuplex(t)};var uo;$e.from=function(t){return uo||(uo=bh()),uo(t,"body")}});var po=O((UR,vh)=>{"use strict";_();v();m();var{ObjectSetPrototypeOf:mh,Symbol:um}=ie();vh.exports=vt;var{ERR_METHOD_NOT_IMPLEMENTED:cm}=me().codes,ho=rt(),{getHighWaterMark:fm}=li();mh(vt.prototype,ho.prototype);mh(vt,ho);var gi=um("kCallback");function vt(t){if(!(this instanceof vt))return new vt(t);let e=t?fm(this,t,"readableHighWaterMark",!0):null;e===0&&(t={...t,highWaterMark:null,readableHighWaterMark:e,writableHighWaterMark:t.writableHighWaterMark||0}),ho.call(this,t),this._readableState.sync=!1,this[gi]=null,t&&(typeof t.transform=="function"&&(this._transform=t.transform),typeof t.flush=="function"&&(this._flush=t.flush)),this.on("prefinish",hm)}function fo(t){typeof this._flush=="function"&&!this.destroyed?this._flush((e,r)=>{if(e){t?t(e):this.destroy(e);return}r!=null&&this.push(r),this.push(null),t&&t()}):(this.push(null),t&&t())}function hm(){this._final!==fo&&fo.call(this)}vt.prototype._final=fo;vt.prototype._transform=function(t,e,r){throw new cm("_transform()")};vt.prototype._write=function(t,e,r){let i=this._readableState,n=this._writableState,o=i.length;this._transform(t,e,(s,a)=>{if(s){r(s);return}a!=null&&this.push(a),n.ended||o===i.length||i.length<i.highWaterMark?r():this[gi]=r})};vt.prototype._read=function(){if(this[gi]){let t=this[gi];this[gi]=null,t()}}});var bo=O((HR,Sh)=>{"use strict";_();v();m();var{ObjectSetPrototypeOf:Eh}=ie();Sh.exports=jr;var go=po();Eh(jr.prototype,go.prototype);Eh(jr,go);function jr(t){if(!(this instanceof jr))return new jr(t);go.call(this,t)}jr.prototype._transform=function(t,e,r){r(null,t)}});var yn=O((JR,Ch)=>{_();v();m();var bi=Mt(),{ArrayIsArray:dm,Promise:pm,SymbolAsyncIterator:gm,SymbolDispose:bm}=ie(),bn=_t(),{once:ym}=Ie(),wm=er(),Ah=rt(),{aggregateTwoErrors:_m,codes:{ERR_INVALID_ARG_TYPE:Io,ERR_INVALID_RETURN_VALUE:yo,ERR_MISSING_ARGS:mm,ERR_STREAM_DESTROYED:vm,ERR_STREAM_PREMATURE_CLOSE:Em},AbortError:Sm}=me(),{validateFunction:Am,validateAbortSignal:Im}=kr(),{isIterable:nr,isReadable:wo,isReadableNodeStream:gn,isNodeStream:Ih,isTransformStream:Fr,isWebStream:Tm,isReadableStream:_o,isReadableFinished:Rm}=Ze(),Cm=globalThis.AbortController||Rr().AbortController,mo,vo,Eo;function Th(t,e,r){let i=!1;t.on("close",()=>{i=!0});let n=bn(t,{readable:e,writable:r},o=>{i=!o});return{destroy:o=>{i||(i=!0,wm.destroyer(t,o||new vm("pipe")))},cleanup:n}}function Pm(t){return Am(t[t.length-1],"streams[stream.length - 1]"),t.pop()}function So(t){if(nr(t))return t;if(gn(t))return km(t);throw new Io("val",["Readable","Iterable","AsyncIterable"],t)}async function*km(t){vo||(vo=fi()),yield*vo.prototype[gm].call(t)}async function pn(t,e,r,{end:i}){let n,o=null,s=f=>{if(f&&(n=f),o){let d=o;o=null,d()}},a=()=>new pm((f,d)=>{n?d(n):o=()=>{n?d(n):f()}});e.on("drain",s);let u=bn(e,{readable:!1},s);try{e.writableNeedDrain&&await a();for await(let f of t)e.write(f)||await a();i&&(e.end(),await a()),r()}catch(f){r(n!==f?_m(n,f):f)}finally{u(),e.off("drain",s)}}async function Ao(t,e,r,{end:i}){Fr(e)&&(e=e.writable);let n=e.getWriter();try{for await(let o of t)await n.ready,n.write(o).catch(()=>{});await n.ready,i&&await n.close(),r()}catch(o){try{await n.abort(o),r(o)}catch(s){r(s)}}}function Bm(...t){return Rh(t,ym(Pm(t)))}function Rh(t,e,r){if(t.length===1&&dm(t[0])&&(t=t[0]),t.length<2)throw new mm("streams");let i=new Cm,n=i.signal,o=r?.signal,s=[];Im(o,"options.signal");function a(){E(new Sm)}Eo=Eo||Ie().addAbortListener;let u;o&&(u=Eo(o,a));let f,d,h=[],g=0;function b(C){E(C,--g===0)}function E(C,M){var q;if(C&&(!f||f.code==="ERR_STREAM_PREMATURE_CLOSE")&&(f=C),!(!f&&!M)){for(;h.length;)h.shift()(f);(q=u)===null||q===void 0||q[bm](),i.abort(),M&&(f||s.forEach(z=>z()),bi.nextTick(e,f,d))}}let w;for(let C=0;C<t.length;C++){let M=t[C],q=C<t.length-1,z=C>0,j=q||r?.end!==!1,G=C===t.length-1;if(Ih(M)){let $=function(te){te&&te.name!=="AbortError"&&te.code!=="ERR_STREAM_PREMATURE_CLOSE"&&b(te)};var P=$;if(j){let{destroy:te,cleanup:pt}=Th(M,q,z);h.push(te),wo(M)&&G&&s.push(pt)}M.on("error",$),wo(M)&&G&&s.push(()=>{M.removeListener("error",$)})}if(C===0)if(typeof M=="function"){if(w=M({signal:n}),!nr(w))throw new yo("Iterable, AsyncIterable or Stream","source",w)}else nr(M)||gn(M)||Fr(M)?w=M:w=Ah.from(M);else if(typeof M=="function"){if(Fr(w)){var S;w=So((S=w)===null||S===void 0?void 0:S.readable)}else w=So(w);if(w=M(w,{signal:n}),q){if(!nr(w,!0))throw new yo("AsyncIterable",`transform[${C-1}]`,w)}else{var I;mo||(mo=bo());let $=new mo({objectMode:!0}),te=(I=w)===null||I===void 0?void 0:I.then;if(typeof te=="function")g++,te.call(w,Se=>{d=Se,Se!=null&&$.write(Se),j&&$.end(),bi.nextTick(b)},Se=>{$.destroy(Se),bi.nextTick(b,Se)});else if(nr(w,!0))g++,pn(w,$,b,{end:j});else if(_o(w)||Fr(w)){let Se=w.readable||w;g++,pn(Se,$,b,{end:j})}else throw new yo("AsyncIterable or Promise","destination",w);w=$;let{destroy:pt,cleanup:Fe}=Th(w,!1,!0);h.push(pt),G&&s.push(Fe)}}else if(Ih(M)){if(gn(w)){g+=2;let $=xm(w,M,b,{end:j});wo(M)&&G&&s.push($)}else if(Fr(w)||_o(w)){let $=w.readable||w;g++,pn($,M,b,{end:j})}else if(nr(w))g++,pn(w,M,b,{end:j});else throw new Io("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],w);w=M}else if(Tm(M)){if(gn(w))g++,Ao(So(w),M,b,{end:j});else if(_o(w)||nr(w))g++,Ao(w,M,b,{end:j});else if(Fr(w))g++,Ao(w.readable,M,b,{end:j});else throw new Io("val",["Readable","Iterable","AsyncIterable","ReadableStream","TransformStream"],w);w=M}else w=Ah.from(M)}return(n!=null&&n.aborted||o!=null&&o.aborted)&&bi.nextTick(a),w}function xm(t,e,r,{end:i}){let n=!1;if(e.on("close",()=>{n||r(new Em)}),t.pipe(e,{end:!1}),i){let s=function(){n=!0,e.end()};var o=s;Rm(t)?bi.nextTick(s):t.once("end",s)}else r();return bn(t,{readable:!0,writable:!1},s=>{let a=t._readableState;s&&s.code==="ERR_STREAM_PREMATURE_CLOSE"&&a&&a.ended&&!a.errored&&!a.errorEmitted?t.once("end",r).once("error",r):r(s)}),bn(e,{readable:!1,writable:!0},r)}Ch.exports={pipelineImpl:Rh,pipeline:Bm}});var Ro=O((nC,Mh)=>{"use strict";_();v();m();var{pipeline:Om}=yn(),wn=rt(),{destroyer:Mm}=er(),{isNodeStream:_n,isReadable:Ph,isWritable:kh,isWebStream:To,isTransformStream:sr,isWritableStream:Bh,isReadableStream:xh}=Ze(),{AbortError:Lm,codes:{ERR_INVALID_ARG_VALUE:Oh,ERR_MISSING_ARGS:qm}}=me(),Um=_t();Mh.exports=function(...e){if(e.length===0)throw new qm("streams");if(e.length===1)return wn.from(e[0]);let r=[...e];if(typeof e[0]=="function"&&(e[0]=wn.from(e[0])),typeof e[e.length-1]=="function"){let b=e.length-1;e[b]=wn.from(e[b])}for(let b=0;b<e.length;++b)if(!(!_n(e[b])&&!To(e[b]))){if(b<e.length-1&&!(Ph(e[b])||xh(e[b])||sr(e[b])))throw new Oh(`streams[${b}]`,r[b],"must be readable");if(b>0&&!(kh(e[b])||Bh(e[b])||sr(e[b])))throw new Oh(`streams[${b}]`,r[b],"must be writable")}let i,n,o,s,a;function u(b){let E=s;s=null,E?E(b):b?a.destroy(b):!g&&!h&&a.destroy()}let f=e[0],d=Om(e,u),h=!!(kh(f)||Bh(f)||sr(f)),g=!!(Ph(d)||xh(d)||sr(d));if(a=new wn({writableObjectMode:!!(f!=null&&f.writableObjectMode),readableObjectMode:!!(d!=null&&d.readableObjectMode),writable:h,readable:g}),h){if(_n(f))a._write=function(E,w,S){f.write(E,w)?S():i=S},a._final=function(E){f.end(),n=E},f.on("drain",function(){if(i){let E=i;i=null,E()}});else if(To(f)){let w=(sr(f)?f.writable:f).getWriter();a._write=async function(S,I,P){try{await w.ready,w.write(S).catch(()=>{}),P()}catch(C){P(C)}},a._final=async function(S){try{await w.ready,w.close().catch(()=>{}),n=S}catch(I){S(I)}}}let b=sr(d)?d.readable:d;Um(b,()=>{if(n){let E=n;n=null,E()}})}if(g){if(_n(d))d.on("readable",function(){if(o){let b=o;o=null,b()}}),d.on("end",function(){a.push(null)}),a._read=function(){for(;;){let b=d.read();if(b===null){o=a._read;return}if(!a.push(b))return}};else if(To(d)){let E=(sr(d)?d.readable:d).getReader();a._read=async function(){for(;;)try{let{value:w,done:S}=await E.read();if(!a.push(w))return;if(S){a.push(null);return}}catch{return}}}}return a._destroy=function(b,E){!b&&s!==null&&(b=new Lm),o=null,i=null,n=null,s===null?E(b):(s=E,_n(d)&&Mm(d,b))},a}});var Hh=O((fC,ko)=>{"use strict";_();v();m();var Nm=globalThis.AbortController||Rr().AbortController,{codes:{ERR_INVALID_ARG_VALUE:Dm,ERR_INVALID_ARG_TYPE:yi,ERR_MISSING_ARGS:jm,ERR_OUT_OF_RANGE:Fm},AbortError:it}=me(),{validateAbortSignal:or,validateInteger:Lh,validateObject:ar}=kr(),Wm=ie().Symbol("kWeak"),$m=ie().Symbol("kResistStopPropagation"),{finished:Hm}=_t(),Vm=Ro(),{addAbortSignalNoValidate:zm}=ai(),{isWritable:Km,isNodeStream:Gm}=Ze(),{deprecate:Qm}=Ie(),{ArrayPrototypePush:Ym,Boolean:Jm,MathFloor:qh,Number:Xm,NumberIsNaN:Zm,Promise:Uh,PromiseReject:Nh,PromiseResolve:e0,PromisePrototypeThen:Dh,Symbol:Fh}=ie(),mn=Fh("kEmpty"),jh=Fh("kEof");function t0(t,e){if(e!=null&&ar(e,"options"),e?.signal!=null&&or(e.signal,"options.signal"),Gm(t)&&!Km(t))throw new Dm("stream",t,"must be writable");let r=Vm(this,t);return e!=null&&e.signal&&zm(e.signal,r),r}function vn(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);e!=null&&ar(e,"options"),e?.signal!=null&&or(e.signal,"options.signal");let r=1;e?.concurrency!=null&&(r=qh(e.concurrency));let i=r-1;return e?.highWaterMark!=null&&(i=qh(e.highWaterMark)),Lh(r,"options.concurrency",1),Lh(i,"options.highWaterMark",0),i+=r,async function*(){let o=Ie().AbortSignalAny([e?.signal].filter(Jm)),s=this,a=[],u={signal:o},f,d,h=!1,g=0;function b(){h=!0,E()}function E(){g-=1,w()}function w(){d&&!h&&g<r&&a.length<i&&(d(),d=null)}async function S(){try{for await(let I of s){if(h)return;if(o.aborted)throw new it;try{if(I=t(I,u),I===mn)continue;I=e0(I)}catch(P){I=Nh(P)}g+=1,Dh(I,E,b),a.push(I),f&&(f(),f=null),!h&&(a.length>=i||g>=r)&&await new Uh(P=>{d=P})}a.push(jh)}catch(I){let P=Nh(I);Dh(P,E,b),a.push(P)}finally{h=!0,f&&(f(),f=null)}}S();try{for(;;){for(;a.length>0;){let I=await a[0];if(I===jh)return;if(o.aborted)throw new it;I!==mn&&(yield I),a.shift(),w()}await new Uh(I=>{f=I})}}finally{h=!0,d&&(d(),d=null)}}.call(this)}function r0(t=void 0){return t!=null&&ar(t,"options"),t?.signal!=null&&or(t.signal,"options.signal"),async function*(){let r=0;for await(let n of this){var i;if(t!=null&&(i=t.signal)!==null&&i!==void 0&&i.aborted)throw new it({cause:t.signal.reason});yield[r++,n]}}.call(this)}async function Wh(t,e=void 0){for await(let r of Po.call(this,t,e))return!0;return!1}async function i0(t,e=void 0){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);return!await Wh.call(this,async(...r)=>!await t(...r),e)}async function n0(t,e){for await(let r of Po.call(this,t,e))return r}async function s0(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);async function r(i,n){return await t(i,n),mn}for await(let i of vn.call(this,r,e));}function Po(t,e){if(typeof t!="function")throw new yi("fn",["Function","AsyncFunction"],t);async function r(i,n){return await t(i,n)?i:mn}return vn.call(this,r,e)}var Co=class extends jm{constructor(){super("reduce"),this.message="Reduce of an empty stream requires an initial value"}};async function o0(t,e,r){var i;if(typeof t!="function")throw new yi("reducer",["Function","AsyncFunction"],t);r!=null&&ar(r,"options"),r?.signal!=null&&or(r.signal,"options.signal");let n=arguments.length>1;if(r!=null&&(i=r.signal)!==null&&i!==void 0&&i.aborted){let f=new it(void 0,{cause:r.signal.reason});throw this.once("error",()=>{}),await Hm(this.destroy(f)),f}let o=new Nm,s=o.signal;if(r!=null&&r.signal){let f={once:!0,[Wm]:this,[$m]:!0};r.signal.addEventListener("abort",()=>o.abort(),f)}let a=!1;try{for await(let f of this){var u;if(a=!0,r!=null&&(u=r.signal)!==null&&u!==void 0&&u.aborted)throw new it;n?e=await t(e,f,{signal:s}):(e=f,n=!0)}if(!a&&!n)throw new Co}finally{o.abort()}return e}async function a0(t){t!=null&&ar(t,"options"),t?.signal!=null&&or(t.signal,"options.signal");let e=[];for await(let i of this){var r;if(t!=null&&(r=t.signal)!==null&&r!==void 0&&r.aborted)throw new it(void 0,{cause:t.signal.reason});Ym(e,i)}return e}function l0(t,e){let r=vn.call(this,t,e);return async function*(){for await(let n of r)yield*n}.call(this)}function $h(t){if(t=Xm(t),Zm(t))return 0;if(t<0)throw new Fm("number",">= 0",t);return t}function u0(t,e=void 0){return e!=null&&ar(e,"options"),e?.signal!=null&&or(e.signal,"options.signal"),t=$h(t),async function*(){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new it;for await(let o of this){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new it;t--<=0&&(yield o)}}.call(this)}function c0(t,e=void 0){return e!=null&&ar(e,"options"),e?.signal!=null&&or(e.signal,"options.signal"),t=$h(t),async function*(){var i;if(e!=null&&(i=e.signal)!==null&&i!==void 0&&i.aborted)throw new it;for await(let o of this){var n;if(e!=null&&(n=e.signal)!==null&&n!==void 0&&n.aborted)throw new it;if(t-- >0&&(yield o),t<=0)return}}.call(this)}ko.exports.streamReturningOperators={asIndexedPairs:Qm(r0,"readable.asIndexedPairs will be removed in a future version."),drop:u0,filter:Po,flatMap:l0,map:vn,take:c0,compose:t0};ko.exports.promiseReturningOperators={every:i0,forEach:s0,reduce:o0,toArray:a0,some:Wh,find:n0}});var Bo=O((wC,Vh)=>{"use strict";_();v();m();var{ArrayPrototypePop:f0,Promise:h0}=ie(),{isIterable:d0,isNodeStream:p0,isWebStream:g0}=Ze(),{pipelineImpl:b0}=yn(),{finished:y0}=_t();xo();function w0(...t){return new h0((e,r)=>{let i,n,o=t[t.length-1];if(o&&typeof o=="object"&&!p0(o)&&!d0(o)&&!g0(o)){let s=f0(t);i=s.signal,n=s.end}b0(t,(s,a)=>{s?r(s):e(a)},{signal:i,end:n})})}Vh.exports={finished:y0,pipeline:w0}});var xo=O((IC,ed)=>{"use strict";_();v();m();var{Buffer:_0}=(he(),Q(ye)),{ObjectDefineProperty:Et,ObjectKeys:Gh,ReflectApply:Qh}=ie(),{promisify:{custom:Yh}}=Ie(),{streamReturningOperators:zh,promiseReturningOperators:Kh}=Hh(),{codes:{ERR_ILLEGAL_CONSTRUCTOR:Jh}}=me(),m0=Ro(),{setDefaultHighWaterMark:v0,getDefaultHighWaterMark:E0}=li(),{pipeline:Xh}=yn(),{destroyer:S0}=er(),Zh=_t(),Oo=Bo(),wi=Ze(),ee=ed.exports=Zi().Stream;ee.isDestroyed=wi.isDestroyed;ee.isDisturbed=wi.isDisturbed;ee.isErrored=wi.isErrored;ee.isReadable=wi.isReadable;ee.isWritable=wi.isWritable;ee.Readable=fi();for(let t of Gh(zh)){let r=function(...i){if(new.target)throw Jh();return ee.Readable.from(Qh(e,this,i))},e=zh[t];Et(r,"name",{__proto__:null,value:e.name}),Et(r,"length",{__proto__:null,value:e.length}),Et(ee.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}for(let t of Gh(Kh)){let r=function(...i){if(new.target)throw Jh();return Qh(e,this,i)},e=Kh[t];Et(r,"name",{__proto__:null,value:e.name}),Et(r,"length",{__proto__:null,value:e.length}),Et(ee.Readable.prototype,t,{__proto__:null,value:r,enumerable:!1,configurable:!0,writable:!0})}ee.Writable=dn();ee.Duplex=rt();ee.Transform=po();ee.PassThrough=bo();ee.pipeline=Xh;var{addAbortSignal:A0}=ai();ee.addAbortSignal=A0;ee.finished=Zh;ee.destroy=S0;ee.compose=m0;ee.setDefaultHighWaterMark=v0;ee.getDefaultHighWaterMark=E0;Et(ee,"promises",{__proto__:null,configurable:!0,enumerable:!0,get(){return Oo}});Et(Xh,Yh,{__proto__:null,enumerable:!0,get(){return Oo.pipeline}});Et(Zh,Yh,{__proto__:null,enumerable:!0,get(){return Oo.finished}});ee.Stream=ee;ee._isUint8Array=function(e){return e instanceof Uint8Array};ee._uint8ArrayToBuffer=function(e){return _0.from(e.buffer,e.byteOffset,e.byteLength)}});var Nt=O((xC,ce)=>{"use strict";_();v();m();var pe=xo(),I0=Bo(),T0=pe.Readable.destroy;ce.exports=pe.Readable;ce.exports._uint8ArrayToBuffer=pe._uint8ArrayToBuffer;ce.exports._isUint8Array=pe._isUint8Array;ce.exports.isDisturbed=pe.isDisturbed;ce.exports.isErrored=pe.isErrored;ce.exports.isReadable=pe.isReadable;ce.exports.Readable=pe.Readable;ce.exports.Writable=pe.Writable;ce.exports.Duplex=pe.Duplex;ce.exports.Transform=pe.Transform;ce.exports.PassThrough=pe.PassThrough;ce.exports.addAbortSignal=pe.addAbortSignal;ce.exports.finished=pe.finished;ce.exports.destroy=pe.destroy;ce.exports.destroy=T0;ce.exports.pipeline=pe.pipeline;ce.exports.compose=pe.compose;Object.defineProperty(pe,"promises",{configurable:!0,enumerable:!0,get(){return I0}});ce.exports.Stream=pe.Stream;ce.exports.default=ce.exports});var td=O((DC,Mo)=>{_();v();m();typeof Object.create=="function"?Mo.exports=function(e,r){r&&(e.super_=r,e.prototype=Object.create(r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:Mo.exports=function(e,r){if(r){e.super_=r;var i=function(){};i.prototype=r.prototype,e.prototype=new i,e.prototype.constructor=e}}});var nd=O((zC,id)=>{"use strict";_();v();m();var{Buffer:He}=(he(),Q(ye)),rd=Symbol.for("BufferList");function Z(t){if(!(this instanceof Z))return new Z(t);Z._init.call(this,t)}Z._init=function(e){Object.defineProperty(this,rd,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)};Z.prototype._new=function(e){return new Z(e)};Z.prototype._offset=function(e){if(e===0)return[0,0];let r=0;for(let i=0;i<this._bufs.length;i++){let n=r+this._bufs[i].length;if(e<n||i===this._bufs.length-1)return[i,e-r];r=n}};Z.prototype._reverseOffset=function(t){let e=t[0],r=t[1];for(let i=0;i<e;i++)r+=this._bufs[i].length;return r};Z.prototype.get=function(e){if(e>this.length||e<0)return;let r=this._offset(e);return this._bufs[r[0]][r[1]]};Z.prototype.slice=function(e,r){return typeof e=="number"&&e<0&&(e+=this.length),typeof r=="number"&&r<0&&(r+=this.length),this.copy(null,0,e,r)};Z.prototype.copy=function(e,r,i,n){if((typeof i!="number"||i<0)&&(i=0),(typeof n!="number"||n>this.length)&&(n=this.length),i>=this.length||n<=0)return e||He.alloc(0);let o=!!e,s=this._offset(i),a=n-i,u=a,f=o&&r||0,d=s[1];if(i===0&&n===this.length){if(!o)return this._bufs.length===1?this._bufs[0]:He.concat(this._bufs,this.length);for(let h=0;h<this._bufs.length;h++)this._bufs[h].copy(e,f),f+=this._bufs[h].length;return e}if(u<=this._bufs[s[0]].length-d)return o?this._bufs[s[0]].copy(e,r,d,d+u):this._bufs[s[0]].slice(d,d+u);o||(e=He.allocUnsafe(a));for(let h=s[0];h<this._bufs.length;h++){let g=this._bufs[h].length-d;if(u>g)this._bufs[h].copy(e,f,d),f+=g;else{this._bufs[h].copy(e,f,d,d+u),f+=g;break}u-=g,d&&(d=0)}return e.length>f?e.slice(0,f):e};Z.prototype.shallowSlice=function(e,r){if(e=e||0,r=typeof r!="number"?this.length:r,e<0&&(e+=this.length),r<0&&(r+=this.length),e===r)return this._new();let i=this._offset(e),n=this._offset(r),o=this._bufs.slice(i[0],n[0]+1);return n[1]===0?o.pop():o[o.length-1]=o[o.length-1].slice(0,n[1]),i[1]!==0&&(o[0]=o[0].slice(i[1])),this._new(o)};Z.prototype.toString=function(e,r,i){return this.slice(r,i).toString(e)};Z.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;for(;this._bufs.length;)if(e>=this._bufs[0].length)e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}return this};Z.prototype.duplicate=function(){let e=this._new();for(let r=0;r<this._bufs.length;r++)e.append(this._bufs[r]);return e};Z.prototype.append=function(e){if(e==null)return this;if(e.buffer)this._appendBuffer(He.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e))for(let r=0;r<e.length;r++)this.append(e[r]);else if(this._isBufferList(e))for(let r=0;r<e._bufs.length;r++)this.append(e._bufs[r]);else typeof e=="number"&&(e=e.toString()),this._appendBuffer(He.from(e));return this};Z.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length};Z.prototype.indexOf=function(t,e,r){if(r===void 0&&typeof e=="string"&&(r=e,e=void 0),typeof t=="function"||Array.isArray(t))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if(typeof t=="number"?t=He.from([t]):typeof t=="string"?t=He.from(t,r):this._isBufferList(t)?t=t.slice():Array.isArray(t.buffer)?t=He.from(t.buffer,t.byteOffset,t.byteLength):He.isBuffer(t)||(t=He.from(t)),e=Number(e||0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),t.length===0)return e>this.length?this.length:e;let i=this._offset(e),n=i[0],o=i[1];for(;n<this._bufs.length;n++){let s=this._bufs[n];for(;o<s.length;)if(s.length-o>=t.length){let u=s.indexOf(t,o);if(u!==-1)return this._reverseOffset([n,u]);o=s.length-t.length+1}else{let u=this._reverseOffset([n,o]);if(this._match(u,t))return u;o++}o=0}return-1};Z.prototype._match=function(t,e){if(this.length-t<e.length)return!1;for(let r=0;r<e.length;r++)if(this.get(t+r)!==e[r])return!1;return!0};(function(){let t={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readBigInt64BE:8,readBigInt64LE:8,readBigUInt64BE:8,readBigUInt64LE:8,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(let e in t)(function(r){t[r]===null?Z.prototype[r]=function(i,n){return this.slice(i,i+n)[r](0,n)}:Z.prototype[r]=function(i=0){return this.slice(i,i+t[r])[r](0)}})(e)})();Z.prototype._isBufferList=function(e){return e instanceof Z||Z.isBufferList(e)};Z.isBufferList=function(e){return e!=null&&e[rd]};id.exports=Z});var sd=O((ZC,En)=>{"use strict";_();v();m();var Lo=Nt().Duplex,R0=td(),_i=nd();function Ee(t){if(!(this instanceof Ee))return new Ee(t);if(typeof t=="function"){this._callback=t;let e=function(i){this._callback&&(this._callback(i),this._callback=null)}.bind(this);this.on("pipe",function(i){i.on("error",e)}),this.on("unpipe",function(i){i.removeListener("error",e)}),t=null}_i._init.call(this,t),Lo.call(this)}R0(Ee,Lo);Object.assign(Ee.prototype,_i.prototype);Ee.prototype._new=function(e){return new Ee(e)};Ee.prototype._write=function(e,r,i){this._appendBuffer(e),typeof i=="function"&&i()};Ee.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)};Ee.prototype.end=function(e){Lo.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)};Ee.prototype._destroy=function(e,r){this._bufs.length=0,this.length=0,r(e)};Ee.prototype._isBufferList=function(e){return e instanceof Ee||e instanceof _i||Ee.isBufferList(e)};Ee.isBufferList=_i.isBufferList;En.exports=Ee;En.exports.BufferListStream=Ee;En.exports.BufferList=_i});var ad=O((oP,od)=>{_();v();m();var qo=class{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}};od.exports=qo});var Uo=O((dP,ld)=>{_();v();m();var L=ld.exports,{Buffer:ke}=(he(),Q(ye));L.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"};L.requiredHeaderFlags={1:0,2:0,4:0,5:0,6:2,7:0,8:2,9:0,10:2,11:0,12:0,13:0,14:0,15:0};L.requiredHeaderFlagsErrors={};for(let t in L.requiredHeaderFlags){let e=L.requiredHeaderFlags[t];L.requiredHeaderFlagsErrors[t]="Invalid header flag bits, must be 0x"+e.toString(16)+" for "+L.types[t]+" packet"}L.codes={};for(let t in L.types){let e=L.types[t];L.codes[e]=t}L.CMD_SHIFT=4;L.CMD_MASK=240;L.DUP_MASK=8;L.QOS_MASK=3;L.QOS_SHIFT=1;L.RETAIN_MASK=1;L.VARBYTEINT_MASK=127;L.VARBYTEINT_FIN_MASK=128;L.VARBYTEINT_MAX=268435455;L.SESSIONPRESENT_MASK=1;L.SESSIONPRESENT_HEADER=ke.from([L.SESSIONPRESENT_MASK]);L.CONNACK_HEADER=ke.from([L.codes.connack<<L.CMD_SHIFT]);L.USERNAME_MASK=128;L.PASSWORD_MASK=64;L.WILL_RETAIN_MASK=32;L.WILL_QOS_MASK=24;L.WILL_QOS_SHIFT=3;L.WILL_FLAG_MASK=4;L.CLEAN_SESSION_MASK=2;L.CONNECT_HEADER=ke.from([L.codes.connect<<L.CMD_SHIFT]);L.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11};L.propertiesCodes={};for(let t in L.properties){let e=L.properties[t];L.propertiesCodes[e]=t}L.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"};function Dt(t){return[0,1,2].map(e=>[0,1].map(r=>[0,1].map(i=>{let n=ke.alloc(1);return n.writeUInt8(L.codes[t]<<L.CMD_SHIFT|(r?L.DUP_MASK:0)|e<<L.QOS_SHIFT|i,0,!0),n})))}L.PUBLISH_HEADER=Dt("publish");L.SUBSCRIBE_HEADER=Dt("subscribe");L.SUBSCRIBE_OPTIONS_QOS_MASK=3;L.SUBSCRIBE_OPTIONS_NL_MASK=1;L.SUBSCRIBE_OPTIONS_NL_SHIFT=2;L.SUBSCRIBE_OPTIONS_RAP_MASK=1;L.SUBSCRIBE_OPTIONS_RAP_SHIFT=3;L.SUBSCRIBE_OPTIONS_RH_MASK=3;L.SUBSCRIBE_OPTIONS_RH_SHIFT=4;L.SUBSCRIBE_OPTIONS_RH=[0,16,32];L.SUBSCRIBE_OPTIONS_NL=4;L.SUBSCRIBE_OPTIONS_RAP=8;L.SUBSCRIBE_OPTIONS_QOS=[0,1,2];L.UNSUBSCRIBE_HEADER=Dt("unsubscribe");L.ACKS={unsuback:Dt("unsuback"),puback:Dt("puback"),pubcomp:Dt("pubcomp"),pubrel:Dt("pubrel"),pubrec:Dt("pubrec")};L.SUBACK_HEADER=ke.from([L.codes.suback<<L.CMD_SHIFT]);L.VERSION3=ke.from([3]);L.VERSION4=ke.from([4]);L.VERSION5=ke.from([5]);L.VERSION131=ke.from([131]);L.VERSION132=ke.from([132]);L.QOS=[0,1,2].map(t=>ke.from([t]));L.EMPTY={pingreq:ke.from([L.codes.pingreq<<4,0]),pingresp:ke.from([L.codes.pingresp<<4,0]),disconnect:ke.from([L.codes.disconnect<<4,0])};L.MQTT5_PUBACK_PUBREC_CODES={0:"Success",16:"No matching subscribers",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",144:"Topic Name invalid",145:"Packet identifier in use",151:"Quota exceeded",153:"Payload format invalid"};L.MQTT5_PUBREL_PUBCOMP_CODES={0:"Success",146:"Packet Identifier not found"};L.MQTT5_SUBACK_CODES={0:"Granted QoS 0",1:"Granted QoS 1",2:"Granted QoS 2",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use",151:"Quota exceeded",158:"Shared Subscriptions not supported",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};L.MQTT5_UNSUBACK_CODES={0:"Success",17:"No subscription existed",128:"Unspecified error",131:"Implementation specific error",135:"Not authorized",143:"Topic Filter invalid",145:"Packet Identifier in use"};L.MQTT5_DISCONNECT_CODES={0:"Normal disconnection",4:"Disconnect with Will Message",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",135:"Not authorized",137:"Server busy",139:"Server shutting down",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};L.MQTT5_AUTH_CODES={0:"Success",24:"Continue authentication",25:"Re-authenticate"}});var cd=O((mP,ud)=>{_();v();m();var Wr=1e3,$r=Wr*60,Hr=$r*60,lr=Hr*24,C0=lr*7,P0=lr*365.25;ud.exports=function(t,e){e=e||{};var r=typeof t;if(r==="string"&&t.length>0)return k0(t);if(r==="number"&&isFinite(t))return e.long?x0(t):B0(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function k0(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var r=parseFloat(e[1]),i=(e[2]||"ms").toLowerCase();switch(i){case"years":case"year":case"yrs":case"yr":case"y":return r*P0;case"weeks":case"week":case"w":return r*C0;case"days":case"day":case"d":return r*lr;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Hr;case"minutes":case"minute":case"mins":case"min":case"m":return r*$r;case"seconds":case"second":case"secs":case"sec":case"s":return r*Wr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function B0(t){var e=Math.abs(t);return e>=lr?Math.round(t/lr)+"d":e>=Hr?Math.round(t/Hr)+"h":e>=$r?Math.round(t/$r)+"m":e>=Wr?Math.round(t/Wr)+"s":t+"ms"}function x0(t){var e=Math.abs(t);return e>=lr?Sn(t,e,lr,"day"):e>=Hr?Sn(t,e,Hr,"hour"):e>=$r?Sn(t,e,$r,"minute"):e>=Wr?Sn(t,e,Wr,"second"):t+" ms"}function Sn(t,e,r,i){var n=e>=r*1.5;return Math.round(t/r)+" "+i+(n?"s":"")}});var hd=O((RP,fd)=>{_();v();m();function O0(t){r.debug=r,r.default=r,r.coerce=u,r.disable=s,r.enable=n,r.enabled=a,r.humanize=cd(),r.destroy=f,Object.keys(t).forEach(d=>{r[d]=t[d]}),r.names=[],r.skips=[],r.formatters={};function e(d){let h=0;for(let g=0;g<d.length;g++)h=(h<<5)-h+d.charCodeAt(g),h|=0;return r.colors[Math.abs(h)%r.colors.length]}r.selectColor=e;function r(d){let h,g=null,b,E;function w(...S){if(!w.enabled)return;let I=w,P=Number(new Date),C=P-(h||P);I.diff=C,I.prev=h,I.curr=P,h=P,S[0]=r.coerce(S[0]),typeof S[0]!="string"&&S.unshift("%O");let M=0;S[0]=S[0].replace(/%([a-zA-Z%])/g,(z,j)=>{if(z==="%%")return"%";M++;let G=r.formatters[j];if(typeof G=="function"){let $=S[M];z=G.call(I,$),S.splice(M,1),M--}return z}),r.formatArgs.call(I,S),(I.log||r.log).apply(I,S)}return w.namespace=d,w.useColors=r.useColors(),w.color=r.selectColor(d),w.extend=i,w.destroy=r.destroy,Object.defineProperty(w,"enabled",{enumerable:!0,configurable:!1,get:()=>g!==null?g:(b!==r.namespaces&&(b=r.namespaces,E=r.enabled(d)),E),set:S=>{g=S}}),typeof r.init=="function"&&r.init(w),w}function i(d,h){let g=r(this.namespace+(typeof h>"u"?":":h)+d);return g.log=this.log,g}function n(d){r.save(d),r.namespaces=d,r.names=[],r.skips=[];let h=(typeof d=="string"?d:"").trim().replace(" ",",").split(",").filter(Boolean);for(let g of h)g[0]==="-"?r.skips.push(g.slice(1)):r.names.push(g)}function o(d,h){let g=0,b=0,E=-1,w=0;for(;g<d.length;)if(b<h.length&&(h[b]===d[g]||h[b]==="*"))h[b]==="*"?(E=b,w=g,b++):(g++,b++);else if(E!==-1)b=E+1,w++,g=w;else return!1;for(;b<h.length&&h[b]==="*";)b++;return b===h.length}function s(){let d=[...r.names,...r.skips.map(h=>"-"+h)].join(",");return r.enable(""),d}function a(d){for(let h of r.skips)if(o(d,h))return!1;for(let h of r.names)if(o(d,h))return!0;return!1}function u(d){return d instanceof Error?d.stack||d.message:d}function f(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.enable(r.load()),r}fd.exports=O0});var nt=O((Be,An)=>{_();v();m();Be.formatArgs=L0;Be.save=q0;Be.load=U0;Be.useColors=M0;Be.storage=N0();Be.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();Be.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function M0(){if(typeof window<"u"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function L0(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+An.exports.humanize(this.diff),!this.useColors)return;let e="color: "+this.color;t.splice(1,0,e,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,n=>{n!=="%%"&&(r++,n==="%c"&&(i=r))}),t.splice(i,0,e)}Be.log=console.debug||console.log||(()=>{});function q0(t){try{t?Be.storage.setItem("debug",t):Be.storage.removeItem("debug")}catch{}}function U0(){let t;try{t=Be.storage.getItem("debug")}catch{}return!t&&typeof R<"u"&&"env"in R&&(t=R.env.DEBUG),t}function N0(){try{return localStorage}catch{}}An.exports=hd()(Be);var{formatters:D0}=An.exports;D0.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}});var gd=O((jP,pd)=>{_();v();m();var j0=sd(),{EventEmitter:F0}=(Ot(),Q(xt)),dd=ad(),V=Uo(),N=nt()("mqtt-packet:parser"),No=class t extends F0{constructor(){super(),this.parser=this.constructor.parser}static parser(e){return this instanceof t?(this.settings=e||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):new t().parser(e)}_resetState(){N("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new dd,this.error=null,this._list=j0(),this._stateCounter=0}parse(e){for(this.error&&this._resetState(),this._list.append(e),N("parse: current state: %s",this._states[this._stateCounter]);(this.packet.length!==-1||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,N("parse: state complete. _stateCounter is now: %d",this._stateCounter),N("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return N("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){let e=this._list.readUInt8(0),r=e>>V.CMD_SHIFT;this.packet.cmd=V.types[r];let i=e&15,n=V.requiredHeaderFlags[r];return n!=null&&i!==n?this._emitError(new Error(V.requiredHeaderFlagsErrors[r])):(this.packet.retain=(e&V.RETAIN_MASK)!==0,this.packet.qos=e>>V.QOS_SHIFT&V.QOS_MASK,this.packet.qos>2?this._emitError(new Error("Packet must not have both QoS bits set to 1")):(this.packet.dup=(e&V.DUP_MASK)!==0,N("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0))}_parseLength(){let e=this._parseVarByteNum(!0);return e&&(this.packet.length=e.value,this._list.consume(e.bytes)),N("_parseLength %d",e.value),!!e}_parsePayload(){N("_parsePayload: payload %O",this._list);let e=!1;if(this.packet.length===0||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return N("_parsePayload complete result: %s",e),e}_parseConnect(){N("_parseConnect");let e,r,i,n,o={},s=this.packet,a=this._parseString();if(a===null)return this._emitError(new Error("Cannot parse protocolId"));if(a!=="MQTT"&&a!=="MQIsdp")return this._emitError(new Error("Invalid protocolId"));if(s.protocolId=a,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(s.protocolVersion=this._list.readUInt8(this._pos),s.protocolVersion>=128&&(s.bridgeMode=!0,s.protocolVersion=s.protocolVersion-128),s.protocolVersion!==3&&s.protocolVersion!==4&&s.protocolVersion!==5)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(this._list.readUInt8(this._pos)&1)return this._emitError(new Error("Connect flag bit 0 must be 0, but got 1"));o.username=this._list.readUInt8(this._pos)&V.USERNAME_MASK,o.password=this._list.readUInt8(this._pos)&V.PASSWORD_MASK,o.will=this._list.readUInt8(this._pos)&V.WILL_FLAG_MASK;let u=!!(this._list.readUInt8(this._pos)&V.WILL_RETAIN_MASK),f=(this._list.readUInt8(this._pos)&V.WILL_QOS_MASK)>>V.WILL_QOS_SHIFT;if(o.will)s.will={},s.will.retain=u,s.will.qos=f;else{if(u)return this._emitError(new Error("Will Retain Flag must be set to zero when Will Flag is set to 0"));if(f)return this._emitError(new Error("Will QoS must be set to zero when Will Flag is set to 0"))}if(s.clean=(this._list.readUInt8(this._pos)&V.CLEAN_SESSION_MASK)!==0,this._pos++,s.keepalive=this._parseNum(),s.keepalive===-1)return this._emitError(new Error("Packet too short"));if(s.protocolVersion===5){let h=this._parseProperties();Object.getOwnPropertyNames(h).length&&(s.properties=h)}let d=this._parseString();if(d===null)return this._emitError(new Error("Packet too short"));if(s.clientId=d,N("_parseConnect: packet.clientId: %s",s.clientId),o.will){if(s.protocolVersion===5){let h=this._parseProperties();Object.getOwnPropertyNames(h).length&&(s.will.properties=h)}if(e=this._parseString(),e===null)return this._emitError(new Error("Cannot parse will topic"));if(s.will.topic=e,N("_parseConnect: packet.will.topic: %s",s.will.topic),r=this._parseBuffer(),r===null)return this._emitError(new Error("Cannot parse will payload"));s.will.payload=r,N("_parseConnect: packet.will.paylaod: %s",s.will.payload)}if(o.username){if(n=this._parseString(),n===null)return this._emitError(new Error("Cannot parse username"));s.username=n,N("_parseConnect: packet.username: %s",s.username)}if(o.password){if(i=this._parseBuffer(),i===null)return this._emitError(new Error("Cannot parse password"));s.password=i}return this.settings=s,N("_parseConnect: complete"),s}_parseConnack(){N("_parseConnack");let e=this.packet;if(this._list.length<1)return null;let r=this._list.readUInt8(this._pos++);if(r>1)return this._emitError(new Error("Invalid connack flags, bits 7-1 must be set to 0"));if(e.sessionPresent=!!(r&V.SESSIONPRESENT_MASK),this.settings.protocolVersion===5)this._list.length>=2?e.reasonCode=this._list.readUInt8(this._pos++):e.reasonCode=0;else{if(this._list.length<2)return null;e.returnCode=this._list.readUInt8(this._pos++)}if(e.returnCode===-1||e.reasonCode===-1)return this._emitError(new Error("Cannot parse return code"));if(this.settings.protocolVersion===5){let i=this._parseProperties();Object.getOwnPropertyNames(i).length&&(e.properties=i)}N("_parseConnack: complete")}_parsePublish(){N("_parsePublish");let e=this.packet;if(e.topic=this._parseString(),e.topic===null)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0&&!this._parseMessageId())){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}e.payload=this._list.slice(this._pos,e.length),N("_parsePublish: payload from buffer list: %o",e.payload)}}_parseSubscribe(){N("_parseSubscribe");let e=this.packet,r,i,n,o,s,a,u;if(e.subscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let f=this._parseProperties();Object.getOwnPropertyNames(f).length&&(e.properties=f)}if(e.length<=0)return this._emitError(new Error("Malformed subscribe, no payload specified"));for(;this._pos<e.length;){if(r=this._parseString(),r===null)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=e.length)return this._emitError(new Error("Malformed Subscribe Payload"));if(i=this._parseByte(),this.settings.protocolVersion===5){if(i&192)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-6 must be 0"))}else if(i&252)return this._emitError(new Error("Invalid subscribe topic flag bits, bits 7-2 must be 0"));if(n=i&V.SUBSCRIBE_OPTIONS_QOS_MASK,n>2)return this._emitError(new Error("Invalid subscribe QoS, must be <= 2"));if(a=(i>>V.SUBSCRIBE_OPTIONS_NL_SHIFT&V.SUBSCRIBE_OPTIONS_NL_MASK)!==0,s=(i>>V.SUBSCRIBE_OPTIONS_RAP_SHIFT&V.SUBSCRIBE_OPTIONS_RAP_MASK)!==0,o=i>>V.SUBSCRIBE_OPTIONS_RH_SHIFT&V.SUBSCRIBE_OPTIONS_RH_MASK,o>2)return this._emitError(new Error("Invalid retain handling, must be <= 2"));u={topic:r,qos:n},this.settings.protocolVersion===5?(u.nl=a,u.rap=s,u.rh=o):this.settings.bridgeMode&&(u.rh=0,u.rap=!0,u.nl=!0),N("_parseSubscribe: push subscription `%s` to subscription",u),e.subscriptions.push(u)}}}_parseSuback(){N("_parseSuback");let e=this.packet;if(this.packet.granted=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed suback, no payload specified"));for(;this._pos<this.packet.length;){let r=this._list.readUInt8(this._pos++);if(this.settings.protocolVersion===5){if(!V.MQTT5_SUBACK_CODES[r])return this._emitError(new Error("Invalid suback code"))}else if(r>2&&r!==128)return this._emitError(new Error("Invalid suback QoS, must be 0, 1, 2 or 128"));this.packet.granted.push(r)}}}_parseUnsubscribe(){N("_parseUnsubscribe");let e=this.packet;if(e.unsubscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}if(e.length<=0)return this._emitError(new Error("Malformed unsubscribe, no payload specified"));for(;this._pos<e.length;){let r=this._parseString();if(r===null)return this._emitError(new Error("Cannot parse topic"));N("_parseUnsubscribe: push topic `%s` to unsubscriptions",r),e.unsubscriptions.push(r)}}}_parseUnsuback(){N("_parseUnsuback");let e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if((this.settings.protocolVersion===3||this.settings.protocolVersion===4)&&e.length!==2)return this._emitError(new Error("Malformed unsuback, payload length must be 2"));if(e.length<=0)return this._emitError(new Error("Malformed unsuback, no payload specified"));if(this.settings.protocolVersion===5){let r=this._parseProperties();for(Object.getOwnPropertyNames(r).length&&(e.properties=r),e.granted=[];this._pos<this.packet.length;){let i=this._list.readUInt8(this._pos++);if(!V.MQTT5_UNSUBACK_CODES[i])return this._emitError(new Error("Invalid unsuback code"));this.packet.granted.push(i)}}}_parseConfirmation(){N("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);let e=this.packet;if(this._parseMessageId(),this.settings.protocolVersion===5){if(e.length>2){switch(e.reasonCode=this._parseByte(),this.packet.cmd){case"puback":case"pubrec":if(!V.MQTT5_PUBACK_PUBREC_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break;case"pubrel":case"pubcomp":if(!V.MQTT5_PUBREL_PUBCOMP_CODES[e.reasonCode])return this._emitError(new Error("Invalid "+this.packet.cmd+" reason code"));break}N("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)}else e.reasonCode=0;if(e.length>3){let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}}return!0}_parseDisconnect(){let e=this.packet;if(N("_parseDisconnect"),this.settings.protocolVersion===5){this._list.length>0?(e.reasonCode=this._parseByte(),V.MQTT5_DISCONNECT_CODES[e.reasonCode]||this._emitError(new Error("Invalid disconnect reason code"))):e.reasonCode=0;let r=this._parseProperties();Object.getOwnPropertyNames(r).length&&(e.properties=r)}return N("_parseDisconnect result: true"),!0}_parseAuth(){N("_parseAuth");let e=this.packet;if(this.settings.protocolVersion!==5)return this._emitError(new Error("Not supported auth packet for this version MQTT"));if(e.reasonCode=this._parseByte(),!V.MQTT5_AUTH_CODES[e.reasonCode])return this._emitError(new Error("Invalid auth reason code"));let r=this._parseProperties();return Object.getOwnPropertyNames(r).length&&(e.properties=r),N("_parseAuth: result: true"),!0}_parseMessageId(){let e=this.packet;return e.messageId=this._parseNum(),e.messageId===null?(this._emitError(new Error("Cannot parse messageId")),!1):(N("_parseMessageId: packet.messageId %d",e.messageId),!0)}_parseString(e){let r=this._parseNum(),i=r+this._pos;if(r===-1||i>this._list.length||i>this.packet.length)return null;let n=this._list.toString("utf8",this._pos,i);return this._pos+=r,N("_parseString: result: %s",n),n}_parseStringPair(){return N("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){let e=this._parseNum(),r=e+this._pos;if(e===-1||r>this._list.length||r>this.packet.length)return null;let i=this._list.slice(this._pos,r);return this._pos+=e,N("_parseBuffer: result: %o",i),i}_parseNum(){if(this._list.length-this._pos<2)return-1;let e=this._list.readUInt16BE(this._pos);return this._pos+=2,N("_parseNum: result: %s",e),e}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;let e=this._list.readUInt32BE(this._pos);return this._pos+=4,N("_parse4ByteNum: result: %s",e),e}_parseVarByteNum(e){N("_parseVarByteNum");let r=4,i=0,n=1,o=0,s=!1,a,u=this._pos?this._pos:0;for(;i<r&&u+i<this._list.length;){if(a=this._list.readUInt8(u+i++),o+=n*(a&V.VARBYTEINT_MASK),n*=128,(a&V.VARBYTEINT_FIN_MASK)===0){s=!0;break}if(this._list.length<=i)break}return!s&&i===r&&this._list.length>=i&&this._emitError(new Error("Invalid variable byte integer")),u&&(this._pos+=i),s?e?s={bytes:i,value:o}:s=o:s=!1,N("_parseVarByteNum: result: %o",s),s}_parseByte(){let e;return this._pos<this._list.length&&(e=this._list.readUInt8(this._pos),this._pos++),N("_parseByte: result: %o",e),e}_parseByType(e){switch(N("_parseByType: type: %s",e),e){case"byte":return this._parseByte()!==0;case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){N("_parseProperties");let e=this._parseVarByteNum(),i=this._pos+e,n={};for(;this._pos<i;){let o=this._parseByte();if(!o)return this._emitError(new Error("Cannot parse property code type")),!1;let s=V.propertiesCodes[o];if(!s)return this._emitError(new Error("Unknown property")),!1;if(s==="userProperties"){n[s]||(n[s]=Object.create(null));let a=this._parseByType(V.propertiesTypes[s]);if(n[s][a.name])if(Array.isArray(n[s][a.name]))n[s][a.name].push(a.value);else{let u=n[s][a.name];n[s][a.name]=[u],n[s][a.name].push(a.value)}else n[s][a.name]=a.value;continue}n[s]?Array.isArray(n[s])?n[s].push(this._parseByType(V.propertiesTypes[s])):(n[s]=[n[s]],n[s].push(this._parseByType(V.propertiesTypes[s]))):n[s]=this._parseByType(V.propertiesTypes[s])}return n}_newPacket(){return N("_newPacket"),this.packet&&(this._list.consume(this.packet.length),N("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),N("_newPacket: new packet"),this.packet=new dd,this._pos=0,!0}_emitError(e){N("_emitError",e),this.error=e,this.emit("error",e)}};pd.exports=No});var _d=O((KP,wd)=>{_();v();m();var{Buffer:mi}=(he(),Q(ye)),W0=65536,bd={},$0=mi.isBuffer(mi.from([1,2]).subarray(0,1));function yd(t){let e=mi.allocUnsafe(2);return e.writeUInt8(t>>8,0),e.writeUInt8(t&255,1),e}function H0(){for(let t=0;t<W0;t++)bd[t]=yd(t)}function V0(t){let r=0,i=0,n=mi.allocUnsafe(4);do r=t%128|0,t=t/128|0,t>0&&(r=r|128),n.writeUInt8(r,i++);while(t>0&&i<4);return t>0&&(i=0),$0?n.subarray(0,i):n.slice(0,i)}function z0(t){let e=mi.allocUnsafe(4);return e.writeUInt32BE(t,0),e}wd.exports={cache:bd,generateCache:H0,generateNumber:yd,genBufVariableByteInt:V0,generate4ByteBuffer:z0}});var md=O((ek,Do)=>{"use strict";_();v();m();typeof R>"u"||!R.version||R.version.indexOf("v0.")===0||R.version.indexOf("v1.")===0&&R.version.indexOf("v1.8.")!==0?Do.exports={nextTick:K0}:Do.exports=R;function K0(t,e,r,i){if(typeof t!="function")throw new TypeError('"callback" argument must be a function');var n=arguments.length,o,s;switch(n){case 0:case 1:return R.nextTick(t);case 2:return R.nextTick(function(){t.call(null,e)});case 3:return R.nextTick(function(){t.call(null,e,r)});case 4:return R.nextTick(function(){t.call(null,e,r,i)});default:for(o=new Array(n-1),s=0;s<o.length;)o[s++]=arguments[s];return R.nextTick(function(){t.apply(null,o)})}}});var Wo=O((ak,Cd)=>{_();v();m();var D=Uo(),{Buffer:U}=(he(),Q(ye)),G0=U.allocUnsafe(0),Q0=U.from([0]),vi=_d(),Y0=md().nextTick,qe=nt()("mqtt-packet:writeToStream"),In=vi.cache,J0=vi.generateNumber,X0=vi.generateCache,jo=vi.genBufVariableByteInt,Z0=vi.generate4ByteBuffer,Te=Fo,Tn=!0;function Td(t,e,r){switch(qe("generate called"),e.cork&&(e.cork(),Y0(ev,e)),Tn&&(Tn=!1,X0()),qe("generate: packet.cmd: %s",t.cmd),t.cmd){case"connect":return tv(t,e,r);case"connack":return rv(t,e,r);case"publish":return iv(t,e,r);case"puback":case"pubrec":case"pubrel":case"pubcomp":return nv(t,e,r);case"subscribe":return sv(t,e,r);case"suback":return ov(t,e,r);case"unsubscribe":return av(t,e,r);case"unsuback":return lv(t,e,r);case"pingreq":case"pingresp":return uv(t,e,r);case"disconnect":return cv(t,e,r);case"auth":return fv(t,e,r);default:return e.destroy(new Error("Unknown command")),!1}}Object.defineProperty(Td,"cacheNumbers",{get(){return Te===Fo},set(t){t?((!In||Object.keys(In).length===0)&&(Tn=!0),Te=Fo):(Tn=!1,Te=hv)}});function ev(t){t.uncork()}function tv(t,e,r){let i=t||{},n=i.protocolId||"MQTT",o=i.protocolVersion||4,s=i.will,a=i.clean,u=i.keepalive||0,f=i.clientId||"",d=i.username,h=i.password,g=i.properties;a===void 0&&(a=!0);let b=0;if(!n||typeof n!="string"&&!U.isBuffer(n))return e.destroy(new Error("Invalid protocolId")),!1;if(b+=n.length+2,o!==3&&o!==4&&o!==5)return e.destroy(new Error("Invalid protocol version")),!1;if(b+=1,(typeof f=="string"||U.isBuffer(f))&&(f||o>=4)&&(f||a))b+=U.byteLength(f)+2;else{if(o<4)return e.destroy(new Error("clientId must be supplied before 3.1.1")),!1;if(a*1===0)return e.destroy(new Error("clientId must be given if cleanSession set to 0")),!1}if(typeof u!="number"||u<0||u>65535||u%1!==0)return e.destroy(new Error("Invalid keepalive")),!1;b+=2,b+=1;let E,w;if(o===5){if(E=jt(e,g),!E)return!1;b+=E.length}if(s){if(typeof s!="object")return e.destroy(new Error("Invalid will")),!1;if(!s.topic||typeof s.topic!="string")return e.destroy(new Error("Invalid will topic")),!1;if(b+=U.byteLength(s.topic)+2,b+=2,s.payload)if(s.payload.length>=0)typeof s.payload=="string"?b+=U.byteLength(s.payload):b+=s.payload.length;else return e.destroy(new Error("Invalid will payload")),!1;if(w={},o===5){if(w=jt(e,s.properties),!w)return!1;b+=w.length}}let S=!1;if(d!=null)if(Id(d))S=!0,b+=U.byteLength(d)+2;else return e.destroy(new Error("Invalid username")),!1;if(h!=null){if(!S)return e.destroy(new Error("Username is required to use password")),!1;if(Id(h))b+=Rd(h)+2;else return e.destroy(new Error("Invalid password")),!1}e.write(D.CONNECT_HEADER),Ue(e,b),Vr(e,n),i.bridgeMode&&(o+=128),e.write(o===131?D.VERSION131:o===132?D.VERSION132:o===4?D.VERSION4:o===5?D.VERSION5:D.VERSION3);let I=0;return I|=d!=null?D.USERNAME_MASK:0,I|=h!=null?D.PASSWORD_MASK:0,I|=s&&s.retain?D.WILL_RETAIN_MASK:0,I|=s&&s.qos?s.qos<<D.WILL_QOS_SHIFT:0,I|=s?D.WILL_FLAG_MASK:0,I|=a?D.CLEAN_SESSION_MASK:0,e.write(U.from([I])),Te(e,u),o===5&&E.write(),Vr(e,f),s&&(o===5&&w.write(),ur(e,s.topic),Vr(e,s.payload)),d!=null&&Vr(e,d),h!=null&&Vr(e,h),!0}function rv(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=i===5?n.reasonCode:n.returnCode,s=n.properties,a=2;if(typeof o!="number")return e.destroy(new Error("Invalid return code")),!1;let u=null;if(i===5){if(u=jt(e,s),!u)return!1;a+=u.length}return e.write(D.CONNACK_HEADER),Ue(e,a),e.write(n.sessionPresent?D.SESSIONPRESENT_HEADER:Q0),e.write(U.from([o])),u?.write(),!0}function iv(t,e,r){qe("publish: packet: %o",t);let i=r?r.protocolVersion:4,n=t||{},o=n.qos||0,s=n.retain?D.RETAIN_MASK:0,a=n.topic,u=n.payload||G0,f=n.messageId,d=n.properties,h=0;if(typeof a=="string")h+=U.byteLength(a)+2;else if(U.isBuffer(a))h+=a.length+2;else return e.destroy(new Error("Invalid topic")),!1;if(U.isBuffer(u)?h+=u.length:h+=U.byteLength(u),o&&typeof f!="number")return e.destroy(new Error("Invalid messageId")),!1;o&&(h+=2);let g=null;if(i===5){if(g=jt(e,d),!g)return!1;h+=g.length}return e.write(D.PUBLISH_HEADER[o][n.dup?1:0][s?1:0]),Ue(e,h),Te(e,Rd(a)),e.write(a),o>0&&Te(e,f),g?.write(),qe("publish: payload: %o",u),e.write(u)}function nv(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.cmd||"puback",s=n.messageId,a=n.dup&&o==="pubrel"?D.DUP_MASK:0,u=0,f=n.reasonCode,d=n.properties,h=i===5?3:2;if(o==="pubrel"&&(u=1),typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;let g=null;if(i===5&&typeof d=="object"){if(g=Ei(e,d,r,h),!g)return!1;h+=g.length}return e.write(D.ACKS[o][u][a][0]),h===3&&(h+=f!==0?1:-1),Ue(e,h),Te(e,s),i===5&&h!==2&&e.write(U.from([f])),g!==null?g.write():h===4&&e.write(U.from([0])),!0}function sv(t,e,r){qe("subscribe: packet: ");let i=r?r.protocolVersion:4,n=t||{},o=n.dup?D.DUP_MASK:0,s=n.messageId,a=n.subscriptions,u=n.properties,f=0;if(typeof s!="number")return e.destroy(new Error("Invalid messageId")),!1;f+=2;let d=null;if(i===5){if(d=jt(e,u),!d)return!1;f+=d.length}if(typeof a=="object"&&a.length)for(let g=0;g<a.length;g+=1){let b=a[g].topic,E=a[g].qos;if(typeof b!="string")return e.destroy(new Error("Invalid subscriptions - invalid topic")),!1;if(typeof E!="number")return e.destroy(new Error("Invalid subscriptions - invalid qos")),!1;if(i===5){if(typeof(a[g].nl||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid No Local")),!1;if(typeof(a[g].rap||!1)!="boolean")return e.destroy(new Error("Invalid subscriptions - invalid Retain as Published")),!1;let I=a[g].rh||0;if(typeof I!="number"||I>2)return e.destroy(new Error("Invalid subscriptions - invalid Retain Handling")),!1}f+=U.byteLength(b)+2+1}else return e.destroy(new Error("Invalid subscriptions")),!1;qe("subscribe: writing to stream: %o",D.SUBSCRIBE_HEADER),e.write(D.SUBSCRIBE_HEADER[1][o?1:0][0]),Ue(e,f),Te(e,s),d!==null&&d.write();let h=!0;for(let g of a){let b=g.topic,E=g.qos,w=+g.nl,S=+g.rap,I=g.rh,P;ur(e,b),P=D.SUBSCRIBE_OPTIONS_QOS[E],i===5&&(P|=w?D.SUBSCRIBE_OPTIONS_NL:0,P|=S?D.SUBSCRIBE_OPTIONS_RAP:0,P|=I?D.SUBSCRIBE_OPTIONS_RH[I]:0),h=e.write(U.from([P]))}return h}function ov(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.granted,a=n.properties,u=0;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(u+=2,typeof s=="object"&&s.length)for(let d=0;d<s.length;d+=1){if(typeof s[d]!="number")return e.destroy(new Error("Invalid qos vector")),!1;u+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let f=null;if(i===5){if(f=Ei(e,a,r,u),!f)return!1;u+=f.length}return e.write(D.SUBACK_HEADER),Ue(e,u),Te(e,o),f!==null&&f.write(),e.write(U.from(s))}function av(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.dup?D.DUP_MASK:0,a=n.unsubscriptions,u=n.properties,f=0;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(f+=2,typeof a=="object"&&a.length)for(let g=0;g<a.length;g+=1){if(typeof a[g]!="string")return e.destroy(new Error("Invalid unsubscriptions")),!1;f+=U.byteLength(a[g])+2}else return e.destroy(new Error("Invalid unsubscriptions")),!1;let d=null;if(i===5){if(d=jt(e,u),!d)return!1;f+=d.length}e.write(D.UNSUBSCRIBE_HEADER[1][s?1:0][0]),Ue(e,f),Te(e,o),d!==null&&d.write();let h=!0;for(let g=0;g<a.length;g++)h=ur(e,a[g]);return h}function lv(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.messageId,s=n.dup?D.DUP_MASK:0,a=n.granted,u=n.properties,f=n.cmd,d=0,h=2;if(typeof o!="number")return e.destroy(new Error("Invalid messageId")),!1;if(i===5)if(typeof a=="object"&&a.length)for(let b=0;b<a.length;b+=1){if(typeof a[b]!="number")return e.destroy(new Error("Invalid qos vector")),!1;h+=1}else return e.destroy(new Error("Invalid qos vector")),!1;let g=null;if(i===5){if(g=Ei(e,u,r,h),!g)return!1;h+=g.length}return e.write(D.ACKS[f][d][s][0]),Ue(e,h),Te(e,o),g!==null&&g.write(),i===5&&e.write(U.from(a)),!0}function uv(t,e,r){return e.write(D.EMPTY[t.cmd])}function cv(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.reasonCode,s=n.properties,a=i===5?1:0,u=null;if(i===5){if(u=Ei(e,s,r,a),!u)return!1;a+=u.length}return e.write(U.from([D.codes.disconnect<<4])),Ue(e,a),i===5&&e.write(U.from([o])),u!==null&&u.write(),!0}function fv(t,e,r){let i=r?r.protocolVersion:4,n=t||{},o=n.reasonCode,s=n.properties,a=i===5?1:0;i!==5&&e.destroy(new Error("Invalid mqtt version for auth packet"));let u=Ei(e,s,r,a);return u?(a+=u.length,e.write(U.from([D.codes.auth<<4])),Ue(e,a),e.write(U.from([o])),u!==null&&u.write(),!0):!1}var vd={};function Ue(t,e){if(e>D.VARBYTEINT_MAX)return t.destroy(new Error(`Invalid variable byte integer: ${e}`)),!1;let r=vd[e];return r||(r=jo(e),e<16384&&(vd[e]=r)),qe("writeVarByteInt: writing to stream: %o",r),t.write(r)}function ur(t,e){let r=U.byteLength(e);return Te(t,r),qe("writeString: %s",e),t.write(e,"utf8")}function Ed(t,e,r){ur(t,e),ur(t,r)}function Fo(t,e){return qe("writeNumberCached: number: %d",e),qe("writeNumberCached: %o",In[e]),t.write(In[e])}function hv(t,e){let r=J0(e);return qe("writeNumberGenerated: %o",r),t.write(r)}function dv(t,e){let r=Z0(e);return qe("write4ByteNumber: %o",r),t.write(r)}function Vr(t,e){typeof e=="string"?ur(t,e):e?(Te(t,e.length),t.write(e)):Te(t,0)}function jt(t,e){if(typeof e!="object"||e.length!=null)return{length:1,write(){Ad(t,{},0)}};let r=0;function i(o,s){let a=D.propertiesTypes[o],u=0;switch(a){case"byte":{if(typeof s!="boolean")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=2;break}case"int8":{if(typeof s!="number"||s<0||s>255)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=2;break}case"binary":{if(s&&s===null)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+U.byteLength(s)+2;break}case"int16":{if(typeof s!="number"||s<0||s>65535)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=3;break}case"int32":{if(typeof s!="number"||s<0||s>4294967295)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=5;break}case"var":{if(typeof s!="number"||s<0||s>268435455)return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=1+U.byteLength(jo(s));break}case"string":{if(typeof s!="string")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=3+U.byteLength(s.toString());break}case"pair":{if(typeof s!="object")return t.destroy(new Error(`Invalid ${o}: ${s}`)),!1;u+=Object.getOwnPropertyNames(s).reduce((f,d)=>{let h=s[d];return Array.isArray(h)?f+=h.reduce((g,b)=>(g+=3+U.byteLength(d.toString())+2+U.byteLength(b.toString()),g),0):f+=3+U.byteLength(d.toString())+2+U.byteLength(s[d].toString()),f},0);break}default:return t.destroy(new Error(`Invalid property ${o}: ${s}`)),!1}return u}if(e)for(let o in e){let s=0,a=0,u=e[o];if(u!==void 0){if(Array.isArray(u))for(let f=0;f<u.length;f++){if(a=i(o,u[f]),!a)return!1;s+=a}else{if(a=i(o,u),!a)return!1;s=a}if(!s)return!1;r+=s}}return{length:U.byteLength(jo(r))+r,write(){Ad(t,e,r)}}}function Ei(t,e,r,i){let n=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0,s=jt(t,e);if(o)for(;i+s.length>o;){let a=n.shift();if(a&&e[a])delete e[a],s=jt(t,e);else return!1}return s}function Sd(t,e,r){switch(D.propertiesTypes[e]){case"byte":{t.write(U.from([D.properties[e]])),t.write(U.from([+r]));break}case"int8":{t.write(U.from([D.properties[e]])),t.write(U.from([r]));break}case"binary":{t.write(U.from([D.properties[e]])),Vr(t,r);break}case"int16":{t.write(U.from([D.properties[e]])),Te(t,r);break}case"int32":{t.write(U.from([D.properties[e]])),dv(t,r);break}case"var":{t.write(U.from([D.properties[e]])),Ue(t,r);break}case"string":{t.write(U.from([D.properties[e]])),ur(t,r);break}case"pair":{Object.getOwnPropertyNames(r).forEach(n=>{let o=r[n];Array.isArray(o)?o.forEach(s=>{t.write(U.from([D.properties[e]])),Ed(t,n.toString(),s.toString())}):(t.write(U.from([D.properties[e]])),Ed(t,n.toString(),o.toString()))});break}default:return t.destroy(new Error(`Invalid property ${e} value: ${r}`)),!1}}function Ad(t,e,r){Ue(t,r);for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&e[i]!=null){let n=e[i];if(Array.isArray(n))for(let o=0;o<n.length;o++)Sd(t,i,n[o]);else Sd(t,i,n)}}function Rd(t){return t?t instanceof U?t.length:U.byteLength(t):0}function Id(t){return typeof t=="string"||t instanceof U}Cd.exports=Td});var Bd=O((pk,kd)=>{_();v();m();var pv=Wo(),{EventEmitter:gv}=(Ot(),Q(xt)),{Buffer:Pd}=(he(),Q(ye));function bv(t,e){let r=new $o;return pv(t,r,e),r.concat()}var $o=class extends gv{constructor(){super(),this._array=new Array(20),this._i=0}write(e){return this._array[this._i++]=e,!0}concat(){let e=0,r=new Array(this._array.length),i=this._array,n=0,o;for(o=0;o<i.length&&i[o]!==void 0;o++)typeof i[o]!="string"?r[o]=i[o].length:r[o]=Pd.byteLength(i[o]),e+=r[o];let s=Pd.allocUnsafe(e);for(o=0;o<i.length&&i[o]!==void 0;o++)typeof i[o]!="string"?(i[o].copy(s,n),n+=r[o]):(s.write(i[o],n),n+=r[o]);return s}destroy(e){e&&this.emit("error",e)}};kd.exports=bv});var xd=O(Rn=>{_();v();m();Rn.parser=gd().parser;Rn.generate=Bd();Rn.writeToStream=Wo()});var zo=O(Vo=>{"use strict";_();v();m();Object.defineProperty(Vo,"__esModule",{value:!0});var Ho=class{constructor(){this.nextId=Math.max(1,Math.floor(Math.random()*65535))}allocate(){let e=this.nextId++;return this.nextId===65536&&(this.nextId=1),e}getLastAllocated(){return this.nextId===1?65535:this.nextId-1}register(e){return!0}deallocate(e){}clear(){}};Vo.default=Ho});var Md=O((Lk,Od)=>{"use strict";_();v();m();Od.exports=yv;function zr(t){return t instanceof x?x.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function yv(t){if(t=t||{},t.circles)return wv(t);let e=new Map;if(e.set(Date,s=>new Date(s)),e.set(Map,(s,a)=>new Map(i(Array.from(s),a))),e.set(Set,(s,a)=>new Set(i(Array.from(s),a))),t.constructorHandlers)for(let s of t.constructorHandlers)e.set(s[0],s[1]);let r=null;return t.proto?o:n;function i(s,a){let u=Object.keys(s),f=new Array(u.length);for(let d=0;d<u.length;d++){let h=u[d],g=s[h];typeof g!="object"||g===null?f[h]=g:g.constructor!==Object&&(r=e.get(g.constructor))?f[h]=r(g,a):ArrayBuffer.isView(g)?f[h]=zr(g):f[h]=a(g)}return f}function n(s){if(typeof s!="object"||s===null)return s;if(Array.isArray(s))return i(s,n);if(s.constructor!==Object&&(r=e.get(s.constructor)))return r(s,n);let a={};for(let u in s){if(Object.hasOwnProperty.call(s,u)===!1)continue;let f=s[u];typeof f!="object"||f===null?a[u]=f:f.constructor!==Object&&(r=e.get(f.constructor))?a[u]=r(f,n):ArrayBuffer.isView(f)?a[u]=zr(f):a[u]=n(f)}return a}function o(s){if(typeof s!="object"||s===null)return s;if(Array.isArray(s))return i(s,o);if(s.constructor!==Object&&(r=e.get(s.constructor)))return r(s,o);let a={};for(let u in s){let f=s[u];typeof f!="object"||f===null?a[u]=f:f.constructor!==Object&&(r=e.get(f.constructor))?a[u]=r(f,o):ArrayBuffer.isView(f)?a[u]=zr(f):a[u]=o(f)}return a}}function wv(t){let e=[],r=[],i=new Map;if(i.set(Date,u=>new Date(u)),i.set(Map,(u,f)=>new Map(o(Array.from(u),f))),i.set(Set,(u,f)=>new Set(o(Array.from(u),f))),t.constructorHandlers)for(let u of t.constructorHandlers)i.set(u[0],u[1]);let n=null;return t.proto?a:s;function o(u,f){let d=Object.keys(u),h=new Array(d.length);for(let g=0;g<d.length;g++){let b=d[g],E=u[b];if(typeof E!="object"||E===null)h[b]=E;else if(E.constructor!==Object&&(n=i.get(E.constructor)))h[b]=n(E,f);else if(ArrayBuffer.isView(E))h[b]=zr(E);else{let w=e.indexOf(E);w!==-1?h[b]=r[w]:h[b]=f(E)}}return h}function s(u){if(typeof u!="object"||u===null)return u;if(Array.isArray(u))return o(u,s);if(u.constructor!==Object&&(n=i.get(u.constructor)))return n(u,s);let f={};e.push(u),r.push(f);for(let d in u){if(Object.hasOwnProperty.call(u,d)===!1)continue;let h=u[d];if(typeof h!="object"||h===null)f[d]=h;else if(h.constructor!==Object&&(n=i.get(h.constructor)))f[d]=n(h,s);else if(ArrayBuffer.isView(h))f[d]=zr(h);else{let g=e.indexOf(h);g!==-1?f[d]=r[g]:f[d]=s(h)}}return e.pop(),r.pop(),f}function a(u){if(typeof u!="object"||u===null)return u;if(Array.isArray(u))return o(u,a);if(u.constructor!==Object&&(n=i.get(u.constructor)))return n(u,a);let f={};e.push(u),r.push(f);for(let d in u){let h=u[d];if(typeof h!="object"||h===null)f[d]=h;else if(h.constructor!==Object&&(n=i.get(h.constructor)))f[d]=n(h,a);else if(ArrayBuffer.isView(h))f[d]=zr(h);else{let g=e.indexOf(h);g!==-1?f[d]=r[g]:f[d]=a(h)}}return e.pop(),r.pop(),f}}});var qd=O((Wk,Ld)=>{"use strict";_();v();m();Ld.exports=Md()()});var Nd=O(Cn=>{"use strict";_();v();m();Object.defineProperty(Cn,"__esModule",{value:!0});Cn.validateTopic=Ud;Cn.validateTopics=_v;function Ud(t){let e=t.split("/");for(let r=0;r<e.length;r++)if(e[r]!=="+"){if(e[r]==="#")return r===e.length-1;if(e[r].indexOf("+")!==-1||e[r].indexOf("#")!==-1)return!1}return!0}function _v(t){if(t.length===0)return"empty_topic_list";for(let e=0;e<t.length;e++)if(!Ud(t[e]))return t[e];return null}});var Qo=O(Go=>{"use strict";_();v();m();Object.defineProperty(Go,"__esModule",{value:!0});var mv=Nt(),vv={objectMode:!0},Ev={clean:!0},Ko=class{constructor(e){this.options=e||{},this.options=Object.assign(Object.assign({},Ev),e),this._inflights=new Map}put(e,r){return this._inflights.set(e.messageId,e),r&&r(),this}createStream(){let e=new mv.Readable(vv),r=[],i=!1,n=0;return this._inflights.forEach((o,s)=>{r.push(o)}),e._read=()=>{!i&&n<r.length?e.push(r[n++]):e.push(null)},e.destroy=o=>{if(!i)return i=!0,setTimeout(()=>{e.emit("close")},0),e},e}del(e,r){let i=this._inflights.get(e.messageId);return i?(this._inflights.delete(e.messageId),r(null,i)):r&&r(new Error("missing packet")),this}get(e,r){let i=this._inflights.get(e.messageId);return i?r(null,i):r&&r(new Error("missing packet")),this}close(e){this.options.clean&&(this._inflights=null),e&&e()}};Go.default=Ko});var jd=O(Yo=>{"use strict";_();v();m();Object.defineProperty(Yo,"__esModule",{value:!0});var Dd=[0,16,128,131,135,144,145,151,153],Sv=(t,e,r)=>{t.log("handlePublish: packet %o",e),r=typeof r<"u"?r:t.noop;let i=e.topic.toString(),n=e.payload,{qos:o}=e,{messageId:s}=e,{options:a}=t;if(t.options.protocolVersion===5){let u;if(e.properties&&(u=e.properties.topicAlias),typeof u<"u")if(i.length===0)if(u>0&&u<=65535){let f=t.topicAliasRecv.getTopicByAlias(u);if(f)i=f,t.log("handlePublish :: topic complemented by alias. topic: %s - alias: %d",i,u);else{t.log("handlePublish :: unregistered topic alias. alias: %d",u),t.emit("error",new Error("Received unregistered Topic Alias"));return}}else{t.log("handlePublish :: topic alias out of range. alias: %d",u),t.emit("error",new Error("Received Topic Alias is out of range"));return}else if(t.topicAliasRecv.put(i,u))t.log("handlePublish :: registered topic: %s - alias: %d",i,u);else{t.log("handlePublish :: topic alias out of range. alias: %d",u),t.emit("error",new Error("Received Topic Alias is out of range"));return}}switch(t.log("handlePublish: qos %d",o),o){case 2:{a.customHandleAcks(i,n,e,(u,f)=>{if(typeof u=="number"&&(f=u,u=null),u)return t.emit("error",u);if(Dd.indexOf(f)===-1)return t.emit("error",new Error("Wrong reason code for pubrec"));f?t._sendPacket({cmd:"pubrec",messageId:s,reasonCode:f},r):t.incomingStore.put(e,()=>{t._sendPacket({cmd:"pubrec",messageId:s},r)})});break}case 1:{a.customHandleAcks(i,n,e,(u,f)=>{if(typeof u=="number"&&(f=u,u=null),u)return t.emit("error",u);if(Dd.indexOf(f)===-1)return t.emit("error",new Error("Wrong reason code for puback"));f||t.emit("message",i,n,e),t.handleMessage(e,d=>{if(d)return r&&r(d);t._sendPacket({cmd:"puback",messageId:s,reasonCode:f},r)})});break}case 0:t.emit("message",i,n,e),t.handleMessage(e,r);break;default:t.log("handlePublish: unknown QoS. Doing nothing.");break}};Yo.default=Sv});var Fd=O((bB,Av)=>{Av.exports={version:"5.13.1"}});var cr=O(Ve=>{"use strict";_();v();m();Object.defineProperty(Ve,"__esModule",{value:!0});Ve.MQTTJS_VERSION=Ve.nextTick=Ve.ErrorWithSubackPacket=Ve.ErrorWithReasonCode=void 0;Ve.applyMixin=Iv;var Jo=class t extends Error{constructor(e,r){super(e),this.code=r,Object.setPrototypeOf(this,t.prototype),Object.getPrototypeOf(this).name="ErrorWithReasonCode"}};Ve.ErrorWithReasonCode=Jo;var Xo=class t extends Error{constructor(e,r){super(e),this.packet=r,Object.setPrototypeOf(this,t.prototype),Object.getPrototypeOf(this).name="ErrorWithSubackPacket"}};Ve.ErrorWithSubackPacket=Xo;function Iv(t,e,r=!1){var i;let n=[e];for(;;){let o=n[0],s=Object.getPrototypeOf(o);if(s?.prototype)n.unshift(s);else break}for(let o of n)for(let s of Object.getOwnPropertyNames(o.prototype))(r||s!=="constructor")&&Object.defineProperty(t.prototype,s,(i=Object.getOwnPropertyDescriptor(o.prototype,s))!==null&&i!==void 0?i:Object.create(null))}Ve.nextTick=typeof(R===null||R===void 0?void 0:R.nextTick)=="function"?R.nextTick:t=>{setTimeout(t,0)};Ve.MQTTJS_VERSION=Fd().version});var Si=O(Ft=>{"use strict";_();v();m();Object.defineProperty(Ft,"__esModule",{value:!0});Ft.ReasonCodes=void 0;var Wd=cr();Ft.ReasonCodes={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};var Tv=(t,e)=>{let{messageId:r}=e,i=e.cmd,n=null,o=t.outgoing[r]?t.outgoing[r].cb:null,s=null;if(!o){t.log("_handleAck :: Server sent an ack in error. Ignoring.");return}switch(t.log("_handleAck :: packet type",i),i){case"pubcomp":case"puback":{let a=e.reasonCode;a&&a>0&&a!==16?(s=new Wd.ErrorWithReasonCode(`Publish error: ${Ft.ReasonCodes[a]}`,a),t._removeOutgoingAndStoreMessage(r,()=>{o(s,e)})):t._removeOutgoingAndStoreMessage(r,o);break}case"pubrec":{n={cmd:"pubrel",qos:2,messageId:r};let a=e.reasonCode;a&&a>0&&a!==16?(s=new Wd.ErrorWithReasonCode(`Publish error: ${Ft.ReasonCodes[a]}`,a),t._removeOutgoingAndStoreMessage(r,()=>{o(s,e)})):t._sendPacket(n);break}case"suback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r);let a=e.granted;for(let u=0;u<a.length;u++){let f=a[u];if((f&128)!==0){s=new Error(`Subscribe error: ${Ft.ReasonCodes[f]}`),s.code=f;let d=t.messageIdToTopic[r];d&&d.forEach(h=>{delete t._resubscribeTopics[h]})}}delete t.messageIdToTopic[r],t._invokeStoreProcessingQueue(),o(s,e);break}case"unsuback":{delete t.outgoing[r],t.messageIdProvider.deallocate(r),t._invokeStoreProcessingQueue(),o(null,e);break}default:t.emit("error",new Error("unrecognized packet type"))}t.disconnecting&&Object.keys(t.outgoing).length===0&&t.emit("outgoingEmpty")};Ft.default=Tv});var Hd=O(Zo=>{"use strict";_();v();m();Object.defineProperty(Zo,"__esModule",{value:!0});var $d=cr(),Rv=Si(),Cv=(t,e)=>{let{options:r}=t,i=r.protocolVersion,n=i===5?e.reasonCode:e.returnCode;if(i!==5){let o=new $d.ErrorWithReasonCode(`Protocol error: Auth packets are only supported in MQTT 5. Your version:${i}`,n);t.emit("error",o);return}t.handleAuth(e,(o,s)=>{if(o){t.emit("error",o);return}if(n===24)t.reconnecting=!1,t._sendPacket(s);else{let a=new $d.ErrorWithReasonCode(`Connection refused: ${Rv.ReasonCodes[n]}`,n);t.emit("error",a)}})};Zo.default=Cv});var Qd=O(kn=>{"use strict";_();v();m();Object.defineProperty(kn,"__esModule",{value:!0});kn.LRUCache=void 0;var Kr=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,zd=new Set,ea=typeof R=="object"&&R?R:{},Kd=(t,e,r,i)=>{typeof ea.emitWarning=="function"?ea.emitWarning(t,e,r,i):console.error(`[${r}] ${e}: ${t}`)},Pn=globalThis.AbortController,Vd=globalThis.AbortSignal;if(typeof Pn>"u"){Vd=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(i,n){this._onabort.push(n)}},Pn=class{constructor(){e()}signal=new Vd;abort(i){if(!this.signal.aborted){this.signal.reason=i,this.signal.aborted=!0;for(let n of this.signal._onabort)n(i);this.signal.onabort?.(i)}}};let t=ea.env?.LRU_CACHE_IGNORE_AC_WARNING!=="1",e=()=>{t&&(t=!1,Kd("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}var Pv=t=>!zd.has(t),jB=Symbol("type"),Wt=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),Gd=t=>Wt(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?Gr:null:null,Gr=class extends Array{constructor(e){super(e),this.fill(0)}},ta=class t{heap;length;static#l=!1;static create(e){let r=Gd(e);if(!r)return[];t.#l=!0;let i=new t(e,r);return t.#l=!1,i}constructor(e,r){if(!t.#l)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new r(e),this.length=0}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}},ra=class t{#l;#f;#g;#b;#k;#B;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#n;#y;#i;#r;#e;#u;#h;#a;#s;#w;#o;#_;#m;#d;#v;#T;#c;static unsafeExposeInternals(e){return{starts:e.#m,ttls:e.#d,sizes:e.#_,keyMap:e.#i,keyList:e.#r,valList:e.#e,next:e.#u,prev:e.#h,get head(){return e.#a},get tail(){return e.#s},free:e.#w,isBackgroundFetch:r=>e.#t(r),backgroundFetch:(r,i,n,o)=>e.#M(r,i,n,o),moveToTail:r=>e.#P(r),indexes:r=>e.#E(r),rindexes:r=>e.#S(r),isStale:r=>e.#p(r)}}get max(){return this.#l}get maxSize(){return this.#f}get calculatedSize(){return this.#y}get size(){return this.#n}get fetchMethod(){return this.#k}get memoMethod(){return this.#B}get dispose(){return this.#g}get disposeAfter(){return this.#b}constructor(e){let{max:r=0,ttl:i,ttlResolution:n=1,ttlAutopurge:o,updateAgeOnGet:s,updateAgeOnHas:a,allowStale:u,dispose:f,disposeAfter:d,noDisposeOnSet:h,noUpdateTTL:g,maxSize:b=0,maxEntrySize:E=0,sizeCalculation:w,fetchMethod:S,memoMethod:I,noDeleteOnFetchRejection:P,noDeleteOnStaleGet:C,allowStaleOnFetchRejection:M,allowStaleOnFetchAbort:q,ignoreFetchAbort:z}=e;if(r!==0&&!Wt(r))throw new TypeError("max option must be a nonnegative integer");let j=r?Gd(r):Array;if(!j)throw new Error("invalid max value: "+r);if(this.#l=r,this.#f=b,this.maxEntrySize=E||this.#f,this.sizeCalculation=w,this.sizeCalculation){if(!this.#f&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(I!==void 0&&typeof I!="function")throw new TypeError("memoMethod must be a function if defined");if(this.#B=I,S!==void 0&&typeof S!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#k=S,this.#T=!!S,this.#i=new Map,this.#r=new Array(r).fill(void 0),this.#e=new Array(r).fill(void 0),this.#u=new j(r),this.#h=new j(r),this.#a=0,this.#s=0,this.#w=ta.create(r),this.#n=0,this.#y=0,typeof f=="function"&&(this.#g=f),typeof d=="function"?(this.#b=d,this.#o=[]):(this.#b=void 0,this.#o=void 0),this.#v=!!this.#g,this.#c=!!this.#b,this.noDisposeOnSet=!!h,this.noUpdateTTL=!!g,this.noDeleteOnFetchRejection=!!P,this.allowStaleOnFetchRejection=!!M,this.allowStaleOnFetchAbort=!!q,this.ignoreFetchAbort=!!z,this.maxEntrySize!==0){if(this.#f!==0&&!Wt(this.#f))throw new TypeError("maxSize must be a positive integer if specified");if(!Wt(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#F()}if(this.allowStale=!!u,this.noDeleteOnStaleGet=!!C,this.updateAgeOnGet=!!s,this.updateAgeOnHas=!!a,this.ttlResolution=Wt(n)||n===0?n:1,this.ttlAutopurge=!!o,this.ttl=i||0,this.ttl){if(!Wt(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#L()}if(this.#l===0&&this.ttl===0&&this.#f===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#l&&!this.#f){let G="LRU_CACHE_UNBOUNDED";Pv(G)&&(zd.add(G),Kd("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",G,t))}}getRemainingTTL(e){return this.#i.has(e)?1/0:0}#L(){let e=new Gr(this.#l),r=new Gr(this.#l);this.#d=e,this.#m=r,this.#q=(o,s,a=Kr.now())=>{if(r[o]=s!==0?a:0,e[o]=s,s!==0&&this.ttlAutopurge){let u=setTimeout(()=>{this.#p(o)&&this.#A(this.#r[o],"expire")},s+1);u.unref&&u.unref()}},this.#R=o=>{r[o]=e[o]!==0?Kr.now():0},this.#I=(o,s)=>{if(e[s]){let a=e[s],u=r[s];if(!a||!u)return;o.ttl=a,o.start=u,o.now=i||n();let f=o.now-u;o.remainingTTL=a-f}};let i=0,n=()=>{let o=Kr.now();if(this.ttlResolution>0){i=o;let s=setTimeout(()=>i=0,this.ttlResolution);s.unref&&s.unref()}return o};this.getRemainingTTL=o=>{let s=this.#i.get(o);if(s===void 0)return 0;let a=e[s],u=r[s];if(!a||!u)return 1/0;let f=(i||n())-u;return a-f},this.#p=o=>{let s=r[o],a=e[o];return!!a&&!!s&&(i||n())-s>a}}#R=()=>{};#I=()=>{};#q=()=>{};#p=()=>!1;#F(){let e=new Gr(this.#l);this.#y=0,this.#_=e,this.#C=r=>{this.#y-=e[r],e[r]=0},this.#U=(r,i,n,o)=>{if(this.#t(i))return 0;if(!Wt(n))if(o){if(typeof o!="function")throw new TypeError("sizeCalculation must be a function");if(n=o(i,r),!Wt(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return n},this.#x=(r,i,n)=>{if(e[r]=i,this.#f){let o=this.#f-e[r];for(;this.#y>o;)this.#O(!0)}this.#y+=e[r],n&&(n.entrySize=i,n.totalCalculatedSize=this.#y)}}#C=e=>{};#x=(e,r,i)=>{};#U=(e,r,i,n)=>{if(i||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#E({allowStale:e=this.allowStale}={}){if(this.#n)for(let r=this.#s;!(!this.#N(r)||((e||!this.#p(r))&&(yield r),r===this.#a));)r=this.#h[r]}*#S({allowStale:e=this.allowStale}={}){if(this.#n)for(let r=this.#a;!(!this.#N(r)||((e||!this.#p(r))&&(yield r),r===this.#s));)r=this.#u[r]}#N(e){return e!==void 0&&this.#i.get(this.#r[e])===e}*entries(){for(let e of this.#E())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*rentries(){for(let e of this.#S())this.#e[e]!==void 0&&this.#r[e]!==void 0&&!this.#t(this.#e[e])&&(yield[this.#r[e],this.#e[e]])}*keys(){for(let e of this.#E()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*rkeys(){for(let e of this.#S()){let r=this.#r[e];r!==void 0&&!this.#t(this.#e[e])&&(yield r)}}*values(){for(let e of this.#E())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}*rvalues(){for(let e of this.#S())this.#e[e]!==void 0&&!this.#t(this.#e[e])&&(yield this.#e[e])}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(e,r={}){for(let i of this.#E()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;if(o!==void 0&&e(o,this.#r[i],this))return this.get(this.#r[i],r)}}forEach(e,r=this){for(let i of this.#E()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;o!==void 0&&e.call(r,o,this.#r[i],this)}}rforEach(e,r=this){for(let i of this.#S()){let n=this.#e[i],o=this.#t(n)?n.__staleWhileFetching:n;o!==void 0&&e.call(r,o,this.#r[i],this)}}purgeStale(){let e=!1;for(let r of this.#S({allowStale:!0}))this.#p(r)&&(this.#A(this.#r[r],"expire"),e=!0);return e}info(e){let r=this.#i.get(e);if(r===void 0)return;let i=this.#e[r],n=this.#t(i)?i.__staleWhileFetching:i;if(n===void 0)return;let o={value:n};if(this.#d&&this.#m){let s=this.#d[r],a=this.#m[r];if(s&&a){let u=s-(Kr.now()-a);o.ttl=u,o.start=Date.now()}}return this.#_&&(o.size=this.#_[r]),o}dump(){let e=[];for(let r of this.#E({allowStale:!0})){let i=this.#r[r],n=this.#e[r],o=this.#t(n)?n.__staleWhileFetching:n;if(o===void 0||i===void 0)continue;let s={value:o};if(this.#d&&this.#m){s.ttl=this.#d[r];let a=Kr.now()-this.#m[r];s.start=Math.floor(Date.now()-a)}this.#_&&(s.size=this.#_[r]),e.unshift([i,s])}return e}load(e){this.clear();for(let[r,i]of e){if(i.start){let n=Date.now()-i.start;i.start=Kr.now()-n}this.set(r,i.value,i)}}set(e,r,i={}){if(r===void 0)return this.delete(e),this;let{ttl:n=this.ttl,start:o,noDisposeOnSet:s=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:u}=i,{noUpdateTTL:f=this.noUpdateTTL}=i,d=this.#U(e,r,i.size||0,a);if(this.maxEntrySize&&d>this.maxEntrySize)return u&&(u.set="miss",u.maxEntrySizeExceeded=!0),this.#A(e,"set"),this;let h=this.#n===0?void 0:this.#i.get(e);if(h===void 0)h=this.#n===0?this.#s:this.#w.length!==0?this.#w.pop():this.#n===this.#l?this.#O(!1):this.#n,this.#r[h]=e,this.#e[h]=r,this.#i.set(e,h),this.#u[this.#s]=h,this.#h[h]=this.#s,this.#s=h,this.#n++,this.#x(h,d,u),u&&(u.set="add"),f=!1;else{this.#P(h);let g=this.#e[h];if(r!==g){if(this.#T&&this.#t(g)){g.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:b}=g;b!==void 0&&!s&&(this.#v&&this.#g?.(b,e,"set"),this.#c&&this.#o?.push([b,e,"set"]))}else s||(this.#v&&this.#g?.(g,e,"set"),this.#c&&this.#o?.push([g,e,"set"]));if(this.#C(h),this.#x(h,d,u),this.#e[h]=r,u){u.set="replace";let b=g&&this.#t(g)?g.__staleWhileFetching:g;b!==void 0&&(u.oldValue=b)}}else u&&(u.set="update")}if(n!==0&&!this.#d&&this.#L(),this.#d&&(f||this.#q(h,n,o),u&&this.#I(u,h)),!s&&this.#c&&this.#o){let g=this.#o,b;for(;b=g?.shift();)this.#b?.(...b)}return this}pop(){try{for(;this.#n;){let e=this.#e[this.#a];if(this.#O(!0),this.#t(e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(this.#c&&this.#o){let e=this.#o,r;for(;r=e?.shift();)this.#b?.(...r)}}}#O(e){let r=this.#a,i=this.#r[r],n=this.#e[r];return this.#T&&this.#t(n)?n.__abortController.abort(new Error("evicted")):(this.#v||this.#c)&&(this.#v&&this.#g?.(n,i,"evict"),this.#c&&this.#o?.push([n,i,"evict"])),this.#C(r),e&&(this.#r[r]=void 0,this.#e[r]=void 0,this.#w.push(r)),this.#n===1?(this.#a=this.#s=0,this.#w.length=0):this.#a=this.#u[r],this.#i.delete(i),this.#n--,r}has(e,r={}){let{updateAgeOnHas:i=this.updateAgeOnHas,status:n}=r,o=this.#i.get(e);if(o!==void 0){let s=this.#e[o];if(this.#t(s)&&s.__staleWhileFetching===void 0)return!1;if(this.#p(o))n&&(n.has="stale",this.#I(n,o));else return i&&this.#R(o),n&&(n.has="hit",this.#I(n,o)),!0}else n&&(n.has="miss");return!1}peek(e,r={}){let{allowStale:i=this.allowStale}=r,n=this.#i.get(e);if(n===void 0||!i&&this.#p(n))return;let o=this.#e[n];return this.#t(o)?o.__staleWhileFetching:o}#M(e,r,i,n){let o=r===void 0?void 0:this.#e[r];if(this.#t(o))return o;let s=new Pn,{signal:a}=i;a?.addEventListener("abort",()=>s.abort(a.reason),{signal:s.signal});let u={signal:s.signal,options:i,context:n},f=(w,S=!1)=>{let{aborted:I}=s.signal,P=i.ignoreFetchAbort&&w!==void 0;if(i.status&&(I&&!S?(i.status.fetchAborted=!0,i.status.fetchError=s.signal.reason,P&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),I&&!P&&!S)return h(s.signal.reason);let C=b;return this.#e[r]===b&&(w===void 0?C.__staleWhileFetching?this.#e[r]=C.__staleWhileFetching:this.#A(e,"fetch"):(i.status&&(i.status.fetchUpdated=!0),this.set(e,w,u.options))),w},d=w=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=w),h(w)),h=w=>{let{aborted:S}=s.signal,I=S&&i.allowStaleOnFetchAbort,P=I||i.allowStaleOnFetchRejection,C=P||i.noDeleteOnFetchRejection,M=b;if(this.#e[r]===b&&(!C||M.__staleWhileFetching===void 0?this.#A(e,"fetch"):I||(this.#e[r]=M.__staleWhileFetching)),P)return i.status&&M.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),M.__staleWhileFetching;if(M.__returned===M)throw w},g=(w,S)=>{let I=this.#k?.(e,o,u);I&&I instanceof Promise&&I.then(P=>w(P===void 0?void 0:P),S),s.signal.addEventListener("abort",()=>{(!i.ignoreFetchAbort||i.allowStaleOnFetchAbort)&&(w(void 0),i.allowStaleOnFetchAbort&&(w=P=>f(P,!0)))})};i.status&&(i.status.fetchDispatched=!0);let b=new Promise(g).then(f,d),E=Object.assign(b,{__abortController:s,__staleWhileFetching:o,__returned:void 0});return r===void 0?(this.set(e,E,{...u.options,status:void 0}),r=this.#i.get(e)):this.#e[r]=E,E}#t(e){if(!this.#T)return!1;let r=e;return!!r&&r instanceof Promise&&r.hasOwnProperty("__staleWhileFetching")&&r.__abortController instanceof Pn}async fetch(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,ttl:s=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:u=0,sizeCalculation:f=this.sizeCalculation,noUpdateTTL:d=this.noUpdateTTL,noDeleteOnFetchRejection:h=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:g=this.allowStaleOnFetchRejection,ignoreFetchAbort:b=this.ignoreFetchAbort,allowStaleOnFetchAbort:E=this.allowStaleOnFetchAbort,context:w,forceRefresh:S=!1,status:I,signal:P}=r;if(!this.#T)return I&&(I.fetch="get"),this.get(e,{allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:o,status:I});let C={allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:o,ttl:s,noDisposeOnSet:a,size:u,sizeCalculation:f,noUpdateTTL:d,noDeleteOnFetchRejection:h,allowStaleOnFetchRejection:g,allowStaleOnFetchAbort:E,ignoreFetchAbort:b,status:I,signal:P},M=this.#i.get(e);if(M===void 0){I&&(I.fetch="miss");let q=this.#M(e,M,C,w);return q.__returned=q}else{let q=this.#e[M];if(this.#t(q)){let te=i&&q.__staleWhileFetching!==void 0;return I&&(I.fetch="inflight",te&&(I.returnedStale=!0)),te?q.__staleWhileFetching:q.__returned=q}let z=this.#p(M);if(!S&&!z)return I&&(I.fetch="hit"),this.#P(M),n&&this.#R(M),I&&this.#I(I,M),q;let j=this.#M(e,M,C,w),$=j.__staleWhileFetching!==void 0&&i;return I&&(I.fetch=z?"stale":"refresh",$&&z&&(I.returnedStale=!0)),$?j.__staleWhileFetching:j.__returned=j}}async forceFetch(e,r={}){let i=await this.fetch(e,r);if(i===void 0)throw new Error("fetch() returned undefined");return i}memo(e,r={}){let i=this.#B;if(!i)throw new Error("no memoMethod provided to constructor");let{context:n,forceRefresh:o,...s}=r,a=this.get(e,s);if(!o&&a!==void 0)return a;let u=i(e,a,{options:s,context:n});return this.set(e,u,s),u}get(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:o=this.noDeleteOnStaleGet,status:s}=r,a=this.#i.get(e);if(a!==void 0){let u=this.#e[a],f=this.#t(u);return s&&this.#I(s,a),this.#p(a)?(s&&(s.get="stale"),f?(s&&i&&u.__staleWhileFetching!==void 0&&(s.returnedStale=!0),i?u.__staleWhileFetching:void 0):(o||this.#A(e,"expire"),s&&i&&(s.returnedStale=!0),i?u:void 0)):(s&&(s.get="hit"),f?u.__staleWhileFetching:(this.#P(a),n&&this.#R(a),u))}else s&&(s.get="miss")}#D(e,r){this.#h[r]=e,this.#u[e]=r}#P(e){e!==this.#s&&(e===this.#a?this.#a=this.#u[e]:this.#D(this.#h[e],this.#u[e]),this.#D(this.#s,e),this.#s=e)}delete(e){return this.#A(e,"delete")}#A(e,r){let i=!1;if(this.#n!==0){let n=this.#i.get(e);if(n!==void 0)if(i=!0,this.#n===1)this.#j(r);else{this.#C(n);let o=this.#e[n];if(this.#t(o)?o.__abortController.abort(new Error("deleted")):(this.#v||this.#c)&&(this.#v&&this.#g?.(o,e,r),this.#c&&this.#o?.push([o,e,r])),this.#i.delete(e),this.#r[n]=void 0,this.#e[n]=void 0,n===this.#s)this.#s=this.#h[n];else if(n===this.#a)this.#a=this.#u[n];else{let s=this.#h[n];this.#u[s]=this.#u[n];let a=this.#u[n];this.#h[a]=this.#h[n]}this.#n--,this.#w.push(n)}}if(this.#c&&this.#o?.length){let n=this.#o,o;for(;o=n?.shift();)this.#b?.(...o)}return i}clear(){return this.#j("delete")}#j(e){for(let r of this.#S({allowStale:!0})){let i=this.#e[r];if(this.#t(i))i.__abortController.abort(new Error("deleted"));else{let n=this.#r[r];this.#v&&this.#g?.(i,n,e),this.#c&&this.#o?.push([i,n,e])}}if(this.#i.clear(),this.#e.fill(void 0),this.#r.fill(void 0),this.#d&&this.#m&&(this.#d.fill(0),this.#m.fill(0)),this.#_&&this.#_.fill(0),this.#a=0,this.#s=0,this.#w.length=0,this.#y=0,this.#n=0,this.#c&&this.#o){let r=this.#o,i;for(;i=r?.shift();)this.#b?.(...i)}}};kn.LRUCache=ra});var st=O($t=>{"use strict";_();v();m();Object.defineProperty($t,"t",{value:!0});$t.ContainerIterator=$t.Container=$t.Base=void 0;var ia=class{constructor(e=0){this.iteratorType=e}equals(e){return this.o===e.o}};$t.ContainerIterator=ia;var Bn=class{constructor(){this.i=0}get length(){return this.i}size(){return this.i}empty(){return this.i===0}};$t.Base=Bn;var na=class extends Bn{};$t.Container=na});var Yd=O(xn=>{"use strict";_();v();m();Object.defineProperty(xn,"t",{value:!0});xn.default=void 0;var kv=st(),sa=class extends kv.Base{constructor(e=[]){super(),this.S=[];let r=this;e.forEach(function(i){r.push(i)})}clear(){this.i=0,this.S=[]}push(e){return this.S.push(e),this.i+=1,this.i}pop(){if(this.i!==0)return this.i-=1,this.S.pop()}top(){return this.S[this.i-1]}},Bv=sa;xn.default=Bv});var Jd=O(On=>{"use strict";_();v();m();Object.defineProperty(On,"t",{value:!0});On.default=void 0;var xv=st(),oa=class extends xv.Base{constructor(e=[]){super(),this.j=0,this.q=[];let r=this;e.forEach(function(i){r.push(i)})}clear(){this.q=[],this.i=this.j=0}push(e){let r=this.q.length;if(this.j/r>.5&&this.j+this.i>=r&&r>4096){let i=this.i;for(let n=0;n<i;++n)this.q[n]=this.q[this.j+n];this.j=0,this.q[this.i]=e}else this.q[this.j+this.i]=e;return++this.i}pop(){if(this.i===0)return;let e=this.q[this.j++];return this.i-=1,e}front(){if(this.i!==0)return this.q[this.j]}},Ov=oa;On.default=Ov});var Xd=O(Mn=>{"use strict";_();v();m();Object.defineProperty(Mn,"t",{value:!0});Mn.default=void 0;var Mv=st(),aa=class extends Mv.Base{constructor(e=[],r=function(n,o){return n>o?-1:n<o?1:0},i=!0){if(super(),this.v=r,Array.isArray(e))this.C=i?[...e]:e;else{this.C=[];let o=this;e.forEach(function(s){o.C.push(s)})}this.i=this.C.length;let n=this.i>>1;for(let o=this.i-1>>1;o>=0;--o)this.k(o,n)}m(e){let r=this.C[e];for(;e>0;){let i=e-1>>1,n=this.C[i];if(this.v(n,r)<=0)break;this.C[e]=n,e=i}this.C[e]=r}k(e,r){let i=this.C[e];for(;e<r;){let n=e<<1|1,o=n+1,s=this.C[n];if(o<this.i&&this.v(s,this.C[o])>0&&(n=o,s=this.C[o]),this.v(s,i)>=0)break;this.C[e]=s,e=n}this.C[e]=i}clear(){this.i=0,this.C.length=0}push(e){this.C.push(e),this.m(this.i),this.i+=1}pop(){if(this.i===0)return;let e=this.C[0],r=this.C.pop();return this.i-=1,this.i&&(this.C[0]=r,this.k(0,this.i>>1)),e}top(){return this.C[0]}find(e){return this.C.indexOf(e)>=0}remove(e){let r=this.C.indexOf(e);return r<0?!1:(r===0?this.pop():r===this.i-1?(this.C.pop(),this.i-=1):(this.C.splice(r,1,this.C.pop()),this.i-=1,this.m(r),this.k(r,this.i>>1)),!0)}updateItem(e){let r=this.C.indexOf(e);return r<0?!1:(this.m(r),this.k(r,this.i>>1),!0)}toArray(){return[...this.C]}},Lv=aa;Mn.default=Lv});var qn=O(Ln=>{"use strict";_();v();m();Object.defineProperty(Ln,"t",{value:!0});Ln.default=void 0;var qv=st(),la=class extends qv.Container{},Uv=la;Ln.default=Uv});var ot=O(ua=>{"use strict";_();v();m();Object.defineProperty(ua,"t",{value:!0});ua.throwIteratorAccessError=Nv;function Nv(){throw new RangeError("Iterator access denied!")}});var fa=O(Nn=>{"use strict";_();v();m();Object.defineProperty(Nn,"t",{value:!0});Nn.RandomIterator=void 0;var Dv=st(),Un=ot(),ca=class extends Dv.ContainerIterator{constructor(e,r){super(r),this.o=e,this.iteratorType===0?(this.pre=function(){return this.o===0&&(0,Un.throwIteratorAccessError)(),this.o-=1,this},this.next=function(){return this.o===this.container.size()&&(0,Un.throwIteratorAccessError)(),this.o+=1,this}):(this.pre=function(){return this.o===this.container.size()-1&&(0,Un.throwIteratorAccessError)(),this.o+=1,this},this.next=function(){return this.o===-1&&(0,Un.throwIteratorAccessError)(),this.o-=1,this})}get pointer(){return this.container.getElementByPos(this.o)}set pointer(e){this.container.setElementByPos(this.o,e)}};Nn.RandomIterator=ca});var Zd=O(Dn=>{"use strict";_();v();m();Object.defineProperty(Dn,"t",{value:!0});Dn.default=void 0;var jv=Wv(qn()),Fv=fa();function Wv(t){return t&&t.t?t:{default:t}}var fr=class t extends Fv.RandomIterator{constructor(e,r,i){super(e,i),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},ha=class extends jv.default{constructor(e=[],r=!0){if(super(),Array.isArray(e))this.J=r?[...e]:e,this.i=e.length;else{this.J=[];let i=this;e.forEach(function(n){i.pushBack(n)})}}clear(){this.i=0,this.J.length=0}begin(){return new fr(0,this)}end(){return new fr(this.i,this)}rBegin(){return new fr(this.i-1,this,1)}rEnd(){return new fr(-1,this,1)}front(){return this.J[0]}back(){return this.J[this.i-1]}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J[e]}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;return this.J.splice(e,1),this.i-=1,this.i}eraseElementByValue(e){let r=0;for(let i=0;i<this.i;++i)this.J[i]!==e&&(this.J[r++]=this.J[i]);return this.i=this.J.length=r,this.i}eraseElementByIterator(e){let r=e.o;return e=e.next(),this.eraseElementByPos(r),e}pushBack(e){return this.J.push(e),this.i+=1,this.i}popBack(){if(this.i!==0)return this.i-=1,this.J.pop()}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;this.J[e]=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;return this.J.splice(e,0,...new Array(i).fill(r)),this.i+=i,this.i}find(e){for(let r=0;r<this.i;++r)if(this.J[r]===e)return new fr(r,this);return this.end()}reverse(){this.J.reverse()}unique(){let e=1;for(let r=1;r<this.i;++r)this.J[r]!==this.J[r-1]&&(this.J[e++]=this.J[r]);return this.i=this.J.length=e,this.i}sort(e){this.J.sort(e)}forEach(e){for(let r=0;r<this.i;++r)e(this.J[r],r,this)}[Symbol.iterator](){return function*(){yield*this.J}.bind(this)()}},$v=ha;Dn.default=$v});var ep=O(jn=>{"use strict";_();v();m();Object.defineProperty(jn,"t",{value:!0});jn.default=void 0;var Hv=zv(qn()),Vv=st(),hr=ot();function zv(t){return t&&t.t?t:{default:t}}var dr=class t extends Vv.ContainerIterator{constructor(e,r,i,n){super(n),this.o=e,this.h=r,this.container=i,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,hr.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,hr.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,hr.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,hr.throwIteratorAccessError)(),this.o=this.o.L,this})}get pointer(){return this.o===this.h&&(0,hr.throwIteratorAccessError)(),this.o.l}set pointer(e){this.o===this.h&&(0,hr.throwIteratorAccessError)(),this.o.l=e}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},da=class extends Hv.default{constructor(e=[]){super(),this.h={},this.p=this._=this.h.L=this.h.B=this.h;let r=this;e.forEach(function(i){r.pushBack(i)})}V(e){let{L:r,B:i}=e;r.B=i,i.L=r,e===this.p&&(this.p=i),e===this._&&(this._=r),this.i-=1}G(e,r){let i=r.B,n={l:e,L:r,B:i};r.B=n,i.L=n,r===this.h&&(this.p=n),i===this.h&&(this._=n),this.i+=1}clear(){this.i=0,this.p=this._=this.h.L=this.h.B=this.h}begin(){return new dr(this.p,this.h,this)}end(){return new dr(this.h,this.h,this)}rBegin(){return new dr(this._,this.h,this,1)}rEnd(){return new dr(this.h,this.h,this,1)}front(){return this.p.l}back(){return this._.l}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.l}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}eraseElementByValue(e){let r=this.p;for(;r!==this.h;)r.l===e&&this.V(r),r=r.B;return this.i}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,hr.throwIteratorAccessError)(),e=e.next(),this.V(r),e}pushBack(e){return this.G(e,this._),this.i}popBack(){if(this.i===0)return;let e=this._.l;return this.V(this._),e}pushFront(e){return this.G(e,this.h),this.i}popFront(){if(this.i===0)return;let e=this.p.l;return this.V(this.p),e}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let i=this.p;for(;e--;)i=i.B;i.l=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;if(i<=0)return this.i;if(e===0)for(;i--;)this.pushFront(r);else if(e===this.i)for(;i--;)this.pushBack(r);else{let n=this.p;for(let s=1;s<e;++s)n=n.B;let o=n.B;for(this.i+=i;i--;)n.B={l:r,L:n},n.B.L=n,n=n.B;n.B=o,o.L=n}return this.i}find(e){let r=this.p;for(;r!==this.h;){if(r.l===e)return new dr(r,this.h,this);r=r.B}return this.end()}reverse(){if(this.i<=1)return;let e=this.p,r=this._,i=0;for(;i<<1<this.i;){let n=e.l;e.l=r.l,r.l=n,e=e.B,r=r.L,i+=1}}unique(){if(this.i<=1)return this.i;let e=this.p;for(;e!==this.h;){let r=e;for(;r.B!==this.h&&r.l===r.B.l;)r=r.B,this.i-=1;e.B=r.B,e.B.L=e,e=e.B}return this.i}sort(e){if(this.i<=1)return;let r=[];this.forEach(function(n){r.push(n)}),r.sort(e);let i=this.p;r.forEach(function(n){i.l=n,i=i.B})}merge(e){let r=this;if(this.i===0)e.forEach(function(i){r.pushBack(i)});else{let i=this.p;e.forEach(function(n){for(;i!==r.h&&i.l<=n;)i=i.B;r.G(n,i.L)})}return this.i}forEach(e){let r=this.p,i=0;for(;r!==this.h;)e(r.l,i++,this),r=r.B}[Symbol.iterator](){return function*(){if(this.i===0)return;let e=this.p;for(;e!==this.h;)yield e.l,e=e.B}.bind(this)()}},Kv=da;jn.default=Kv});var tp=O(Fn=>{"use strict";_();v();m();Object.defineProperty(Fn,"t",{value:!0});Fn.default=void 0;var Gv=Yv(qn()),Qv=fa();function Yv(t){return t&&t.t?t:{default:t}}var pr=class t extends Qv.RandomIterator{constructor(e,r,i){super(e,i),this.container=r}copy(){return new t(this.o,this.container,this.iteratorType)}},pa=class extends Gv.default{constructor(e=[],r=4096){super(),this.j=0,this.D=0,this.R=0,this.N=0,this.P=0,this.A=[];let i=(()=>{if(typeof e.length=="number")return e.length;if(typeof e.size=="number")return e.size;if(typeof e.size=="function")return e.size();throw new TypeError("Cannot get the length or size of the container")})();this.F=r,this.P=Math.max(Math.ceil(i/this.F),1);for(let s=0;s<this.P;++s)this.A.push(new Array(this.F));let n=Math.ceil(i/this.F);this.j=this.R=(this.P>>1)-(n>>1),this.D=this.N=this.F-i%this.F>>1;let o=this;e.forEach(function(s){o.pushBack(s)})}T(){let e=[],r=Math.max(this.P>>1,1);for(let i=0;i<r;++i)e[i]=new Array(this.F);for(let i=this.j;i<this.P;++i)e[e.length]=this.A[i];for(let i=0;i<this.R;++i)e[e.length]=this.A[i];e[e.length]=[...this.A[this.R]],this.j=r,this.R=e.length-1;for(let i=0;i<r;++i)e[e.length]=new Array(this.F);this.A=e,this.P=e.length}O(e){let r=this.D+e+1,i=r%this.F,n=i-1,o=this.j+(r-i)/this.F;return i===0&&(o-=1),o%=this.P,n<0&&(n+=this.F),{curNodeBucketIndex:o,curNodePointerIndex:n}}clear(){this.A=[new Array(this.F)],this.P=1,this.j=this.R=this.i=0,this.D=this.N=this.F>>1}begin(){return new pr(0,this)}end(){return new pr(this.i,this)}rBegin(){return new pr(this.i-1,this,1)}rEnd(){return new pr(-1,this,1)}front(){if(this.i!==0)return this.A[this.j][this.D]}back(){if(this.i!==0)return this.A[this.R][this.N]}pushBack(e){return this.i&&(this.N<this.F-1?this.N+=1:this.R<this.P-1?(this.R+=1,this.N=0):(this.R=0,this.N=0),this.R===this.j&&this.N===this.D&&this.T()),this.i+=1,this.A[this.R][this.N]=e,this.i}popBack(){if(this.i===0)return;let e=this.A[this.R][this.N];return this.i!==1&&(this.N>0?this.N-=1:this.R>0?(this.R-=1,this.N=this.F-1):(this.R=this.P-1,this.N=this.F-1)),this.i-=1,e}pushFront(e){return this.i&&(this.D>0?this.D-=1:this.j>0?(this.j-=1,this.D=this.F-1):(this.j=this.P-1,this.D=this.F-1),this.j===this.R&&this.D===this.N&&this.T()),this.i+=1,this.A[this.j][this.D]=e,this.i}popFront(){if(this.i===0)return;let e=this.A[this.j][this.D];return this.i!==1&&(this.D<this.F-1?this.D+=1:this.j<this.P-1?(this.j+=1,this.D=0):(this.j=0,this.D=0)),this.i-=1,e}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:r,curNodePointerIndex:i}=this.O(e);return this.A[r][i]}setElementByPos(e,r){if(e<0||e>this.i-1)throw new RangeError;let{curNodeBucketIndex:i,curNodePointerIndex:n}=this.O(e);this.A[i][n]=r}insert(e,r,i=1){if(e<0||e>this.i)throw new RangeError;if(e===0)for(;i--;)this.pushFront(r);else if(e===this.i)for(;i--;)this.pushBack(r);else{let n=[];for(let o=e;o<this.i;++o)n.push(this.getElementByPos(o));this.cut(e-1);for(let o=0;o<i;++o)this.pushBack(r);for(let o=0;o<n.length;++o)this.pushBack(n[o])}return this.i}cut(e){if(e<0)return this.clear(),0;let{curNodeBucketIndex:r,curNodePointerIndex:i}=this.O(e);return this.R=r,this.N=i,this.i=e+1,this.i}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;if(e===0)this.popFront();else if(e===this.i-1)this.popBack();else{let r=[];for(let n=e+1;n<this.i;++n)r.push(this.getElementByPos(n));this.cut(e),this.popBack();let i=this;r.forEach(function(n){i.pushBack(n)})}return this.i}eraseElementByValue(e){if(this.i===0)return 0;let r=[];for(let n=0;n<this.i;++n){let o=this.getElementByPos(n);o!==e&&r.push(o)}let i=r.length;for(let n=0;n<i;++n)this.setElementByPos(n,r[n]);return this.cut(i-1)}eraseElementByIterator(e){let r=e.o;return this.eraseElementByPos(r),e=e.next(),e}find(e){for(let r=0;r<this.i;++r)if(this.getElementByPos(r)===e)return new pr(r,this);return this.end()}reverse(){let e=0,r=this.i-1;for(;e<r;){let i=this.getElementByPos(e);this.setElementByPos(e,this.getElementByPos(r)),this.setElementByPos(r,i),e+=1,r-=1}}unique(){if(this.i<=1)return this.i;let e=1,r=this.getElementByPos(0);for(let i=1;i<this.i;++i){let n=this.getElementByPos(i);n!==r&&(r=n,this.setElementByPos(e++,n))}for(;this.i>e;)this.popBack();return this.i}sort(e){let r=[];for(let i=0;i<this.i;++i)r.push(this.getElementByPos(i));r.sort(e);for(let i=0;i<this.i;++i)this.setElementByPos(i,r[i])}shrinkToFit(){if(this.i===0)return;let e=[];this.forEach(function(r){e.push(r)}),this.P=Math.max(Math.ceil(this.i/this.F),1),this.i=this.j=this.R=this.D=this.N=0,this.A=[];for(let r=0;r<this.P;++r)this.A.push(new Array(this.F));for(let r=0;r<e.length;++r)this.pushBack(e[r])}forEach(e){for(let r=0;r<this.i;++r)e(this.getElementByPos(r),r,this)}[Symbol.iterator](){return function*(){for(let e=0;e<this.i;++e)yield this.getElementByPos(e)}.bind(this)()}},Jv=pa;Fn.default=Jv});var rp=O(Qr=>{"use strict";_();v();m();Object.defineProperty(Qr,"t",{value:!0});Qr.TreeNodeEnableIndex=Qr.TreeNode=void 0;var Wn=class{constructor(e,r){this.ee=1,this.u=void 0,this.l=void 0,this.U=void 0,this.W=void 0,this.tt=void 0,this.u=e,this.l=r}L(){let e=this;if(e.ee===1&&e.tt.tt===e)e=e.W;else if(e.U)for(e=e.U;e.W;)e=e.W;else{let r=e.tt;for(;r.U===e;)e=r,r=e.tt;e=r}return e}B(){let e=this;if(e.W){for(e=e.W;e.U;)e=e.U;return e}else{let r=e.tt;for(;r.W===e;)e=r,r=e.tt;return e.W!==r?r:e}}te(){let e=this.tt,r=this.W,i=r.U;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.U=this,this.tt=r,this.W=i,i&&(i.tt=this),r}se(){let e=this.tt,r=this.U,i=r.W;return e.tt===this?e.tt=r:e.U===this?e.U=r:e.W=r,r.tt=e,r.W=this,this.tt=r,this.U=i,i&&(i.tt=this),r}};Qr.TreeNode=Wn;var ga=class extends Wn{constructor(){super(...arguments),this.rt=1}te(){let e=super.te();return this.ie(),e.ie(),e}se(){let e=super.se();return this.ie(),e.ie(),e}ie(){this.rt=1,this.U&&(this.rt+=this.U.rt),this.W&&(this.rt+=this.W.rt)}};Qr.TreeNodeEnableIndex=ga});var ya=O($n=>{"use strict";_();v();m();Object.defineProperty($n,"t",{value:!0});$n.default=void 0;var ip=rp(),Xv=st(),np=ot(),ba=class extends Xv.Container{constructor(e=function(i,n){return i<n?-1:i>n?1:0},r=!1){super(),this.Y=void 0,this.v=e,r?(this.re=ip.TreeNodeEnableIndex,this.M=function(i,n,o){let s=this.ne(i,n,o);if(s){let a=s.tt;for(;a!==this.h;)a.rt+=1,a=a.tt;let u=this.he(s);if(u){let{parentNode:f,grandParent:d,curNode:h}=u;f.ie(),d.ie(),h.ie()}}return this.i},this.V=function(i){let n=this.fe(i);for(;n!==this.h;)n.rt-=1,n=n.tt}):(this.re=ip.TreeNode,this.M=function(i,n,o){let s=this.ne(i,n,o);return s&&this.he(s),this.i},this.V=this.fe),this.h=new this.re}X(e,r){let i=this.h;for(;e;){let n=this.v(e.u,r);if(n<0)e=e.W;else if(n>0)i=e,e=e.U;else return e}return i}Z(e,r){let i=this.h;for(;e;)this.v(e.u,r)<=0?e=e.W:(i=e,e=e.U);return i}$(e,r){let i=this.h;for(;e;){let n=this.v(e.u,r);if(n<0)i=e,e=e.W;else if(n>0)e=e.U;else return e}return i}rr(e,r){let i=this.h;for(;e;)this.v(e.u,r)<0?(i=e,e=e.W):e=e.U;return i}ue(e){for(;;){let r=e.tt;if(r===this.h)return;if(e.ee===1){e.ee=0;return}if(e===r.U){let i=r.W;if(i.ee===1)i.ee=0,r.ee=1,r===this.Y?this.Y=r.te():r.te();else if(i.W&&i.W.ee===1){i.ee=r.ee,r.ee=0,i.W.ee=0,r===this.Y?this.Y=r.te():r.te();return}else i.U&&i.U.ee===1?(i.ee=1,i.U.ee=0,i.se()):(i.ee=1,e=r)}else{let i=r.U;if(i.ee===1)i.ee=0,r.ee=1,r===this.Y?this.Y=r.se():r.se();else if(i.U&&i.U.ee===1){i.ee=r.ee,r.ee=0,i.U.ee=0,r===this.Y?this.Y=r.se():r.se();return}else i.W&&i.W.ee===1?(i.ee=1,i.W.ee=0,i.te()):(i.ee=1,e=r)}}}fe(e){if(this.i===1)return this.clear(),this.h;let r=e;for(;r.U||r.W;){if(r.W)for(r=r.W;r.U;)r=r.U;else r=r.U;[e.u,r.u]=[r.u,e.u],[e.l,r.l]=[r.l,e.l],e=r}this.h.U===r?this.h.U=r.tt:this.h.W===r&&(this.h.W=r.tt),this.ue(r);let i=r.tt;return r===i.U?i.U=void 0:i.W=void 0,this.i-=1,this.Y.ee=0,i}oe(e,r){return e===void 0?!1:this.oe(e.U,r)||r(e)?!0:this.oe(e.W,r)}he(e){for(;;){let r=e.tt;if(r.ee===0)return;let i=r.tt;if(r===i.U){let n=i.W;if(n&&n.ee===1){if(n.ee=r.ee=0,i===this.Y)return;i.ee=1,e=i;continue}else if(e===r.W){if(e.ee=0,e.U&&(e.U.tt=r),e.W&&(e.W.tt=i),r.W=e.U,i.U=e.W,e.U=r,e.W=i,i===this.Y)this.Y=e,this.h.tt=e;else{let o=i.tt;o.U===i?o.U=e:o.W=e}return e.tt=i.tt,r.tt=e,i.tt=e,i.ee=1,{parentNode:r,grandParent:i,curNode:e}}else r.ee=0,i===this.Y?this.Y=i.se():i.se(),i.ee=1}else{let n=i.U;if(n&&n.ee===1){if(n.ee=r.ee=0,i===this.Y)return;i.ee=1,e=i;continue}else if(e===r.U){if(e.ee=0,e.U&&(e.U.tt=i),e.W&&(e.W.tt=r),i.W=e.U,r.U=e.W,e.U=i,e.W=r,i===this.Y)this.Y=e,this.h.tt=e;else{let o=i.tt;o.U===i?o.U=e:o.W=e}return e.tt=i.tt,r.tt=e,i.tt=e,i.ee=1,{parentNode:r,grandParent:i,curNode:e}}else r.ee=0,i===this.Y?this.Y=i.te():i.te(),i.ee=1}return}}ne(e,r,i){if(this.Y===void 0){this.i+=1,this.Y=new this.re(e,r),this.Y.ee=0,this.Y.tt=this.h,this.h.tt=this.Y,this.h.U=this.Y,this.h.W=this.Y;return}let n,o=this.h.U,s=this.v(o.u,e);if(s===0){o.l=r;return}else if(s>0)o.U=new this.re(e,r),o.U.tt=o,n=o.U,this.h.U=n;else{let a=this.h.W,u=this.v(a.u,e);if(u===0){a.l=r;return}else if(u<0)a.W=new this.re(e,r),a.W.tt=a,n=a.W,this.h.W=n;else{if(i!==void 0){let f=i.o;if(f!==this.h){let d=this.v(f.u,e);if(d===0){f.l=r;return}else if(d>0){let h=f.L(),g=this.v(h.u,e);if(g===0){h.l=r;return}else g<0&&(n=new this.re(e,r),h.W===void 0?(h.W=n,n.tt=h):(f.U=n,n.tt=f))}}}if(n===void 0)for(n=this.Y;;){let f=this.v(n.u,e);if(f>0){if(n.U===void 0){n.U=new this.re(e,r),n.U.tt=n,n=n.U;break}n=n.U}else if(f<0){if(n.W===void 0){n.W=new this.re(e,r),n.W.tt=n,n=n.W;break}n=n.W}else{n.l=r;return}}}}return this.i+=1,n}I(e,r){for(;e;){let i=this.v(e.u,r);if(i<0)e=e.W;else if(i>0)e=e.U;else return e}return e||this.h}clear(){this.i=0,this.Y=void 0,this.h.tt=void 0,this.h.U=this.h.W=void 0}updateKeyByIterator(e,r){let i=e.o;if(i===this.h&&(0,np.throwIteratorAccessError)(),this.i===1)return i.u=r,!0;if(i===this.h.U)return this.v(i.B().u,r)>0?(i.u=r,!0):!1;if(i===this.h.W)return this.v(i.L().u,r)<0?(i.u=r,!0):!1;let n=i.L().u;if(this.v(n,r)>=0)return!1;let o=i.B().u;return this.v(o,r)<=0?!1:(i.u=r,!0)}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=0,i=this;return this.oe(this.Y,function(n){return e===r?(i.V(n),!0):(r+=1,!1)}),this.i}eraseElementByKey(e){if(this.i===0)return!1;let r=this.I(this.Y,e);return r===this.h?!1:(this.V(r),!0)}eraseElementByIterator(e){let r=e.o;r===this.h&&(0,np.throwIteratorAccessError)();let i=r.W===void 0;return e.iteratorType===0?i&&e.next():(!i||r.U===void 0)&&e.next(),this.V(r),e}forEach(e){let r=0;for(let i of this)e(i,r++,this)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r,i=0;for(let n of this){if(i===e){r=n;break}i+=1}return r}getHeight(){if(this.i===0)return 0;let e=function(r){return r?Math.max(e(r.U),e(r.W))+1:0};return e(this.Y)}},Zv=ba;$n.default=Zv});var _a=O(Vn=>{"use strict";_();v();m();Object.defineProperty(Vn,"t",{value:!0});Vn.default=void 0;var eE=st(),Hn=ot(),wa=class extends eE.ContainerIterator{constructor(e,r,i){super(i),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o===this.h.U&&(0,Hn.throwIteratorAccessError)(),this.o=this.o.L(),this},this.next=function(){return this.o===this.h&&(0,Hn.throwIteratorAccessError)(),this.o=this.o.B(),this}):(this.pre=function(){return this.o===this.h.W&&(0,Hn.throwIteratorAccessError)(),this.o=this.o.B(),this},this.next=function(){return this.o===this.h&&(0,Hn.throwIteratorAccessError)(),this.o=this.o.L(),this})}get index(){let e=this.o,r=this.h.tt;if(e===this.h)return r?r.rt-1:0;let i=0;for(e.U&&(i+=e.U.rt);e!==r;){let n=e.tt;e===n.W&&(i+=1,n.U&&(i+=n.U.rt)),e=n}return i}},tE=wa;Vn.default=tE});var op=O(zn=>{"use strict";_();v();m();Object.defineProperty(zn,"t",{value:!0});zn.default=void 0;var rE=sp(ya()),iE=sp(_a()),nE=ot();function sp(t){return t&&t.t?t:{default:t}}var ze=class t extends iE.default{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){return this.o===this.h&&(0,nE.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},ma=class extends rE.default{constructor(e=[],r,i){super(r,i);let n=this;e.forEach(function(o){n.insert(o)})}*K(e){e!==void 0&&(yield*this.K(e.U),yield e.u,yield*this.K(e.W))}begin(){return new ze(this.h.U||this.h,this.h,this)}end(){return new ze(this.h,this.h,this)}rBegin(){return new ze(this.h.W||this.h,this.h,this,1)}rEnd(){return new ze(this.h,this.h,this,1)}front(){return this.h.U?this.h.U.u:void 0}back(){return this.h.W?this.h.W.u:void 0}insert(e,r){return this.M(e,void 0,r)}find(e){let r=this.I(this.Y,e);return new ze(r,this.h,this)}lowerBound(e){let r=this.X(this.Y,e);return new ze(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new ze(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new ze(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new ze(r,this.h,this)}union(e){let r=this;return e.forEach(function(i){r.insert(i)}),this.i}[Symbol.iterator](){return this.K(this.Y)}},sE=ma;zn.default=sE});var lp=O(Kn=>{"use strict";_();v();m();Object.defineProperty(Kn,"t",{value:!0});Kn.default=void 0;var oE=ap(ya()),aE=ap(_a()),lE=ot();function ap(t){return t&&t.t?t:{default:t}}var Ke=class t extends aE.default{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){this.o===this.h&&(0,lE.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,i){if(i==="0")return e.o.u;if(i==="1")return e.o.l},set(r,i,n){if(i!=="1")throw new TypeError("props must be 1");return e.o.l=n,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},va=class extends oE.default{constructor(e=[],r,i){super(r,i);let n=this;e.forEach(function(o){n.setElement(o[0],o[1])})}*K(e){e!==void 0&&(yield*this.K(e.U),yield[e.u,e.l],yield*this.K(e.W))}begin(){return new Ke(this.h.U||this.h,this.h,this)}end(){return new Ke(this.h,this.h,this)}rBegin(){return new Ke(this.h.W||this.h,this.h,this,1)}rEnd(){return new Ke(this.h,this.h,this,1)}front(){if(this.i===0)return;let e=this.h.U;return[e.u,e.l]}back(){if(this.i===0)return;let e=this.h.W;return[e.u,e.l]}lowerBound(e){let r=this.X(this.Y,e);return new Ke(r,this.h,this)}upperBound(e){let r=this.Z(this.Y,e);return new Ke(r,this.h,this)}reverseLowerBound(e){let r=this.$(this.Y,e);return new Ke(r,this.h,this)}reverseUpperBound(e){let r=this.rr(this.Y,e);return new Ke(r,this.h,this)}setElement(e,r,i){return this.M(e,r,i)}find(e){let r=this.I(this.Y,e);return new Ke(r,this.h,this)}getElementByKey(e){return this.I(this.Y,e).l}union(e){let r=this;return e.forEach(function(i){r.setElement(i[0],i[1])}),this.i}[Symbol.iterator](){return this.K(this.Y)}},uE=va;Kn.default=uE});var Sa=O(Ea=>{"use strict";_();v();m();Object.defineProperty(Ea,"t",{value:!0});Ea.default=cE;function cE(t){let e=typeof t;return e==="object"&&t!==null||e==="function"}});var Ra=O(Yr=>{"use strict";_();v();m();Object.defineProperty(Yr,"t",{value:!0});Yr.HashContainerIterator=Yr.HashContainer=void 0;var up=st(),Aa=fE(Sa()),Ai=ot();function fE(t){return t&&t.t?t:{default:t}}var Ia=class extends up.ContainerIterator{constructor(e,r,i){super(i),this.o=e,this.h=r,this.iteratorType===0?(this.pre=function(){return this.o.L===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.L,this},this.next=function(){return this.o===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.B,this}):(this.pre=function(){return this.o.B===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.B,this},this.next=function(){return this.o===this.h&&(0,Ai.throwIteratorAccessError)(),this.o=this.o.L,this})}};Yr.HashContainerIterator=Ia;var Ta=class extends up.Container{constructor(){super(),this.H=[],this.g={},this.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(this.g,null),this.h={},this.h.L=this.h.B=this.p=this._=this.h}V(e){let{L:r,B:i}=e;r.B=i,i.L=r,e===this.p&&(this.p=i),e===this._&&(this._=r),this.i-=1}M(e,r,i){i===void 0&&(i=(0,Aa.default)(e));let n;if(i){let o=e[this.HASH_TAG];if(o!==void 0)return this.H[o].l=r,this.i;Object.defineProperty(e,this.HASH_TAG,{value:this.H.length,configurable:!0}),n={u:e,l:r,L:this._,B:this.h},this.H.push(n)}else{let o=this.g[e];if(o)return o.l=r,this.i;n={u:e,l:r,L:this._,B:this.h},this.g[e]=n}return this.i===0?(this.p=n,this.h.B=n):this._.B=n,this._=n,this.h.L=n,++this.i}I(e,r){if(r===void 0&&(r=(0,Aa.default)(e)),r){let i=e[this.HASH_TAG];return i===void 0?this.h:this.H[i]}else return this.g[e]||this.h}clear(){let e=this.HASH_TAG;this.H.forEach(function(r){delete r.u[e]}),this.H=[],this.g={},Object.setPrototypeOf(this.g,null),this.i=0,this.p=this._=this.h.L=this.h.B=this.h}eraseElementByKey(e,r){let i;if(r===void 0&&(r=(0,Aa.default)(e)),r){let n=e[this.HASH_TAG];if(n===void 0)return!1;delete e[this.HASH_TAG],i=this.H[n],delete this.H[n]}else{if(i=this.g[e],i===void 0)return!1;delete this.g[e]}return this.V(i),!0}eraseElementByIterator(e){let r=e.o;return r===this.h&&(0,Ai.throwIteratorAccessError)(),this.V(r),e.next()}eraseElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return this.V(r),this.i}};Yr.HashContainer=Ta});var fp=O(Gn=>{"use strict";_();v();m();Object.defineProperty(Gn,"t",{value:!0});Gn.default=void 0;var cp=Ra(),hE=ot(),gr=class t extends cp.HashContainerIterator{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){return this.o===this.h&&(0,hE.throwIteratorAccessError)(),this.o.u}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},Ca=class extends cp.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(i){r.insert(i)})}begin(){return new gr(this.p,this.h,this)}end(){return new gr(this.h,this.h,this)}rBegin(){return new gr(this._,this.h,this,1)}rEnd(){return new gr(this.h,this.h,this,1)}front(){return this.p.u}back(){return this._.u}insert(e,r){return this.M(e,void 0,r)}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return r.u}find(e,r){let i=this.I(e,r);return new gr(i,this.h,this)}forEach(e){let r=0,i=this.p;for(;i!==this.h;)e(i.u,r++,this),i=i.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield e.u,e=e.B}.bind(this)()}},dE=Ca;Gn.default=dE});var dp=O(Qn=>{"use strict";_();v();m();Object.defineProperty(Qn,"t",{value:!0});Qn.default=void 0;var hp=Ra(),pE=bE(Sa()),gE=ot();function bE(t){return t&&t.t?t:{default:t}}var br=class t extends hp.HashContainerIterator{constructor(e,r,i,n){super(e,r,n),this.container=i}get pointer(){this.o===this.h&&(0,gE.throwIteratorAccessError)();let e=this;return new Proxy([],{get(r,i){if(i==="0")return e.o.u;if(i==="1")return e.o.l},set(r,i,n){if(i!=="1")throw new TypeError("props must be 1");return e.o.l=n,!0}})}copy(){return new t(this.o,this.h,this.container,this.iteratorType)}},Pa=class extends hp.HashContainer{constructor(e=[]){super();let r=this;e.forEach(function(i){r.setElement(i[0],i[1])})}begin(){return new br(this.p,this.h,this)}end(){return new br(this.h,this.h,this)}rBegin(){return new br(this._,this.h,this,1)}rEnd(){return new br(this.h,this.h,this,1)}front(){if(this.i!==0)return[this.p.u,this.p.l]}back(){if(this.i!==0)return[this._.u,this._.l]}setElement(e,r,i){return this.M(e,r,i)}getElementByKey(e,r){if(r===void 0&&(r=(0,pE.default)(e)),r){let n=e[this.HASH_TAG];return n!==void 0?this.H[n].l:void 0}let i=this.g[e];return i?i.l:void 0}getElementByPos(e){if(e<0||e>this.i-1)throw new RangeError;let r=this.p;for(;e--;)r=r.B;return[r.u,r.l]}find(e,r){let i=this.I(e,r);return new br(i,this.h,this)}forEach(e){let r=0,i=this.p;for(;i!==this.h;)e([i.u,i.l],r++,this),i=i.B}[Symbol.iterator](){return function*(){let e=this.p;for(;e!==this.h;)yield[e.u,e.l],e=e.B}.bind(this)()}},yE=Pa;Qn.default=yE});var pp=O(Ne=>{"use strict";_();v();m();Object.defineProperty(Ne,"t",{value:!0});Object.defineProperty(Ne,"Deque",{enumerable:!0,get:function(){return SE.default}});Object.defineProperty(Ne,"HashMap",{enumerable:!0,get:function(){return RE.default}});Object.defineProperty(Ne,"HashSet",{enumerable:!0,get:function(){return TE.default}});Object.defineProperty(Ne,"LinkList",{enumerable:!0,get:function(){return EE.default}});Object.defineProperty(Ne,"OrderedMap",{enumerable:!0,get:function(){return IE.default}});Object.defineProperty(Ne,"OrderedSet",{enumerable:!0,get:function(){return AE.default}});Object.defineProperty(Ne,"PriorityQueue",{enumerable:!0,get:function(){return mE.default}});Object.defineProperty(Ne,"Queue",{enumerable:!0,get:function(){return _E.default}});Object.defineProperty(Ne,"Stack",{enumerable:!0,get:function(){return wE.default}});Object.defineProperty(Ne,"Vector",{enumerable:!0,get:function(){return vE.default}});var wE=at(Yd()),_E=at(Jd()),mE=at(Xd()),vE=at(Zd()),EE=at(ep()),SE=at(tp()),AE=at(op()),IE=at(lp()),TE=at(fp()),RE=at(dp());function at(t){return t&&t.t?t:{default:t}}});var bp=O((RM,gp)=>{"use strict";_();v();m();var CE=pp().OrderedSet,lt=nt()("number-allocator:trace"),PE=nt()("number-allocator:error");function Re(t,e){this.low=t,this.high=e}Re.prototype.equals=function(t){return this.low===t.low&&this.high===t.high};Re.prototype.compare=function(t){return this.low<t.low&&this.high<t.low?-1:t.low<this.low&&t.high<this.low?1:0};function ut(t,e){if(!(this instanceof ut))return new ut(t,e);this.min=t,this.max=e,this.ss=new CE([],(r,i)=>r.compare(i)),lt("Create"),this.clear()}ut.prototype.firstVacant=function(){return this.ss.size()===0?null:this.ss.front().low};ut.prototype.alloc=function(){if(this.ss.size()===0)return lt("alloc():empty"),null;let t=this.ss.begin(),e=t.pointer.low,r=t.pointer.high,i=e;return i+1<=r?this.ss.updateKeyByIterator(t,new Re(e+1,r)):this.ss.eraseElementByPos(0),lt("alloc():"+i),i};ut.prototype.use=function(t){let e=new Re(t,t),r=this.ss.lowerBound(e);if(!r.equals(this.ss.end())){let i=r.pointer.low,n=r.pointer.high;return r.pointer.equals(e)?(this.ss.eraseElementByIterator(r),lt("use():"+t),!0):i>t?!1:i===t?(this.ss.updateKeyByIterator(r,new Re(i+1,n)),lt("use():"+t),!0):n===t?(this.ss.updateKeyByIterator(r,new Re(i,n-1)),lt("use():"+t),!0):(this.ss.updateKeyByIterator(r,new Re(t+1,n)),this.ss.insert(new Re(i,t-1)),lt("use():"+t),!0)}return lt("use():failed"),!1};ut.prototype.free=function(t){if(t<this.min||t>this.max){PE("free():"+t+" is out of range");return}let e=new Re(t,t),r=this.ss.upperBound(e);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin())){this.ss.insert(e);return}r.pre();let i=r.pointer.high;r.pointer.high+1===t?this.ss.updateKeyByIterator(r,new Re(i,t)):this.ss.insert(e)}else if(r.equals(this.ss.begin()))if(t+1===r.pointer.low){let i=r.pointer.high;this.ss.updateKeyByIterator(r,new Re(t,i))}else this.ss.insert(e);else{let i=r.pointer.low,n=r.pointer.high;r.pre();let o=r.pointer.low;r.pointer.high+1===t?t+1===i?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new Re(o,n))):this.ss.updateKeyByIterator(r,new Re(o,t)):t+1===i?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new Re(t,n))):this.ss.insert(e)}lt("free():"+t)};ut.prototype.clear=function(){lt("clear()"),this.ss.clear(),this.ss.insert(new Re(this.min,this.max))};ut.prototype.intervalCount=function(){return this.ss.size()};ut.prototype.dump=function(){console.log("length:"+this.ss.size());for(let t of this.ss)console.log(t)};gp.exports=ut});var ka=O((MM,yp)=>{_();v();m();var kE=bp();yp.exports.NumberAllocator=kE});var wp=O(xa=>{"use strict";_();v();m();Object.defineProperty(xa,"__esModule",{value:!0});var BE=Qd(),xE=ka(),Ba=class{constructor(e){e>0&&(this.aliasToTopic=new BE.LRUCache({max:e}),this.topicToAlias={},this.numberAllocator=new xE.NumberAllocator(1,e),this.max=e,this.length=0)}put(e,r){if(r===0||r>this.max)return!1;let i=this.aliasToTopic.get(r);return i&&delete this.topicToAlias[i],this.aliasToTopic.set(r,e),this.topicToAlias[e]=r,this.numberAllocator.use(r),this.length=this.aliasToTopic.size,!0}getTopicByAlias(e){return this.aliasToTopic.get(e)}getAliasByTopic(e){let r=this.topicToAlias[e];return typeof r<"u"&&this.aliasToTopic.get(r),r}clear(){this.aliasToTopic.clear(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0}getLruAlias(){let e=this.numberAllocator.firstVacant();return e||[...this.aliasToTopic.keys()][this.aliasToTopic.size-1]}};xa.default=Ba});var _p=O(Ii=>{"use strict";_();v();m();var OE=Ii&&Ii.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ii,"__esModule",{value:!0});var ME=Si(),LE=OE(wp()),qE=cr(),UE=(t,e)=>{t.log("_handleConnack");let{options:r}=t,n=r.protocolVersion===5?e.reasonCode:e.returnCode;if(clearTimeout(t.connackTimer),delete t.topicAliasSend,e.properties){if(e.properties.topicAliasMaximum){if(e.properties.topicAliasMaximum>65535){t.emit("error",new Error("topicAliasMaximum from broker is out of range"));return}e.properties.topicAliasMaximum>0&&(t.topicAliasSend=new LE.default(e.properties.topicAliasMaximum))}e.properties.serverKeepAlive&&r.keepalive&&(r.keepalive=e.properties.serverKeepAlive),e.properties.maximumPacketSize&&(r.properties||(r.properties={}),r.properties.maximumPacketSize=e.properties.maximumPacketSize)}if(n===0)t.reconnecting=!1,t._onConnect(e);else if(n>0){let o=new qE.ErrorWithReasonCode(`Connection refused: ${ME.ReasonCodes[n]}`,n);t.emit("error",o),t.options.reconnectOnConnackError&&t._cleanUp(!0)}};Ii.default=UE});var mp=O(Oa=>{"use strict";_();v();m();Object.defineProperty(Oa,"__esModule",{value:!0});var NE=(t,e,r)=>{t.log("handling pubrel packet");let i=typeof r<"u"?r:t.noop,{messageId:n}=e,o={cmd:"pubcomp",messageId:n};t.incomingStore.get(e,(s,a)=>{s?t._sendPacket(o,i):(t.emit("message",a.topic,a.payload,a),t.handleMessage(a,u=>{if(u)return i(u);t.incomingStore.del(a,t.noop),t._sendPacket(o,i)}))})};Oa.default=NE});var vp=O(Ti=>{"use strict";_();v();m();var Ri=Ti&&Ti.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ti,"__esModule",{value:!0});var DE=Ri(jd()),jE=Ri(Hd()),FE=Ri(_p()),WE=Ri(Si()),$E=Ri(mp()),HE=(t,e,r)=>{let{options:i}=t;if(i.protocolVersion===5&&i.properties&&i.properties.maximumPacketSize&&i.properties.maximumPacketSize<e.length)return t.emit("error",new Error(`exceeding packets size ${e.cmd}`)),t.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),t;switch(t.log("_handlePacket :: emitting packetreceive"),t.emit("packetreceive",e),e.cmd){case"publish":(0,DE.default)(t,e,r);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":t.reschedulePing(),(0,WE.default)(t,e),r();break;case"pubrel":t.reschedulePing(),(0,$E.default)(t,e,r);break;case"connack":(0,FE.default)(t,e),r();break;case"auth":t.reschedulePing(),(0,jE.default)(t,e),r();break;case"pingresp":t.log("_handlePacket :: received pingresp"),t.reschedulePing(!0),r();break;case"disconnect":t.emit("disconnect",e),r();break;default:t.log("_handlePacket :: unknown command"),r();break}};Ti.default=HE});var Ep=O(Jr=>{"use strict";_();v();m();var VE=Jr&&Jr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Jr,"__esModule",{value:!0});Jr.TypedEventEmitter=void 0;var zE=VE((Ot(),Q(xt))),KE=cr(),Yn=class{};Jr.TypedEventEmitter=Yn;(0,KE.applyMixin)(Yn,zE.default)});var Ci=O(yr=>{"use strict";_();v();m();Object.defineProperty(yr,"__esModule",{value:!0});yr.isReactNativeBrowser=yr.isWebWorker=void 0;var GE=()=>{var t;return typeof window<"u"?typeof navigator<"u"&&((t=navigator.userAgent)===null||t===void 0?void 0:t.toLowerCase().indexOf(" electron/"))>-1&&(!(R===null||R===void 0)&&R.versions)?!Object.prototype.hasOwnProperty.call(R.versions,"electron"):typeof window.document<"u":!1},Sp=()=>{var t,e;return!!(typeof self=="object"&&(!((e=(t=self?.constructor)===null||t===void 0?void 0:t.name)===null||e===void 0)&&e.includes("WorkerGlobalScope")))},Ap=()=>typeof navigator<"u"&&navigator.product==="ReactNative",QE=GE()||Sp()||Ap();yr.isWebWorker=Sp();yr.isReactNativeBrowser=Ap();yr.default=QE});var Tp=O((Jn,Ip)=>{_();v();m();(function(t,e){typeof Jn=="object"&&typeof Ip<"u"?e(Jn):typeof define=="function"&&define.amd?define(["exports"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.fastUniqueNumbers={}))})(Jn,function(t){"use strict";var e=function(g){return function(b){var E=g(b);return b.add(E),E}},r=function(g){return function(b,E){return g.set(b,E),E}},i=Number.MAX_SAFE_INTEGER===void 0?9007199254740991:Number.MAX_SAFE_INTEGER,n=536870912,o=n*2,s=function(g,b){return function(E){var w=b.get(E),S=w===void 0?E.size:w<o?w+1:0;if(!E.has(S))return g(E,S);if(E.size<n){for(;E.has(S);)S=Math.floor(Math.random()*o);return g(E,S)}if(E.size>i)throw new Error("Congratulations, you created a collection of unique numbers which uses all available integers!");for(;E.has(S);)S=Math.floor(Math.random()*i);return g(E,S)}},a=new WeakMap,u=r(a),f=s(u,a),d=e(f);t.addUniqueNumber=d,t.generateUniqueNumber=f})});var Cp=O((Xn,Rp)=>{_();v();m();(function(t,e){typeof Xn=="object"&&typeof Rp<"u"?e(Xn,Tp()):typeof define=="function"&&define.amd?define(["exports","fast-unique-numbers"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.workerTimersBroker={},t.fastUniqueNumbers))})(Xn,function(t,e){"use strict";var r=function(s){return s.method!==void 0&&s.method==="call"},i=function(s){return s.error===null&&typeof s.id=="number"},n=function(s){var a=new Map([[0,function(){}]]),u=new Map([[0,function(){}]]),f=new Map,d=new Worker(s);d.addEventListener("message",function(w){var S=w.data;if(r(S)){var I=S.params,P=I.timerId,C=I.timerType;if(C==="interval"){var M=a.get(P);if(typeof M=="number"){var q=f.get(M);if(q===void 0||q.timerId!==P||q.timerType!==C)throw new Error("The timer is in an undefined state.")}else if(typeof M<"u")M();else throw new Error("The timer is in an undefined state.")}else if(C==="timeout"){var z=u.get(P);if(typeof z=="number"){var j=f.get(z);if(j===void 0||j.timerId!==P||j.timerType!==C)throw new Error("The timer is in an undefined state.")}else if(typeof z<"u")z(),u.delete(P);else throw new Error("The timer is in an undefined state.")}}else if(i(S)){var G=S.id,$=f.get(G);if($===void 0)throw new Error("The timer is in an undefined state.");var te=$.timerId,pt=$.timerType;f.delete(G),pt==="interval"?a.delete(te):u.delete(te)}else{var Fe=S.error.message;throw new Error(Fe)}});var h=function(S){var I=e.generateUniqueNumber(f);f.set(I,{timerId:S,timerType:"interval"}),a.set(S,I),d.postMessage({id:I,method:"clear",params:{timerId:S,timerType:"interval"}})},g=function(S){var I=e.generateUniqueNumber(f);f.set(I,{timerId:S,timerType:"timeout"}),u.set(S,I),d.postMessage({id:I,method:"clear",params:{timerId:S,timerType:"timeout"}})},b=function(S){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,P=e.generateUniqueNumber(a);return a.set(P,function(){S(),typeof a.get(P)=="function"&&d.postMessage({id:null,method:"set",params:{delay:I,now:performance.now(),timerId:P,timerType:"interval"}})}),d.postMessage({id:null,method:"set",params:{delay:I,now:performance.now(),timerId:P,timerType:"interval"}}),P},E=function(S){var I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,P=e.generateUniqueNumber(u);return u.set(P,S),d.postMessage({id:null,method:"set",params:{delay:I,now:performance.now(),timerId:P,timerType:"timeout"}}),P};return{clearInterval:h,clearTimeout:g,setInterval:b,setTimeout:E}};t.load=n})});var kp=O((Zn,Pp)=>{_();v();m();(function(t,e){typeof Zn=="object"&&typeof Pp<"u"?e(Zn,Cp()):typeof define=="function"&&define.amd?define(["exports","worker-timers-broker"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t.workerTimers={},t.workerTimersBroker))})(Zn,function(t,e){"use strict";var r=function(d,h){var g=null;return function(){if(g!==null)return g;var b=new Blob([h],{type:"application/javascript; charset=utf-8"}),E=URL.createObjectURL(b);return g=d(E),setTimeout(function(){return URL.revokeObjectURL(E)}),g}},i=`(()=>{var e={472:(e,t,r)=>{var o,i;void 0===(i="function"==typeof(o=function(){"use strict";var e=new Map,t=new Map,r=function(t){var r=e.get(t);if(void 0===r)throw new Error('There is no interval scheduled with the given id "'.concat(t,'".'));clearTimeout(r),e.delete(t)},o=function(e){var r=t.get(e);if(void 0===r)throw new Error('There is no timeout scheduled with the given id "'.concat(e,'".'));clearTimeout(r),t.delete(e)},i=function(e,t){var r,o=performance.now();return{expected:o+(r=e-Math.max(0,o-t)),remainingDelay:r}},n=function e(t,r,o,i){var n=performance.now();n>o?postMessage({id:null,method:"call",params:{timerId:r,timerType:i}}):t.set(r,setTimeout(e,o-n,t,r,o,i))},a=function(t,r,o){var a=i(t,o),s=a.expected,d=a.remainingDelay;e.set(r,setTimeout(n,d,e,r,s,"interval"))},s=function(e,r,o){var a=i(e,o),s=a.expected,d=a.remainingDelay;t.set(r,setTimeout(n,d,t,r,s,"timeout"))};addEventListener("message",(function(e){var t=e.data;try{if("clear"===t.method){var i=t.id,n=t.params,d=n.timerId,c=n.timerType;if("interval"===c)r(d),postMessage({error:null,id:i});else{if("timeout"!==c)throw new Error('The given type "'.concat(c,'" is not supported'));o(d),postMessage({error:null,id:i})}}else{if("set"!==t.method)throw new Error('The given method "'.concat(t.method,'" is not supported'));var u=t.params,l=u.delay,p=u.now,m=u.timerId,v=u.timerType;if("interval"===v)a(l,m,p);else{if("timeout"!==v)throw new Error('The given type "'.concat(v,'" is not supported'));s(l,m,p)}}}catch(e){postMessage({error:{message:e.message},id:t.id,result:null})}}))})?o.call(t,r,t,e):o)||(e.exports=i)}},t={};function r(o){var i=t[o];if(void 0!==i)return i.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,r),n.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";r(472)})()})();`,n=r(e.load,i),o=function(d){return n().clearInterval(d)},s=function(d){return n().clearTimeout(d)},a=function(){var d;return(d=n()).setInterval.apply(d,arguments)},u=function(){var d;return(d=n()).setTimeout.apply(d,arguments)};t.clearInterval=o,t.clearTimeout=s,t.setInterval=a,t.setTimeout=u})});var Mp=O(St=>{"use strict";_();v();m();var YE=St&&St.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),JE=St&&St.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),XE=St&&St.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var i=[];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i[i.length]=n);return i},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var i=t(e),n=0;n<i.length;n++)i[n]!=="default"&&YE(r,e,i[n]);return JE(r,e),r}}();Object.defineProperty(St,"__esModule",{value:!0});var Ma=XE(Ci()),Bp=kp(),xp={set:Bp.setInterval,clear:Bp.clearInterval},Op={set:(t,e)=>setInterval(t,e),clear:t=>clearInterval(t)},ZE=t=>{switch(t){case"native":return Op;case"worker":return xp;case"auto":default:return Ma.default&&!Ma.isWebWorker&&!Ma.isReactNativeBrowser?xp:Op}};St.default=ZE});var qa=O(Pi=>{"use strict";_();v();m();var e1=Pi&&Pi.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Pi,"__esModule",{value:!0});var t1=e1(Mp()),La=class{get keepaliveTimeoutTimestamp(){return this._keepaliveTimeoutTimestamp}get intervalEvery(){return this._intervalEvery}get keepalive(){return this._keepalive}constructor(e,r){this.destroyed=!1,this.client=e,this.timer=typeof r=="object"&&"set"in r&&"clear"in r?r:(0,t1.default)(r),this.setKeepalive(e.options.keepalive)}clear(){this.timerId&&(this.timer.clear(this.timerId),this.timerId=null)}setKeepalive(e){if(e*=1e3,isNaN(e)||e<=0||e>2147483647)throw new Error(`Keepalive value must be an integer between 0 and 2147483647. Provided value is ${e}`);this._keepalive=e,this.reschedule(),this.client.log(`KeepaliveManager: set keepalive to ${e}ms`)}destroy(){this.clear(),this.destroyed=!0}reschedule(){if(this.destroyed)return;this.clear(),this.counter=0;let e=Math.ceil(this._keepalive*1.5);this._keepaliveTimeoutTimestamp=Date.now()+e,this._intervalEvery=Math.ceil(this._keepalive/2),this.timerId=this.timer.set(()=>{this.destroyed||(this.counter+=1,this.counter===2?this.client.sendPing():this.counter>2&&this.client.onKeepaliveTimeout())},this._intervalEvery)}};Pi.default=La});var ts=O(Ge=>{"use strict";_();v();m();var r1=Ge&&Ge.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),i1=Ge&&Ge.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Dp=Ge&&Ge.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var i=[];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i[i.length]=n);return i},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var i=t(e),n=0;n<i.length;n++)i[n]!=="default"&&r1(r,e,i[n]);return i1(r,e),r}}(),Ht=Ge&&Ge.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Ge,"__esModule",{value:!0});var n1=Ht($u()),Ua=Ht(xd()),s1=Ht(zo()),o1=Nt(),Lp=Ht(qd()),qp=Dp(Nd()),a1=Ht(nt()),Xr=Ht(Qo()),l1=Ht(vp()),ki=cr(),u1=Ep(),c1=Ht(qa()),Up=Dp(Ci()),Na=globalThis.setImmediate||((...t)=>{let e=t.shift();(0,ki.nextTick)(()=>{e(...t)})}),Np={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:30*1e3,clean:!0,resubscribe:!0,subscribeBatchSize:null,writeCache:!0,timerVariant:"auto"},es=class t extends u1.TypedEventEmitter{static defaultId(){return`mqttjs_${Math.random().toString(16).substr(2,8)}`}constructor(e,r){super(),this.options=r||{};for(let i in Np)typeof this.options[i]>"u"?this.options[i]=Np[i]:this.options[i]=r[i];this.log=this.options.log||(0,a1.default)("mqttjs:client"),this.noop=this._noop.bind(this),this.log("MqttClient :: version:",t.VERSION),Up.isWebWorker?this.log("MqttClient :: environment","webworker"):this.log("MqttClient :: environment",Up.default?"browser":"node"),this.log("MqttClient :: options.protocol",r.protocol),this.log("MqttClient :: options.protocolVersion",r.protocolVersion),this.log("MqttClient :: options.username",r.username),this.log("MqttClient :: options.keepalive",r.keepalive),this.log("MqttClient :: options.reconnectPeriod",r.reconnectPeriod),this.log("MqttClient :: options.rejectUnauthorized",r.rejectUnauthorized),this.log("MqttClient :: options.properties.topicAliasMaximum",r.properties?r.properties.topicAliasMaximum:void 0),this.options.clientId=typeof r.clientId=="string"?r.clientId:t.defaultId(),this.log("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=r.protocolVersion===5&&r.customHandleAcks?r.customHandleAcks:(...i)=>{i[3](null,0)},this.options.writeCache||(Ua.default.writeToStream.cacheNumbers=!1),this.streamBuilder=e,this.messageIdProvider=typeof this.options.messageIdProvider>"u"?new s1.default:this.options.messageIdProvider,this.outgoingStore=r.outgoingStore||new Xr.default,this.incomingStore=r.incomingStore||new Xr.default,this.queueQoSZero=r.queueQoSZero===void 0?!0:r.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.keepaliveManager=null,this.connected=!1,this.disconnecting=!1,this.reconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,r.properties&&r.properties.topicAliasMaximum>0&&(r.properties.topicAliasMaximum>65535?this.log("MqttClient :: options.properties.topicAliasMaximum is out of range"):this.topicAliasRecv=new n1.default(r.properties.topicAliasMaximum)),this.on("connect",()=>{let{queue:i}=this,n=()=>{let o=i.shift();this.log("deliver :: entry %o",o);let s=null;if(!o){this._resubscribe();return}s=o.packet,this.log("deliver :: call _sendPacket for %o",s);let a=!0;s.messageId&&s.messageId!==0&&(this.messageIdProvider.register(s.messageId)||(a=!1)),a?this._sendPacket(s,u=>{o.cb&&o.cb(u),n()}):(this.log("messageId: %d has already used. The message is skipped and removed.",s.messageId),n())};this.log("connect :: sending queued packets"),n()}),this.on("close",()=>{this.log("close :: connected set to `false`"),this.connected=!1,this.log("close :: clearing connackTimer"),clearTimeout(this.connackTimer),this._destroyKeepaliveManager(),this.topicAliasRecv&&this.topicAliasRecv.clear(),this.log("close :: calling _setupReconnect"),this._setupReconnect()}),this.options.manualConnect||(this.log("MqttClient :: setting up stream"),this.connect())}handleAuth(e,r){r()}handleMessage(e,r){r()}_nextId(){return this.messageIdProvider.allocate()}getLastMessageId(){return this.messageIdProvider.getLastAllocated()}connect(){var e;let r=new o1.Writable,i=Ua.default.parser(this.options),n=null,o=[];this.log("connect :: calling method to clear reconnect"),this._clearReconnect(),this.disconnected&&!this.reconnecting&&(this.incomingStore=this.options.incomingStore||new Xr.default,this.outgoingStore=this.options.outgoingStore||new Xr.default,this.disconnecting=!1,this.disconnected=!1),this.log("connect :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),i.on("packet",d=>{this.log("parser :: on packet push to packets array."),o.push(d)});let s=()=>{this.log("work :: getting next packet in queue");let d=o.shift();if(d)this.log("work :: packet pulled from queue"),(0,l1.default)(this,d,a);else{this.log("work :: no packets in queue");let h=n;n=null,this.log("work :: done flag is %s",!!h),h&&h()}},a=()=>{if(o.length)(0,ki.nextTick)(s);else{let d=n;n=null,d()}};r._write=(d,h,g)=>{n=g,this.log("writable stream :: parsing buffer"),i.parse(d),s()};let u=d=>{this.log("streamErrorHandler :: error",d.message),d.code?(this.log("streamErrorHandler :: emitting error"),this.emit("error",d)):this.noop(d)};this.log("connect :: pipe stream to writable stream"),this.stream.pipe(r),this.stream.on("error",u),this.stream.on("close",()=>{this.log("(%s)stream :: on close",this.options.clientId),this._flushVolatile(),this.log("stream: emit close to MqttClient"),this.emit("close")}),this.log("connect: sending packet `connect`");let f={cmd:"connect",protocolId:this.options.protocolId,protocolVersion:this.options.protocolVersion,clean:this.options.clean,clientId:this.options.clientId,keepalive:this.options.keepalive,username:this.options.username,password:this.options.password,properties:this.options.properties};if(this.options.will&&(f.will=Object.assign(Object.assign({},this.options.will),{payload:(e=this.options.will)===null||e===void 0?void 0:e.payload})),this.topicAliasRecv&&(f.properties||(f.properties={}),this.topicAliasRecv&&(f.properties.topicAliasMaximum=this.topicAliasRecv.max)),this._writePacket(f),i.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return this.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&typeof this.options.authPacket=="object"){let d=Object.assign({cmd:"auth",reasonCode:0},this.options.authPacket);this._writePacket(d)}}return this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout(()=>{this.log("!!connectTimeout hit!! Calling _cleanUp with force `true`"),this.emit("error",new Error("connack timeout")),this._cleanUp(!0)},this.options.connectTimeout),this}publish(e,r,i,n){this.log("publish :: message `%s` to topic `%s`",r,e);let{options:o}=this;typeof i=="function"&&(n=i,i=null),i=i||{},i=Object.assign(Object.assign({},{qos:0,retain:!1,dup:!1}),i);let{qos:a,retain:u,dup:f,properties:d,cbStorePut:h}=i;if(this._checkDisconnecting(n))return this;let g=()=>{let b=0;if((a===1||a===2)&&(b=this._nextId(),b===null))return this.log("No messageId left"),!1;let E={cmd:"publish",topic:e,payload:r,qos:a,retain:u,messageId:b,dup:f};switch(o.protocolVersion===5&&(E.properties=d),this.log("publish :: qos",a),a){case 1:case 2:this.outgoing[E.messageId]={volatile:!1,cb:n||this.noop},this.log("MqttClient:publish: packet cmd: %s",E.cmd),this._sendPacket(E,void 0,h);break;default:this.log("MqttClient:publish: packet cmd: %s",E.cmd),this._sendPacket(E,n,h);break}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!g())&&this._storeProcessingQueue.push({invoke:g,cbStorePut:i.cbStorePut,callback:n}),this}publishAsync(e,r,i){return new Promise((n,o)=>{this.publish(e,r,i,(s,a)=>{s?o(s):n(a)})})}subscribe(e,r,i){let n=this.options.protocolVersion;typeof r=="function"&&(i=r),i=i||this.noop;let o=!1,s=[];typeof e=="string"?(e=[e],s=e):Array.isArray(e)?s=e:typeof e=="object"&&(o=e.resubscribe,delete e.resubscribe,s=Object.keys(e));let a=qp.validateTopics(s);if(a!==null)return Na(i,new Error(`Invalid topic ${a}`)),this;if(this._checkDisconnecting(i))return this.log("subscribe: discconecting true"),this;let u={qos:0};n===5&&(u.nl=!1,u.rap=!1,u.rh=0),r=Object.assign(Object.assign({},u),r);let f=r.properties,d=[],h=(E,w)=>{if(w=w||r,!Object.prototype.hasOwnProperty.call(this._resubscribeTopics,E)||this._resubscribeTopics[E].qos<w.qos||o){let S={topic:E,qos:w.qos};n===5&&(S.nl=w.nl,S.rap=w.rap,S.rh=w.rh,S.properties=f),this.log("subscribe: pushing topic `%s` and qos `%s` to subs list",S.topic,S.qos),d.push(S)}};if(Array.isArray(e)?e.forEach(E=>{this.log("subscribe: array topic %s",E),h(E)}):Object.keys(e).forEach(E=>{this.log("subscribe: object topic %s, %o",E,e[E]),h(E,e[E])}),!d.length)return i(null,[]),this;let g=(E,w)=>{let S={cmd:"subscribe",subscriptions:E,messageId:w};if(f&&(S.properties=f),this.options.resubscribe){this.log("subscribe :: resubscribe true");let P=[];E.forEach(C=>{if(this.options.reconnectPeriod>0){let M={qos:C.qos};n===5&&(M.nl=C.nl||!1,M.rap=C.rap||!1,M.rh=C.rh||0,M.properties=C.properties),this._resubscribeTopics[C.topic]=M,P.push(C.topic)}}),this.messageIdToTopic[S.messageId]=P}let I=new Promise((P,C)=>{this.outgoing[S.messageId]={volatile:!0,cb(M,q){if(!M){let{granted:z}=q;for(let j=0;j<z.length;j+=1)E[j].qos=z[j]}M?C(new ki.ErrorWithSubackPacket(M.message,q)):P(q)}}});return this.log("subscribe :: call _sendPacket"),this._sendPacket(S),I},b=()=>{var E;let w=(E=this.options.subscribeBatchSize)!==null&&E!==void 0?E:d.length,S=[];for(let I=0;I<d.length;I+=w){let P=d.slice(I,I+w),C=this._nextId();if(C===null)return this.log("No messageId left"),!1;S.push(g(P,C))}return Promise.all(S).then(I=>{i(null,d,I.at(-1))}).catch(I=>{i(I,d,I.packet)}),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!b())&&this._storeProcessingQueue.push({invoke:b,callback:i}),this}subscribeAsync(e,r){return new Promise((i,n)=>{this.subscribe(e,r,(o,s)=>{o?n(o):i(s)})})}unsubscribe(e,r,i){typeof e=="string"&&(e=[e]),typeof r=="function"&&(i=r),i=i||this.noop;let n=qp.validateTopics(e);if(n!==null)return Na(i,new Error(`Invalid topic ${n}`)),this;if(this._checkDisconnecting(i))return this;let o=()=>{let s=this._nextId();if(s===null)return this.log("No messageId left"),!1;let a={cmd:"unsubscribe",messageId:s,unsubscriptions:[]};return typeof e=="string"?a.unsubscriptions=[e]:Array.isArray(e)&&(a.unsubscriptions=e),this.options.resubscribe&&a.unsubscriptions.forEach(u=>{delete this._resubscribeTopics[u]}),typeof r=="object"&&r.properties&&(a.properties=r.properties),this.outgoing[a.messageId]={volatile:!0,cb:i},this.log("unsubscribe: call _sendPacket"),this._sendPacket(a),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!o())&&this._storeProcessingQueue.push({invoke:o,callback:i}),this}unsubscribeAsync(e,r){return new Promise((i,n)=>{this.unsubscribe(e,r,(o,s)=>{o?n(o):i(s)})})}end(e,r,i){this.log("end :: (%s)",this.options.clientId),(e==null||typeof e!="boolean")&&(i=i||r,r=e,e=!1),typeof r!="object"&&(i=i||r,r=null),this.log("end :: cb? %s",!!i),(!i||typeof i!="function")&&(i=this.noop);let n=()=>{this.log("end :: closeStores: closing incoming and outgoing stores"),this.disconnected=!0,this.incomingStore.close(s=>{this.outgoingStore.close(a=>{if(this.log("end :: closeStores: emitting end"),this.emit("end"),i){let u=s||a;this.log("end :: closeStores: invoking callback with args"),i(u)}})}),this._deferredReconnect?this._deferredReconnect():(this.options.reconnectPeriod===0||this.options.manualConnect)&&(this.disconnecting=!1)},o=()=>{this.log("end :: (%s) :: finish :: calling _cleanUp with force %s",this.options.clientId,e),this._cleanUp(e,()=>{this.log("end :: finish :: calling process.nextTick on closeStores"),(0,ki.nextTick)(n)},r)};return this.disconnecting?(i(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(this.log("end :: (%s) :: calling finish in 10ms once outgoing is empty",this.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,o,10))):(this.log("end :: (%s) :: immediately calling finish",this.options.clientId),o()),this)}endAsync(e,r){return new Promise((i,n)=>{this.end(e,r,o=>{o?n(o):i()})})}removeOutgoingMessage(e){if(this.outgoing[e]){let{cb:r}=this.outgoing[e];this._removeOutgoingAndStoreMessage(e,()=>{r(new Error("Message removed"))})}return this}reconnect(e){this.log("client reconnect");let r=()=>{e?(this.options.incomingStore=e.incomingStore,this.options.outgoingStore=e.outgoingStore):(this.options.incomingStore=null,this.options.outgoingStore=null),this.incomingStore=this.options.incomingStore||new Xr.default,this.outgoingStore=this.options.outgoingStore||new Xr.default,this.disconnecting=!1,this.disconnected=!1,this._deferredReconnect=null,this._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this}_flushVolatile(){this.outgoing&&(this.log("_flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(this.outgoing).forEach(e=>{this.outgoing[e].volatile&&typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_flush(){this.outgoing&&(this.log("_flush: queue exists? %b",!!this.outgoing),Object.keys(this.outgoing).forEach(e=>{typeof this.outgoing[e].cb=="function"&&(this.outgoing[e].cb(new Error("Connection closed")),delete this.outgoing[e])}))}_removeTopicAliasAndRecoverTopicName(e){let r;e.properties&&(r=e.properties.topicAlias);let i=e.topic.toString();if(this.log("_removeTopicAliasAndRecoverTopicName :: alias %d, topic %o",r,i),i.length===0){if(typeof r>"u")return new Error("Unregistered Topic Alias");if(i=this.topicAliasSend.getTopicByAlias(r),typeof i>"u")return new Error("Unregistered Topic Alias");e.topic=i}r&&delete e.properties.topicAlias}_checkDisconnecting(e){return this.disconnecting&&(e&&e!==this.noop?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting}_reconnect(){this.log("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this.connect()}),this.log("client already connected. disconnecting first.")):(this.log("_reconnect: calling connect"),this.connect())}_setupReconnect(){!this.disconnecting&&!this.reconnectTimer&&this.options.reconnectPeriod>0?(this.reconnecting||(this.log("_setupReconnect :: emit `offline` state"),this.emit("offline"),this.log("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),this.log("_setupReconnect :: setting reconnectTimer for %d ms",this.options.reconnectPeriod),this.reconnectTimer=setInterval(()=>{this.log("reconnectTimer :: reconnect triggered!"),this._reconnect()},this.options.reconnectPeriod)):this.log("_setupReconnect :: doing nothing...")}_clearReconnect(){this.log("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)}_cleanUp(e,r,i={}){if(r&&(this.log("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",r)),this.log("_cleanUp :: forced? %s",e),e)this.options.reconnectPeriod===0&&this.options.clean&&this._flush(),this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{let n=Object.assign({cmd:"disconnect"},i);this.log("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(n,()=>{this.log("_cleanUp :: (%s) :: destroying stream",this.options.clientId),Na(()=>{this.stream.end(()=>{this.log("_cleanUp :: (%s) :: stream destroyed",this.options.clientId)})})})}!this.disconnecting&&!this.reconnecting&&(this.log("_cleanUp :: client not disconnecting/reconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),this._destroyKeepaliveManager(),r&&!this.connected&&(this.log("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",r),r())}_storeAndSend(e,r,i){this.log("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let n=e,o;if(n.cmd==="publish"&&(n=(0,Lp.default)(e),o=this._removeTopicAliasAndRecoverTopicName(n),o))return r&&r(o);this.outgoingStore.put(n,s=>{if(s)return r&&r(s);i(),this._writePacket(e,r)})}_applyTopicAlias(e){if(this.options.protocolVersion===5&&e.cmd==="publish"){let r;e.properties&&(r=e.properties.topicAlias);let i=e.topic.toString();if(this.topicAliasSend)if(r){if(i.length!==0&&(this.log("applyTopicAlias :: register topic: %s - alias: %d",i,r),!this.topicAliasSend.put(i,r)))return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",i,r),new Error("Sending Topic Alias out of range")}else i.length!==0&&(this.options.autoAssignTopicAlias?(r=this.topicAliasSend.getAliasByTopic(i),r?(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",i,r)):(r=this.topicAliasSend.getLruAlias(),this.topicAliasSend.put(i,r),e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto assign topic: %s - alias: %d",i,r))):this.options.autoUseTopicAlias&&(r=this.topicAliasSend.getAliasByTopic(i),r&&(e.topic="",e.properties=Object.assign(Object.assign({},e.properties),{topicAlias:r}),this.log("applyTopicAlias :: auto use topic: %s - alias: %d",i,r))));else if(r)return this.log("applyTopicAlias :: error out of range. topic: %s - alias: %d",i,r),new Error("Sending Topic Alias out of range")}}_noop(e){this.log("noop ::",e)}_writePacket(e,r){this.log("_writePacket :: packet: %O",e),this.log("_writePacket :: emitting `packetsend`"),this.emit("packetsend",e),this.log("_writePacket :: writing to stream");let i=Ua.default.writeToStream(e,this.stream,this.options);this.log("_writePacket :: writeToStream result %s",i),!i&&r&&r!==this.noop?(this.log("_writePacket :: handle events on `drain` once through callback."),this.stream.once("drain",r)):r&&(this.log("_writePacket :: invoking cb"),r())}_sendPacket(e,r,i,n){this.log("_sendPacket :: (%s) ::  start",this.options.clientId),i=i||this.noop,r=r||this.noop;let o=this._applyTopicAlias(e);if(o){r(o);return}if(!this.connected){if(e.cmd==="auth"){this._writePacket(e,r);return}this.log("_sendPacket :: client not connected. Storing packet offline."),this._storePacket(e,r,i);return}if(n){this._writePacket(e,r);return}switch(e.cmd){case"publish":break;case"pubrel":this._storeAndSend(e,r,i);return;default:this._writePacket(e,r);return}switch(e.qos){case 2:case 1:this._storeAndSend(e,r,i);break;case 0:default:this._writePacket(e,r);break}this.log("_sendPacket :: (%s) ::  end",this.options.clientId)}_storePacket(e,r,i){this.log("_storePacket :: packet: %o",e),this.log("_storePacket :: cb? %s",!!r),i=i||this.noop;let n=e;if(n.cmd==="publish"){n=(0,Lp.default)(e);let s=this._removeTopicAliasAndRecoverTopicName(n);if(s)return r&&r(s)}let o=n.qos||0;o===0&&this.queueQoSZero||n.cmd!=="publish"?this.queue.push({packet:n,cb:r}):o>0?(r=this.outgoing[n.messageId]?this.outgoing[n.messageId].cb:null,this.outgoingStore.put(n,s=>{if(s)return r&&r(s);i()})):r&&r(new Error("No connection to broker"))}_setupKeepaliveManager(){this.log("_setupKeepaliveManager :: keepalive %d (seconds)",this.options.keepalive),!this.keepaliveManager&&this.options.keepalive&&(this.keepaliveManager=new c1.default(this,this.options.timerVariant))}_destroyKeepaliveManager(){this.keepaliveManager&&(this.log("_destroyKeepaliveManager :: destroying keepalive manager"),this.keepaliveManager.destroy(),this.keepaliveManager=null)}reschedulePing(e=!1){this.keepaliveManager&&this.options.keepalive&&(e||this.options.reschedulePings)&&this._reschedulePing()}_reschedulePing(){this.log("_reschedulePing :: rescheduling ping"),this.keepaliveManager.reschedule()}sendPing(){this.log("_sendPing :: sending pingreq"),this._sendPacket({cmd:"pingreq"})}onKeepaliveTimeout(){this.emit("error",new Error("Keepalive timeout")),this.log("onKeepaliveTimeout :: calling _cleanUp with force true"),this._cleanUp(!0)}_resubscribe(){this.log("_resubscribe");let e=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||this.options.protocolVersion>=4&&!this.connackPacket.sessionPresent)&&e.length>0)if(this.options.resubscribe)if(this.options.protocolVersion===5){this.log("_resubscribe: protocolVersion 5");for(let r=0;r<e.length;r++){let i={};i[e[r]]=this._resubscribeTopics[e[r]],i.resubscribe=!0,this.subscribe(i,{properties:i[e[r]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1}_onConnect(e){if(this.disconnected){this.emit("connect",e);return}this.connackPacket=e,this.messageIdProvider.clear(),this._setupKeepaliveManager(),this.connected=!0;let r=()=>{let i=this.outgoingStore.createStream(),n=()=>{i.destroy(),i=null,this._flushStoreProcessingQueue(),o()},o=()=>{this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={}};this.once("close",n),i.on("error",a=>{o(),this._flushStoreProcessingQueue(),this.removeListener("close",n),this.emit("error",a)});let s=()=>{if(!i)return;let a=i.read(1),u;if(!a){i.once("readable",s);return}if(this._storeProcessing=!0,this._packetIdsDuringStoreProcessing[a.messageId]){s();return}!this.disconnecting&&!this.reconnectTimer?(u=this.outgoing[a.messageId]?this.outgoing[a.messageId].cb:null,this.outgoing[a.messageId]={volatile:!1,cb(f,d){u&&u(f,d),s()}},this._packetIdsDuringStoreProcessing[a.messageId]=!0,this.messageIdProvider.register(a.messageId)?this._sendPacket(a,void 0,void 0,!0):this.log("messageId: %d has already used.",a.messageId)):i.destroy&&i.destroy()};i.on("end",()=>{let a=!0;for(let u in this._packetIdsDuringStoreProcessing)if(!this._packetIdsDuringStoreProcessing[u]){a=!1;break}this.removeListener("close",n),a?(o(),this._invokeAllStoreProcessingQueue(),this.emit("connect",e)):r()}),s()};r()}_invokeStoreProcessingQueue(){if(!this._storeProcessing&&this._storeProcessingQueue.length>0){let e=this._storeProcessingQueue[0];if(e&&e.invoke())return this._storeProcessingQueue.shift(),!0}return!1}_invokeAllStoreProcessingQueue(){for(;this._invokeStoreProcessingQueue(););}_flushStoreProcessingQueue(){for(let e of this._storeProcessingQueue)e.cbStorePut&&e.cbStorePut(new Error("Connection closed")),e.callback&&e.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)}_removeOutgoingAndStoreMessage(e,r){delete this.outgoing[e],this.outgoingStore.del({messageId:e},(i,n)=>{r(i,n),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue()})}};es.VERSION=ki.MQTTJS_VERSION;Ge.default=es});var jp=O(ja=>{"use strict";_();v();m();Object.defineProperty(ja,"__esModule",{value:!0});var f1=ka(),Da=class{constructor(){this.numberAllocator=new f1.NumberAllocator(1,65535)}allocate(){return this.lastId=this.numberAllocator.alloc(),this.lastId}getLastAllocated(){return this.lastId}register(e){return this.numberAllocator.use(e)}deallocate(e){this.numberAllocator.free(e)}clear(){this.numberAllocator.clear()}};ja.default=Da});function wr(t){throw new RangeError(g1[t])}function Fp(t,e){let r=t.split("@"),i="";r.length>1&&(i=r[0]+"@",t=r[1]);let n=function(o,s){let a=[],u=o.length;for(;u--;)a[u]=s(o[u]);return a}((t=t.replace(p1,".")).split("."),e).join(".");return i+n}function Vp(t){let e=[],r=0,i=t.length;for(;r<i;){let n=t.charCodeAt(r++);if(n>=55296&&n<=56319&&r<i){let o=t.charCodeAt(r++);(64512&o)==56320?e.push(((1023&n)<<10)+(1023&o)+65536):(e.push(n),r--)}else e.push(n)}return e}var h1,d1,p1,g1,ct,Fa,Wp,zp,$p,Hp,Vt,Kp=Ae(()=>{_();v();m();h1=/^xn--/,d1=/[^\0-\x7E]/,p1=/[\x2E\u3002\uFF0E\uFF61]/g,g1={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},ct=Math.floor,Fa=String.fromCharCode;Wp=function(t,e){return t+22+75*(t<26)-((e!=0)<<5)},zp=function(t,e,r){let i=0;for(t=r?ct(t/700):t>>1,t+=ct(t/e);t>455;i+=36)t=ct(t/35);return ct(i+36*t/(t+38))},$p=function(t){let e=[],r=t.length,i=0,n=128,o=72,s=t.lastIndexOf("-");s<0&&(s=0);for(let u=0;u<s;++u)t.charCodeAt(u)>=128&&wr("not-basic"),e.push(t.charCodeAt(u));for(let u=s>0?s+1:0;u<r;){let f=i;for(let h=1,g=36;;g+=36){u>=r&&wr("invalid-input");let b=(a=t.charCodeAt(u++))-48<10?a-22:a-65<26?a-65:a-97<26?a-97:36;(b>=36||b>ct((2147483647-i)/h))&&wr("overflow"),i+=b*h;let E=g<=o?1:g>=o+26?26:g-o;if(b<E)break;let w=36-E;h>ct(2147483647/w)&&wr("overflow"),h*=w}let d=e.length+1;o=zp(i-f,d,f==0),ct(i/d)>2147483647-n&&wr("overflow"),n+=ct(i/d),i%=d,e.splice(i++,0,n)}var a;return String.fromCodePoint(...e)},Hp=function(t){let e=[],r=(t=Vp(t)).length,i=128,n=0,o=72;for(let u of t)u<128&&e.push(Fa(u));let s=e.length,a=s;for(s&&e.push("-");a<r;){let u=2147483647;for(let d of t)d>=i&&d<u&&(u=d);let f=a+1;u-i>ct((2147483647-n)/f)&&wr("overflow"),n+=(u-i)*f,i=u;for(let d of t)if(d<i&&++n>2147483647&&wr("overflow"),d==i){let h=n;for(let g=36;;g+=36){let b=g<=o?1:g>=o+26?26:g-o;if(h<b)break;let E=h-b,w=36-b;e.push(Fa(Wp(b+E%w,0))),h=ct(E/w)}e.push(Fa(Wp(h,0))),o=zp(n,f,a==s),n=0,++a}++n,++i}return e.join("")},Vt={version:"2.1.0",ucs2:{decode:Vp,encode:t=>String.fromCodePoint(...t)},decode:$p,encode:Hp,toASCII:function(t){return Fp(t,function(e){return d1.test(e)?"xn--"+Hp(e):e})},toUnicode:function(t){return Fp(t,function(e){return h1.test(e)?$p(e.slice(4).toLowerCase()):e})}};Vt.decode;Vt.encode;Vt.toASCII;Vt.toUnicode;Vt.ucs2;Vt.version});function b1(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var y1,Bi,w1,ft,Gp=Ae(()=>{_();v();m();y1=function(t,e,r,i){e=e||"&",r=r||"=";var n={};if(typeof t!="string"||t.length===0)return n;var o=/\+/g;t=t.split(e);var s=1e3;i&&typeof i.maxKeys=="number"&&(s=i.maxKeys);var a=t.length;s>0&&a>s&&(a=s);for(var u=0;u<a;++u){var f,d,h,g,b=t[u].replace(o,"%20"),E=b.indexOf(r);E>=0?(f=b.substr(0,E),d=b.substr(E+1)):(f=b,d=""),h=decodeURIComponent(f),g=decodeURIComponent(d),b1(n,h)?Array.isArray(n[h])?n[h].push(g):n[h]=[n[h],g]:n[h]=g}return n},Bi=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}},w1=function(t,e,r,i){return e=e||"&",r=r||"=",t===null&&(t=void 0),typeof t=="object"?Object.keys(t).map(function(n){var o=encodeURIComponent(Bi(n))+r;return Array.isArray(t[n])?t[n].map(function(s){return o+encodeURIComponent(Bi(s))}).join(e):o+encodeURIComponent(Bi(t[n]))}).join(e):i?encodeURIComponent(Bi(i))+r+encodeURIComponent(Bi(t)):""},ft={};ft.decode=ft.parse=y1,ft.encode=ft.stringify=w1;ft.decode;ft.encode;ft.parse;ft.stringify});function Wa(){throw new Error("setTimeout has not been defined")}function $a(){throw new Error("clearTimeout has not been defined")}function Jp(t){if(It===setTimeout)return setTimeout(t,0);if((It===Wa||!It)&&setTimeout)return It=setTimeout,setTimeout(t,0);try{return It(t,0)}catch{try{return It.call(null,t,0)}catch{return It.call(this||ei,t,0)}}}function _1(){Zr&&_r&&(Zr=!1,_r.length?Rt=_r.concat(Rt):rs=-1,Rt.length&&Xp())}function Xp(){if(!Zr){var t=Jp(_1);Zr=!0;for(var e=Rt.length;e;){for(_r=Rt,Rt=[];++rs<e;)_r&&_r[rs].run();rs=-1,e=Rt.length}_r=null,Zr=!1,function(r){if(Tt===clearTimeout)return clearTimeout(r);if((Tt===$a||!Tt)&&clearTimeout)return Tt=clearTimeout,clearTimeout(r);try{Tt(r)}catch{try{return Tt.call(null,r)}catch{return Tt.call(this||ei,r)}}}(t)}}function Qp(t,e){(this||ei).fun=t,(this||ei).array=e}function At(){}var Yp,It,Tt,ei,fe,_r,Rt,Zr,rs,oe,Zp=Ae(()=>{_();v();m();ei=typeof globalThis<"u"?globalThis:typeof self<"u"?self:global,fe=Yp={};(function(){try{It=typeof setTimeout=="function"?setTimeout:Wa}catch{It=Wa}try{Tt=typeof clearTimeout=="function"?clearTimeout:$a}catch{Tt=$a}})();Rt=[],Zr=!1,rs=-1;fe.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];Rt.push(new Qp(t,e)),Rt.length!==1||Zr||Jp(Xp)},Qp.prototype.run=function(){(this||ei).fun.apply(null,(this||ei).array)},fe.title="browser",fe.browser=!0,fe.env={},fe.argv=[],fe.version="",fe.versions={},fe.on=At,fe.addListener=At,fe.once=At,fe.off=At,fe.removeListener=At,fe.removeAllListeners=At,fe.emit=At,fe.prependListener=At,fe.prependOnceListener=At,fe.listeners=function(t){return[]},fe.binding=function(t){throw new Error("process.binding is not supported")},fe.cwd=function(){return"/"},fe.chdir=function(t){throw new Error("process.chdir is not supported")},fe.umask=function(){return 0};oe=Yp;oe.addListener;oe.argv;oe.binding;oe.browser;oe.chdir;oe.cwd;oe.emit;oe.env;oe.listeners;oe.nextTick;oe.off;oe.on;oe.once;oe.prependListener;oe.prependOnceListener;oe.removeAllListeners;oe.removeListener;oe.title;oe.umask;oe.version;oe.versions});function m1(){if(eg)return Ha;eg=!0;var t=Ha={},e,r;function i(){throw new Error("setTimeout has not been defined")}function n(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?e=setTimeout:e=i}catch{e=i}try{typeof clearTimeout=="function"?r=clearTimeout:r=n}catch{r=n}})();function o(w){if(e===setTimeout)return setTimeout(w,0);if((e===i||!e)&&setTimeout)return e=setTimeout,setTimeout(w,0);try{return e(w,0)}catch{try{return e.call(null,w,0)}catch{return e.call(this||ti,w,0)}}}function s(w){if(r===clearTimeout)return clearTimeout(w);if((r===n||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(w);try{return r(w)}catch{try{return r.call(null,w)}catch{return r.call(this||ti,w)}}}var a=[],u=!1,f,d=-1;function h(){!u||!f||(u=!1,f.length?a=f.concat(a):d=-1,a.length&&g())}function g(){if(!u){var w=o(h);u=!0;for(var S=a.length;S;){for(f=a,a=[];++d<S;)f&&f[d].run();d=-1,S=a.length}f=null,u=!1,s(w)}}t.nextTick=function(w){var S=new Array(arguments.length-1);if(arguments.length>1)for(var I=1;I<arguments.length;I++)S[I-1]=arguments[I];a.push(new b(w,S)),a.length===1&&!u&&o(g)};function b(w,S){(this||ti).fun=w,(this||ti).array=S}b.prototype.run=function(){(this||ti).fun.apply(null,(this||ti).array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={};function E(){}return t.on=E,t.addListener=E,t.once=E,t.off=E,t.removeListener=E,t.removeAllListeners=E,t.emit=E,t.prependListener=E,t.prependOnceListener=E,t.listeners=function(w){return[]},t.binding=function(w){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(w){throw new Error("process.chdir is not supported")},t.umask=function(){return 0},Ha}var Ha,eg,ti,ne,Va=Ae(()=>{_();v();m();Ha={},eg=!1,ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:global;ne=m1();ne.platform="browser";ne.addListener;ne.argv;ne.binding;ne.browser;ne.chdir;ne.cwd;ne.emit;ne.env;ne.listeners;ne.nextTick;ne.off;ne.on;ne.once;ne.prependListener;ne.prependOnceListener;ne.removeAllListeners;ne.removeListener;ne.title;ne.umask;ne.version;ne.versions});function v1(){if(tg)return za;tg=!0;var t=ne;function e(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function r(o,s){for(var a="",u=0,f=-1,d=0,h,g=0;g<=o.length;++g){if(g<o.length)h=o.charCodeAt(g);else{if(h===47)break;h=47}if(h===47){if(!(f===g-1||d===1))if(f!==g-1&&d===2){if(a.length<2||u!==2||a.charCodeAt(a.length-1)!==46||a.charCodeAt(a.length-2)!==46){if(a.length>2){var b=a.lastIndexOf("/");if(b!==a.length-1){b===-1?(a="",u=0):(a=a.slice(0,b),u=a.length-1-a.lastIndexOf("/")),f=g,d=0;continue}}else if(a.length===2||a.length===1){a="",u=0,f=g,d=0;continue}}s&&(a.length>0?a+="/..":a="..",u=2)}else a.length>0?a+="/"+o.slice(f+1,g):a=o.slice(f+1,g),u=g-f-1;f=g,d=0}else h===46&&d!==-1?++d:d=-1}return a}function i(o,s){var a=s.dir||s.root,u=s.base||(s.name||"")+(s.ext||"");return a?a===s.root?a+u:a+o+u:u}var n={resolve:function(){for(var s="",a=!1,u,f=arguments.length-1;f>=-1&&!a;f--){var d;f>=0?d=arguments[f]:(u===void 0&&(u=t.cwd()),d=u),e(d),d.length!==0&&(s=d+"/"+s,a=d.charCodeAt(0)===47)}return s=r(s,!a),a?s.length>0?"/"+s:"/":s.length>0?s:"."},normalize:function(s){if(e(s),s.length===0)return".";var a=s.charCodeAt(0)===47,u=s.charCodeAt(s.length-1)===47;return s=r(s,!a),s.length===0&&!a&&(s="."),s.length>0&&u&&(s+="/"),a?"/"+s:s},isAbsolute:function(s){return e(s),s.length>0&&s.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var s,a=0;a<arguments.length;++a){var u=arguments[a];e(u),u.length>0&&(s===void 0?s=u:s+="/"+u)}return s===void 0?".":n.normalize(s)},relative:function(s,a){if(e(s),e(a),s===a||(s=n.resolve(s),a=n.resolve(a),s===a))return"";for(var u=1;u<s.length&&s.charCodeAt(u)===47;++u);for(var f=s.length,d=f-u,h=1;h<a.length&&a.charCodeAt(h)===47;++h);for(var g=a.length,b=g-h,E=d<b?d:b,w=-1,S=0;S<=E;++S){if(S===E){if(b>E){if(a.charCodeAt(h+S)===47)return a.slice(h+S+1);if(S===0)return a.slice(h+S)}else d>E&&(s.charCodeAt(u+S)===47?w=S:S===0&&(w=0));break}var I=s.charCodeAt(u+S),P=a.charCodeAt(h+S);if(I!==P)break;I===47&&(w=S)}var C="";for(S=u+w+1;S<=f;++S)(S===f||s.charCodeAt(S)===47)&&(C.length===0?C+="..":C+="/..");return C.length>0?C+a.slice(h+w):(h+=w,a.charCodeAt(h)===47&&++h,a.slice(h))},_makeLong:function(s){return s},dirname:function(s){if(e(s),s.length===0)return".";for(var a=s.charCodeAt(0),u=a===47,f=-1,d=!0,h=s.length-1;h>=1;--h)if(a=s.charCodeAt(h),a===47){if(!d){f=h;break}}else d=!1;return f===-1?u?"/":".":u&&f===1?"//":s.slice(0,f)},basename:function(s,a){if(a!==void 0&&typeof a!="string")throw new TypeError('"ext" argument must be a string');e(s);var u=0,f=-1,d=!0,h;if(a!==void 0&&a.length>0&&a.length<=s.length){if(a.length===s.length&&a===s)return"";var g=a.length-1,b=-1;for(h=s.length-1;h>=0;--h){var E=s.charCodeAt(h);if(E===47){if(!d){u=h+1;break}}else b===-1&&(d=!1,b=h+1),g>=0&&(E===a.charCodeAt(g)?--g===-1&&(f=h):(g=-1,f=b))}return u===f?f=b:f===-1&&(f=s.length),s.slice(u,f)}else{for(h=s.length-1;h>=0;--h)if(s.charCodeAt(h)===47){if(!d){u=h+1;break}}else f===-1&&(d=!1,f=h+1);return f===-1?"":s.slice(u,f)}},extname:function(s){e(s);for(var a=-1,u=0,f=-1,d=!0,h=0,g=s.length-1;g>=0;--g){var b=s.charCodeAt(g);if(b===47){if(!d){u=g+1;break}continue}f===-1&&(d=!1,f=g+1),b===46?a===-1?a=g:h!==1&&(h=1):a!==-1&&(h=-1)}return a===-1||f===-1||h===0||h===1&&a===f-1&&a===u+1?"":s.slice(a,f)},format:function(s){if(s===null||typeof s!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof s);return i("/",s)},parse:function(s){e(s);var a={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return a;var u=s.charCodeAt(0),f=u===47,d;f?(a.root="/",d=1):d=0;for(var h=-1,g=0,b=-1,E=!0,w=s.length-1,S=0;w>=d;--w){if(u=s.charCodeAt(w),u===47){if(!E){g=w+1;break}continue}b===-1&&(E=!1,b=w+1),u===46?h===-1?h=w:S!==1&&(S=1):h!==-1&&(S=-1)}return h===-1||b===-1||S===0||S===1&&h===b-1&&h===g+1?b!==-1&&(g===0&&f?a.base=a.name=s.slice(1,b):a.base=a.name=s.slice(g,b)):(g===0&&f?(a.name=s.slice(1,h),a.base=s.slice(1,b)):(a.name=s.slice(g,h),a.base=s.slice(g,b)),a.ext=s.slice(h,b)),g>0?a.dir=s.slice(0,g-1):f&&(a.dir="/"),a},sep:"/",delimiter:":",win32:null,posix:null};return n.posix=n,za=n,za}var za,tg,Ka,rg=Ae(()=>{_();v();m();Va();za={},tg=!1;Ka=v1()});var cg={};Ir(cg,{URL:()=>J1,Url:()=>z1,default:()=>X,fileURLToPath:()=>lg,format:()=>K1,parse:()=>Y1,pathToFileURL:()=>ug,resolve:()=>G1,resolveObject:()=>Q1});function De(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function xi(t,e,r){if(t&&ht.isObject(t)&&t instanceof De)return t;var i=new De;return i.parse(t,e,r),i}function P1(){if(og)return Ya;og=!0;var t=oe;function e(o){if(typeof o!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(o))}function r(o,s){for(var a="",u=0,f=-1,d=0,h,g=0;g<=o.length;++g){if(g<o.length)h=o.charCodeAt(g);else{if(h===47)break;h=47}if(h===47){if(!(f===g-1||d===1))if(f!==g-1&&d===2){if(a.length<2||u!==2||a.charCodeAt(a.length-1)!==46||a.charCodeAt(a.length-2)!==46){if(a.length>2){var b=a.lastIndexOf("/");if(b!==a.length-1){b===-1?(a="",u=0):(a=a.slice(0,b),u=a.length-1-a.lastIndexOf("/")),f=g,d=0;continue}}else if(a.length===2||a.length===1){a="",u=0,f=g,d=0;continue}}s&&(a.length>0?a+="/..":a="..",u=2)}else a.length>0?a+="/"+o.slice(f+1,g):a=o.slice(f+1,g),u=g-f-1;f=g,d=0}else h===46&&d!==-1?++d:d=-1}return a}function i(o,s){var a=s.dir||s.root,u=s.base||(s.name||"")+(s.ext||"");return a?a===s.root?a+u:a+o+u:u}var n={resolve:function(){for(var s="",a=!1,u,f=arguments.length-1;f>=-1&&!a;f--){var d;f>=0?d=arguments[f]:(u===void 0&&(u=t.cwd()),d=u),e(d),d.length!==0&&(s=d+"/"+s,a=d.charCodeAt(0)===47)}return s=r(s,!a),a?s.length>0?"/"+s:"/":s.length>0?s:"."},normalize:function(s){if(e(s),s.length===0)return".";var a=s.charCodeAt(0)===47,u=s.charCodeAt(s.length-1)===47;return s=r(s,!a),s.length===0&&!a&&(s="."),s.length>0&&u&&(s+="/"),a?"/"+s:s},isAbsolute:function(s){return e(s),s.length>0&&s.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var s,a=0;a<arguments.length;++a){var u=arguments[a];e(u),u.length>0&&(s===void 0?s=u:s+="/"+u)}return s===void 0?".":n.normalize(s)},relative:function(s,a){if(e(s),e(a),s===a||(s=n.resolve(s),a=n.resolve(a),s===a))return"";for(var u=1;u<s.length&&s.charCodeAt(u)===47;++u);for(var f=s.length,d=f-u,h=1;h<a.length&&a.charCodeAt(h)===47;++h);for(var g=a.length,b=g-h,E=d<b?d:b,w=-1,S=0;S<=E;++S){if(S===E){if(b>E){if(a.charCodeAt(h+S)===47)return a.slice(h+S+1);if(S===0)return a.slice(h+S)}else d>E&&(s.charCodeAt(u+S)===47?w=S:S===0&&(w=0));break}var I=s.charCodeAt(u+S),P=a.charCodeAt(h+S);if(I!==P)break;I===47&&(w=S)}var C="";for(S=u+w+1;S<=f;++S)(S===f||s.charCodeAt(S)===47)&&(C.length===0?C+="..":C+="/..");return C.length>0?C+a.slice(h+w):(h+=w,a.charCodeAt(h)===47&&++h,a.slice(h))},_makeLong:function(s){return s},dirname:function(s){if(e(s),s.length===0)return".";for(var a=s.charCodeAt(0),u=a===47,f=-1,d=!0,h=s.length-1;h>=1;--h)if(a=s.charCodeAt(h),a===47){if(!d){f=h;break}}else d=!1;return f===-1?u?"/":".":u&&f===1?"//":s.slice(0,f)},basename:function(s,a){if(a!==void 0&&typeof a!="string")throw new TypeError('"ext" argument must be a string');e(s);var u=0,f=-1,d=!0,h;if(a!==void 0&&a.length>0&&a.length<=s.length){if(a.length===s.length&&a===s)return"";var g=a.length-1,b=-1;for(h=s.length-1;h>=0;--h){var E=s.charCodeAt(h);if(E===47){if(!d){u=h+1;break}}else b===-1&&(d=!1,b=h+1),g>=0&&(E===a.charCodeAt(g)?--g===-1&&(f=h):(g=-1,f=b))}return u===f?f=b:f===-1&&(f=s.length),s.slice(u,f)}else{for(h=s.length-1;h>=0;--h)if(s.charCodeAt(h)===47){if(!d){u=h+1;break}}else f===-1&&(d=!1,f=h+1);return f===-1?"":s.slice(u,f)}},extname:function(s){e(s);for(var a=-1,u=0,f=-1,d=!0,h=0,g=s.length-1;g>=0;--g){var b=s.charCodeAt(g);if(b===47){if(!d){u=g+1;break}continue}f===-1&&(d=!1,f=g+1),b===46?a===-1?a=g:h!==1&&(h=1):a!==-1&&(h=-1)}return a===-1||f===-1||h===0||h===1&&a===f-1&&a===u+1?"":s.slice(a,f)},format:function(s){if(s===null||typeof s!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof s);return i("/",s)},parse:function(s){e(s);var a={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return a;var u=s.charCodeAt(0),f=u===47,d;f?(a.root="/",d=1):d=0;for(var h=-1,g=0,b=-1,E=!0,w=s.length-1,S=0;w>=d;--w){if(u=s.charCodeAt(w),u===47){if(!E){g=w+1;break}continue}b===-1&&(E=!1,b=w+1),u===46?h===-1?h=w:S!==1&&(S=1):h!==-1&&(S=-1)}return h===-1||b===-1||S===0||S===1&&h===b-1&&h===g+1?b!==-1&&(g===0&&f?a.base=a.name=s.slice(1,b):a.base=a.name=s.slice(g,b)):(g===0&&f?(a.name=s.slice(1,h),a.base=s.slice(1,b)):(a.name=s.slice(g,h),a.base=s.slice(g,b)),a.ext=s.slice(h,b)),g>0?a.dir=s.slice(0,g-1):f&&(a.dir="/"),a},sep:"/",delimiter:":",win32:null,posix:null};return n.posix=n,Ya=n,Ya}function F1(t){if(typeof t=="string")t=new URL(t);else if(!(t instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if(t.protocol!=="file:")throw new Deno.errors.InvalidData("invalid url scheme");return Xa?W1(t):$1(t)}function W1(t){let e=t.hostname,r=t.pathname;for(let i=0;i<r.length;i++)if(r[i]==="%"){let n=r.codePointAt(i+2)||32;if(r[i+1]==="2"&&n===102||r[i+1]==="5"&&n===99)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(L1,"\\"),r=decodeURIComponent(r),e!=="")return`\\\\${e}${r}`;{let i=r.codePointAt(1)|32,n=r[2];if(i<O1||i>M1||n!==":")throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}function $1(t){if(t.hostname!=="")throw new Deno.errors.InvalidData("invalid file url hostname");let e=t.pathname;for(let r=0;r<e.length;r++)if(e[r]==="%"){let i=e.codePointAt(r+2)||32;if(e[r+1]==="2"&&i===102)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(e)}function H1(t){let e=ag.resolve(t),r=t.charCodeAt(t.length-1);(r===x1||Xa&&r===B1)&&e[e.length-1]!==ag.sep&&(e+="/");let i=new URL("file://");return e.includes("%")&&(e=e.replace(q1,"%25")),!Xa&&e.includes("\\")&&(e=e.replace(U1,"%5C")),e.includes(`
`)&&(e=e.replace(N1,"%0A")),e.includes("\r")&&(e=e.replace(D1,"%0D")),e.includes("	")&&(e=e.replace(j1,"%09")),i.pathname=e,i}function lg(t){if(typeof t=="string")t=new URL(t);else if(!(t instanceof URL))throw new Deno.errors.InvalidData("invalid argument path , must be a string or URL");if(t.protocol!=="file:")throw new Deno.errors.InvalidData("invalid url scheme");return Za?lS(t):uS(t)}function lS(t){let e=t.hostname,r=t.pathname;for(let i=0;i<r.length;i++)if(r[i]==="%"){let n=r.codePointAt(i+2)||32;if(r[i+1]==="2"&&n===102||r[i+1]==="5"&&n===99)throw new Deno.errors.InvalidData("must not include encoded \\ or / characters")}if(r=r.replace(rS,"\\"),r=decodeURIComponent(r),e!=="")return`\\\\${e}${r}`;{let i=r.codePointAt(1)|32,n=r[2];if(i<eS||i>tS||n!==":")throw new Deno.errors.InvalidData("file url path must be absolute");return r.slice(1)}}function uS(t){if(t.hostname!=="")throw new Deno.errors.InvalidData("invalid file url hostname");let e=t.pathname;for(let r=0;r<e.length;r++)if(e[r]==="%"){let i=e.codePointAt(r+2)||32;if(e[r+1]==="2"&&i===102)throw new Deno.errors.InvalidData("must not include encoded / characters")}return decodeURIComponent(e)}function ug(t){let e=Ka.resolve(t),r=t.charCodeAt(t.length-1);(r===Z1||Za&&r===X1)&&e[e.length-1]!==Ka.sep&&(e+="/");let i=new URL("file://");return e.includes("%")&&(e=e.replace(iS,"%25")),!Za&&e.includes("\\")&&(e=e.replace(nS,"%5C")),e.includes(`
`)&&(e=e.replace(sS,"%0A")),e.includes("\r")&&(e=e.replace(oS,"%0D")),e.includes("	")&&(e=e.replace(aS,"%09")),i.pathname=e,i}var X,E1,ht,S1,A1,I1,T1,Ja,ig,ng,sg,R1,C1,Ga,ri,Qa,Ya,og,ag,k1,B1,x1,O1,M1,Xa,L1,q1,U1,N1,D1,j1,V1,z1,K1,G1,Q1,Y1,J1,X1,Z1,eS,tS,Za,rS,iS,nS,sS,oS,aS,fg=Ae(()=>{_();v();m();Kp();Gp();Zp();rg();Va();X={},E1=Vt,ht={isString:function(t){return typeof t=="string"},isObject:function(t){return typeof t=="object"&&t!==null},isNull:function(t){return t===null},isNullOrUndefined:function(t){return t==null}};X.parse=xi,X.resolve=function(t,e){return xi(t,!1,!0).resolve(e)},X.resolveObject=function(t,e){return t?xi(t,!1,!0).resolveObject(e):e},X.format=function(t){return ht.isString(t)&&(t=xi(t)),t instanceof De?t.format():De.prototype.format.call(t)},X.Url=De;S1=/^([a-z0-9.+-]+:)/i,A1=/:[0-9]*$/,I1=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,T1=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r",`
`,"	"]),Ja=["'"].concat(T1),ig=["%","/","?",";","#"].concat(Ja),ng=["/","?","#"],sg=/^[+a-z0-9A-Z_-]{0,63}$/,R1=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,C1={javascript:!0,"javascript:":!0},Ga={javascript:!0,"javascript:":!0},ri={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},Qa=ft;De.prototype.parse=function(t,e,r){if(!ht.isString(t))throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var i=t.indexOf("?"),n=i!==-1&&i<t.indexOf("#")?"?":"#",o=t.split(n);o[0]=o[0].replace(/\\/g,"/");var s=t=o.join(n);if(s=s.trim(),!r&&t.split("#").length===1){var a=I1.exec(s);if(a)return this.path=s,this.href=s,this.pathname=a[1],a[2]?(this.search=a[2],this.query=e?Qa.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var u=S1.exec(s);if(u){var f=(u=u[0]).toLowerCase();this.protocol=f,s=s.substr(u.length)}if(r||u||s.match(/^\/\/[^@\/]+@[^@\/]+/)){var d=s.substr(0,2)==="//";!d||u&&Ga[u]||(s=s.substr(2),this.slashes=!0)}if(!Ga[u]&&(d||u&&!ri[u])){for(var h,g,b=-1,E=0;E<ng.length;E++)(w=s.indexOf(ng[E]))!==-1&&(b===-1||w<b)&&(b=w);for((g=b===-1?s.lastIndexOf("@"):s.lastIndexOf("@",b))!==-1&&(h=s.slice(0,g),s=s.slice(g+1),this.auth=decodeURIComponent(h)),b=-1,E=0;E<ig.length;E++){var w;(w=s.indexOf(ig[E]))!==-1&&(b===-1||w<b)&&(b=w)}b===-1&&(b=s.length),this.host=s.slice(0,b),s=s.slice(b),this.parseHost(),this.hostname=this.hostname||"";var S=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!S)for(var I=this.hostname.split(/\./),P=(E=0,I.length);E<P;E++){var C=I[E];if(C&&!C.match(sg)){for(var M="",q=0,z=C.length;q<z;q++)C.charCodeAt(q)>127?M+="x":M+=C[q];if(!M.match(sg)){var j=I.slice(0,E),G=I.slice(E+1),$=C.match(R1);$&&(j.push($[1]),G.unshift($[2])),G.length&&(s="/"+G.join(".")+s),this.hostname=j.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),S||(this.hostname=E1.toASCII(this.hostname));var te=this.port?":"+this.port:"",pt=this.hostname||"";this.host=pt+te,this.href+=this.host,S&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),s[0]!=="/"&&(s="/"+s))}if(!C1[f])for(E=0,P=Ja.length;E<P;E++){var Fe=Ja[E];if(s.indexOf(Fe)!==-1){var Se=encodeURIComponent(Fe);Se===Fe&&(Se=escape(Fe)),s=s.split(Fe).join(Se)}}var vr=s.indexOf("#");vr!==-1&&(this.hash=s.substr(vr),s=s.slice(0,vr));var Er=s.indexOf("?");if(Er!==-1?(this.search=s.substr(Er),this.query=s.substr(Er+1),e&&(this.query=Qa.parse(this.query)),s=s.slice(0,Er)):e&&(this.search="",this.query={}),s&&(this.pathname=s),ri[f]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){te=this.pathname||"";var ls=this.search||"";this.path=te+ls}return this.href=this.format(),this},De.prototype.format=function(){var t=this.auth||"";t&&(t=(t=encodeURIComponent(t)).replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",i=this.hash||"",n=!1,o="";this.host?n=t+this.host:this.hostname&&(n=t+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(n+=":"+this.port)),this.query&&ht.isObject(this.query)&&Object.keys(this.query).length&&(o=Qa.stringify(this.query));var s=this.search||o&&"?"+o||"";return e&&e.substr(-1)!==":"&&(e+=":"),this.slashes||(!e||ri[e])&&n!==!1?(n="//"+(n||""),r&&r.charAt(0)!=="/"&&(r="/"+r)):n||(n=""),i&&i.charAt(0)!=="#"&&(i="#"+i),s&&s.charAt(0)!=="?"&&(s="?"+s),e+n+(r=r.replace(/[?#]/g,function(a){return encodeURIComponent(a)}))+(s=s.replace("#","%23"))+i},De.prototype.resolve=function(t){return this.resolveObject(xi(t,!1,!0)).format()},De.prototype.resolveObject=function(t){if(ht.isString(t)){var e=new De;e.parse(t,!1,!0),t=e}for(var r=new De,i=Object.keys(this),n=0;n<i.length;n++){var o=i[n];r[o]=this[o]}if(r.hash=t.hash,t.href==="")return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var s=Object.keys(t),a=0;a<s.length;a++){var u=s[a];u!=="protocol"&&(r[u]=t[u])}return ri[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!ri[t.protocol]){for(var f=Object.keys(t),d=0;d<f.length;d++){var h=f[d];r[h]=t[h]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||Ga[t.protocol])r.pathname=t.pathname;else{for(var g=(t.pathname||"").split("/");g.length&&!(t.host=g.shift()););t.host||(t.host=""),t.hostname||(t.hostname=""),g[0]!==""&&g.unshift(""),g.length<2&&g.unshift(""),r.pathname=g.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var b=r.pathname||"",E=r.search||"";r.path=b+E}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var w=r.pathname&&r.pathname.charAt(0)==="/",S=t.host||t.pathname&&t.pathname.charAt(0)==="/",I=S||w||r.host&&t.pathname,P=I,C=r.pathname&&r.pathname.split("/")||[],M=(g=t.pathname&&t.pathname.split("/")||[],r.protocol&&!ri[r.protocol]);if(M&&(r.hostname="",r.port=null,r.host&&(C[0]===""?C[0]=r.host:C.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(g[0]===""?g[0]=t.host:g.unshift(t.host)),t.host=null),I=I&&(g[0]===""||C[0]==="")),S)r.host=t.host||t.host===""?t.host:r.host,r.hostname=t.hostname||t.hostname===""?t.hostname:r.hostname,r.search=t.search,r.query=t.query,C=g;else if(g.length)C||(C=[]),C.pop(),C=C.concat(g),r.search=t.search,r.query=t.query;else if(!ht.isNullOrUndefined(t.search))return M&&(r.hostname=r.host=C.shift(),($=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=$.shift(),r.host=r.hostname=$.shift())),r.search=t.search,r.query=t.query,ht.isNull(r.pathname)&&ht.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r;if(!C.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var q=C.slice(-1)[0],z=(r.host||t.host||C.length>1)&&(q==="."||q==="..")||q==="",j=0,G=C.length;G>=0;G--)(q=C[G])==="."?C.splice(G,1):q===".."?(C.splice(G,1),j++):j&&(C.splice(G,1),j--);if(!I&&!P)for(;j--;j)C.unshift("..");!I||C[0]===""||C[0]&&C[0].charAt(0)==="/"||C.unshift(""),z&&C.join("/").substr(-1)!=="/"&&C.push("");var $,te=C[0]===""||C[0]&&C[0].charAt(0)==="/";return M&&(r.hostname=r.host=te?"":C.length?C.shift():"",($=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=$.shift(),r.host=r.hostname=$.shift())),(I=I||r.host&&C.length)&&!te&&C.unshift(""),C.length?r.pathname=C.join("/"):(r.pathname=null,r.path=null),ht.isNull(r.pathname)&&ht.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},De.prototype.parseHost=function(){var t=this.host,e=A1.exec(t);e&&((e=e[0])!==":"&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)};X.Url;X.format;X.resolve;X.resolveObject;Ya={},og=!1;ag=P1(),k1=typeof Deno<"u"?Deno.build.os==="windows"?"win32":Deno.build.os:void 0;X.URL=typeof URL<"u"?URL:null;X.pathToFileURL=H1;X.fileURLToPath=F1;X.Url;X.format;X.resolve;X.resolveObject;X.URL;B1=92,x1=47,O1=97,M1=122,Xa=k1==="win32",L1=/\//g,q1=/%/g,U1=/\\/g,N1=/\n/g,D1=/\r/g,j1=/\t/g;V1=typeof Deno<"u"?Deno.build.os==="windows"?"win32":Deno.build.os:void 0;X.URL=typeof URL<"u"?URL:null;X.pathToFileURL=ug;X.fileURLToPath=lg;z1=X.Url,K1=X.format,G1=X.resolve,Q1=X.resolveObject,Y1=X.parse,J1=X.URL,X1=92,Z1=47,eS=97,tS=122,Za=V1==="win32",rS=/\//g,iS=/%/g,nS=/\\/g,sS=/\n/g,oS=/\r/g,aS=/\t/g});var dg=O((pq,hg)=>{"use strict";_();v();m();hg.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}});var is=O(Oi=>{"use strict";_();v();m();Object.defineProperty(Oi,"__esModule",{value:!0});Oi.BufferedDuplex=void 0;Oi.writev=gg;var cS=Nt(),pg=(he(),Q(ye));function gg(t,e){let r=new Array(t.length);for(let i=0;i<t.length;i++)typeof t[i].chunk=="string"?r[i]=pg.Buffer.from(t[i].chunk,"utf8"):r[i]=t[i].chunk;this._write(pg.Buffer.concat(r),"binary",e)}var el=class extends cS.Duplex{constructor(e,r,i){super({objectMode:!0}),this.proxy=r,this.socket=i,this.writeQueue=[],e.objectMode||(this._writev=gg.bind(this)),this.isSocketOpen=!1,this.proxy.on("data",n=>{!this.destroyed&&this.readable&&this.push(n)})}_read(e){this.proxy.read(e)}_write(e,r,i){this.isSocketOpen?this.writeToProxy(e,r,i):this.writeQueue.push({chunk:e,encoding:r,cb:i})}_final(e){this.writeQueue=[],this.proxy.end(e)}_destroy(e,r){this.writeQueue=[],this.proxy.destroy(),r(e)}socketReady(){this.emit("connect"),this.isSocketOpen=!0,this.processWriteQueue()}writeToProxy(e,r,i){this.proxy.write(e,r)===!1?this.proxy.once("drain",i):i()}processWriteQueue(){for(;this.writeQueue.length>0;){let{chunk:e,encoding:r,cb:i}=this.writeQueue.shift();this.writeToProxy(e,r,i)}}};Oi.BufferedDuplex=el});var Mi=O(Kt=>{"use strict";_();v();m();var rl=Kt&&Kt.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.streamBuilder=Kt.browserStreamBuilder=void 0;var ns=(he(),Q(ye)),bg=rl(dg()),fS=rl(nt()),hS=Nt(),dS=rl(Ci()),tl=is(),zt=(0,fS.default)("mqttjs:ws"),pS=["rejectUnauthorized","ca","cert","key","pfx","passphrase"];function yg(t,e){let r=`${t.protocol}://${t.hostname}:${t.port}${t.path}`;return typeof t.transformWsUrl=="function"&&(r=t.transformWsUrl(r,t,e)),r}function wg(t){let e=t;return t.port||(t.protocol==="wss"?e.port=443:e.port=80),t.path||(e.path="/"),t.wsOptions||(e.wsOptions={}),!dS.default&&!t.forceNativeWebSocket&&t.protocol==="wss"&&pS.forEach(r=>{Object.prototype.hasOwnProperty.call(t,r)&&!Object.prototype.hasOwnProperty.call(t.wsOptions,r)&&(e.wsOptions[r]=t[r])}),e}function gS(t){let e=wg(t);if(e.hostname||(e.hostname=e.host),!e.hostname){if(typeof document>"u")throw new Error("Could not determine host. Specify host manually.");let r=new URL(document.URL);e.hostname=r.hostname,e.port||(e.port=Number(r.port))}return e.objectMode===void 0&&(e.objectMode=!(e.binary===!0||e.binary===void 0)),e}function bS(t,e,r){zt("createWebSocket"),zt(`protocol: ${r.protocolId} ${r.protocolVersion}`);let i=r.protocolId==="MQIsdp"&&r.protocolVersion===3?"mqttv3.1":"mqtt";zt(`creating new Websocket for url: ${e} and protocol: ${i}`);let n;return r.createWebsocket?n=r.createWebsocket(e,[i],r):n=new bg.default(e,[i],r.wsOptions),n}function yS(t,e){let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt",i=yg(e,t),n;return e.createWebsocket?n=e.createWebsocket(i,[r],e):n=new WebSocket(i,[r]),n.binaryType="arraybuffer",n}var wS=(t,e)=>{zt("streamBuilder");let r=wg(e);r.hostname=r.hostname||r.host||"localhost";let i=yg(r,t),n=bS(t,i,r),o=bg.default.createWebSocketStream(n,r.wsOptions);return o.url=i,n.on("close",()=>{o.destroy()}),o};Kt.streamBuilder=wS;var _S=(t,e)=>{zt("browserStreamBuilder");let r,n=gS(e).browserBufferSize||1024*512,o=e.browserBufferTimeout||1e3,s=!e.objectMode,a=yS(t,e),u=d(e,w,S);e.objectMode||(u._writev=tl.writev.bind(u)),u.on("close",()=>{a.close()});let f=typeof a.addEventListener<"u";a.readyState===a.OPEN?(r=u,r.socket=a):(r=new tl.BufferedDuplex(e,u,a),f?a.addEventListener("open",h):a.onopen=h),f?(a.addEventListener("close",g),a.addEventListener("error",b),a.addEventListener("message",E)):(a.onclose=g,a.onerror=b,a.onmessage=E);function d(I,P,C){let M=new hS.Transform({objectMode:I.objectMode});return M._write=P,M._flush=C,M}function h(){zt("WebSocket onOpen"),r instanceof tl.BufferedDuplex&&r.socketReady()}function g(I){zt("WebSocket onClose",I),r.end(),r.destroy()}function b(I){zt("WebSocket onError",I);let P=new Error("WebSocket error");P.event=I,r.destroy(P)}async function E(I){if(!u||u.destroyed||!u.readable)return;let{data:P}=I;P instanceof ArrayBuffer?P=ns.Buffer.from(P):P instanceof Blob?P=ns.Buffer.from(await new Response(P).arrayBuffer()):P=ns.Buffer.from(P,"utf8"),u.push(P)}function w(I,P,C){if(a.bufferedAmount>n){setTimeout(w,o,I,P,C);return}s&&typeof I=="string"&&(I=ns.Buffer.from(I,"utf8"));try{a.send(I)}catch(M){return C(M)}C()}function S(I){a.close(),I()}return r};Kt.browserStreamBuilder=_S});var il={};Ir(il,{Server:()=>xe,Socket:()=>xe,Stream:()=>xe,_createServerHandle:()=>xe,_normalizeArgs:()=>xe,_setSimultaneousAccepts:()=>xe,connect:()=>xe,createConnection:()=>xe,createServer:()=>xe,default:()=>mS,isIP:()=>xe,isIPv4:()=>xe,isIPv6:()=>xe});function xe(){throw new Error("Node.js net module is not supported by JSPM core outside of Node.js")}var mS,nl=Ae(()=>{_();v();m();mS={_createServerHandle:xe,_normalizeArgs:xe,_setSimultaneousAccepts:xe,connect:xe,createConnection:xe,createServer:xe,isIP:xe,isIPv4:xe,isIPv6:xe,Server:xe,Socket:xe,Stream:xe}});var sl=O((Fq,_g)=>{_();v();m();_g.exports={}});var al=O(Li=>{"use strict";_();v();m();var ol=Li&&Li.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(Li,"__esModule",{value:!0});var vS=ol((nl(),Q(il))),ES=ol(nt()),SS=ol(sl()),AS=(0,ES.default)("mqttjs:tcp"),IS=(t,e)=>{if(e.port=e.port||1883,e.hostname=e.hostname||e.host||"localhost",e.socksProxy)return(0,SS.default)(e.hostname,e.port,e.socksProxy,{timeout:e.socksTimeout});let{port:r,path:i}=e,n=e.hostname;return AS("port %d and host %s",r,n),vS.default.createConnection({port:r,host:n,path:i})};Li.default=IS});var mg={};Ir(mg,{default:()=>TS});var TS,vg=Ae(()=>{_();v();m();TS={}});var ll=O(mr=>{"use strict";_();v();m();var RS=mr&&mr.__rest||function(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(t);n<i.length;n++)e.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(t,i[n])&&(r[i[n]]=t[i[n]]);return r},ss=mr&&mr.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(mr,"__esModule",{value:!0});var CS=ss((vg(),Q(mg))),PS=ss((nl(),Q(il))),kS=ss(nt()),BS=ss(sl()),xS=(0,kS.default)("mqttjs:tls");function OS(t){let{host:e,port:r,socksProxy:i}=t,n=RS(t,["host","port","socksProxy"]);return CS.default.connect(i?Object.assign(Object.assign({},n),{socket:(0,BS.default)(e,r,i,{timeout:t.socksTimeout})}):t)}var MS=(t,e)=>{e.port=e.port||8883,e.host=e.hostname||e.host||"localhost",PS.default.isIP(e.host)===0&&(e.servername=e.host),e.rejectUnauthorized=e.rejectUnauthorized!==!1,delete e.path,xS("port %d host %s rejectUnauthorized %b",e.port,e.host,e.rejectUnauthorized);let r=OS(e);r.on("secureConnect",()=>{e.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",i)});function i(n){e.rejectUnauthorized&&t.emit("error",n),r.end()}return r.on("error",i),r};mr.default=MS});var fl=O(cl=>{"use strict";_();v();m();Object.defineProperty(cl,"__esModule",{value:!0});var Eg=(he(),Q(ye)),LS=Nt(),qS=is(),dt,ul,Oe;function US(){let t=new LS.Transform;return t._write=(e,r,i)=>{dt.send({data:e.buffer,success(){i()},fail(n){i(new Error(n))}})},t._flush=e=>{dt.close({success(){e()}})},t}function NS(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function DS(t,e){let r=t.protocol==="wxs"?"wss":"ws",i=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(i=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(i=t.transformWsUrl(i,t,e)),i}function jS(){dt.onOpen(()=>{Oe.socketReady()}),dt.onMessage(t=>{let{data:e}=t;e instanceof ArrayBuffer?e=Eg.Buffer.from(e):e=Eg.Buffer.from(e,"utf8"),ul.push(e)}),dt.onClose(()=>{Oe.emit("close"),Oe.end(),Oe.destroy()}),dt.onError(t=>{let e=new Error(t.errMsg);Oe.destroy(e)})}var FS=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";NS(e);let i=DS(e,t);dt=wx.connectSocket({url:i,protocols:[r]}),ul=US(),Oe=new qS.BufferedDuplex(e,ul,dt),Oe._destroy=(o,s)=>{dt.close({success(){s&&s(o)}})};let n=Oe.destroy;return Oe.destroy=(o,s)=>(Oe.destroy=n,setTimeout(()=>{dt.close({fail(){Oe._destroy(o,s)}})},0),Oe),jS(),Oe};cl.default=FS});var pl=O(dl=>{"use strict";_();v();m();Object.defineProperty(dl,"__esModule",{value:!0});var hl=(he(),Q(ye)),WS=Nt(),$S=is(),Ct,os,ii,Sg=!1;function HS(){let t=new WS.Transform;return t._write=(e,r,i)=>{Ct.sendSocketMessage({data:e.buffer,success(){i()},fail(){i(new Error)}})},t._flush=e=>{Ct.closeSocket({success(){e()}})},t}function VS(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function zS(t,e){let r=t.protocol==="alis"?"wss":"ws",i=`${r}://${t.hostname}${t.path}`;return t.port&&t.port!==80&&t.port!==443&&(i=`${r}://${t.hostname}:${t.port}${t.path}`),typeof t.transformWsUrl=="function"&&(i=t.transformWsUrl(i,t,e)),i}function KS(){Sg||(Sg=!0,Ct.onSocketOpen(()=>{ii.socketReady()}),Ct.onSocketMessage(t=>{if(typeof t.data=="string"){let e=hl.Buffer.from(t.data,"base64");os.push(e)}else{let e=new FileReader;e.addEventListener("load",()=>{let r=e.result;r instanceof ArrayBuffer?r=hl.Buffer.from(r):r=hl.Buffer.from(r,"utf8"),os.push(r)}),e.readAsArrayBuffer(t.data)}}),Ct.onSocketClose(()=>{ii.end(),ii.destroy()}),Ct.onSocketError(t=>{ii.destroy(t)}))}var GS=(t,e)=>{if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");let r=e.protocolId==="MQIsdp"&&e.protocolVersion===3?"mqttv3.1":"mqtt";VS(e);let i=zS(e,t);return Ct=e.my,Ct.connectSocket({url:i,protocols:r}),os=HS(),ii=new $S.BufferedDuplex(e,os,Ct),KS(),ii};dl.default=GS});var Rg=O(ni=>{"use strict";_();v();m();var as=ni&&ni.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ni,"__esModule",{value:!0});ni.connectAsync=ZS;var QS=as(nt()),YS=as((fg(),Q(cg))),JS=as(ts()),Ag=as(Ci());typeof(R===null||R===void 0?void 0:R.nextTick)!="function"&&(R.nextTick=setImmediate);var Ig=(0,QS.default)("mqttjs"),ge=null;function XS(t){let e;t.auth&&(e=t.auth.match(/^(.+):(.+)$/),e?(t.username=e[1],t.password=e[2]):t.username=t.auth)}function Tg(t,e){var r,i,n;if(Ig("connecting to an MQTT broker..."),typeof t=="object"&&!e&&(e=t,t=""),e=e||{},t&&typeof t=="string"){let a=YS.default.parse(t,!0),u={};if(a.port!=null&&(u.port=Number(a.port)),u.host=a.hostname,u.query=a.query,u.auth=a.auth,u.protocol=a.protocol,u.path=a.path,e=Object.assign(Object.assign({},u),e),!e.protocol)throw new Error("Missing protocol");e.protocol=e.protocol.replace(/:$/,"")}if(e.unixSocket=e.unixSocket||((r=e.protocol)===null||r===void 0?void 0:r.includes("+unix")),e.unixSocket?e.protocol=e.protocol.replace("+unix",""):!(!((i=e.protocol)===null||i===void 0)&&i.startsWith("ws"))&&!(!((n=e.protocol)===null||n===void 0)&&n.startsWith("wx"))&&delete e.path,XS(e),e.query&&typeof e.query.clientId=="string"&&(e.clientId=e.query.clientId),Ag.default||e.unixSocket?e.socksProxy=void 0:e.socksProxy===void 0&&typeof R<"u"&&(e.socksProxy=R.env.MQTTJS_SOCKS_PROXY),e.cert&&e.key)if(e.protocol){if(["mqtts","wss","wxs","alis"].indexOf(e.protocol)===-1)switch(e.protocol){case"mqtt":e.protocol="mqtts";break;case"ws":e.protocol="wss";break;case"wx":e.protocol="wxs";break;case"ali":e.protocol="alis";break;default:throw new Error(`Unknown protocol for secure connection: "${e.protocol}"!`)}}else throw new Error("Missing secure protocol key");if(ge||(ge={},!Ag.default&&!e.forceNativeWebSocket?(ge.ws=Mi().streamBuilder,ge.wss=Mi().streamBuilder,ge.mqtt=al().default,ge.tcp=al().default,ge.ssl=ll().default,ge.tls=ge.ssl,ge.mqtts=ll().default):(ge.ws=Mi().browserStreamBuilder,ge.wss=Mi().browserStreamBuilder,ge.wx=fl().default,ge.wxs=fl().default,ge.ali=pl().default,ge.alis=pl().default)),!ge[e.protocol]){let a=["mqtts","wss"].indexOf(e.protocol)!==-1;e.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((u,f)=>a&&f%2===0?!1:typeof ge[u]=="function")[0]}if(e.clean===!1&&!e.clientId)throw new Error("Missing clientId for unclean clients");e.protocol&&(e.defaultProtocol=e.protocol);function o(a){return e.servers&&((!a._reconnectCount||a._reconnectCount===e.servers.length)&&(a._reconnectCount=0),e.host=e.servers[a._reconnectCount].host,e.port=e.servers[a._reconnectCount].port,e.protocol=e.servers[a._reconnectCount].protocol?e.servers[a._reconnectCount].protocol:e.defaultProtocol,e.hostname=e.host,a._reconnectCount++),Ig("calling streambuilder for",e.protocol),ge[e.protocol](a,e)}let s=new JS.default(o,e);return s.on("error",()=>{}),s}function ZS(t,e,r=!0){return new Promise((i,n)=>{let o=Tg(t,e),s={connect:u=>{a(),i(o)},end:()=>{a(),i(o)},error:u=>{a(),o.end(),n(u)}};r===!1&&(s.close=()=>{s.error(new Error("Couldn't connect to server"))});function a(){Object.keys(s).forEach(u=>{o.off(u,s[u])})}Object.keys(s).forEach(u=>{o.on(u,s[u])})})}ni.default=Tg});var gl=O(K=>{"use strict";_();v();m();var Cg=K&&K.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),eA=K&&K.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),tA=K&&K.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var i=[];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i[i.length]=n);return i},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var i=t(e),n=0;n<i.length;n++)i[n]!=="default"&&Cg(r,e,i[n]);return eA(r,e),r}}(),Pg=K&&K.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&Cg(e,t,r)},qi=K&&K.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(K,"__esModule",{value:!0});K.ReasonCodes=K.KeepaliveManager=K.UniqueMessageIdProvider=K.DefaultMessageIdProvider=K.Store=K.MqttClient=K.connectAsync=K.connect=K.Client=void 0;var kg=qi(ts());K.MqttClient=kg.default;var rA=qi(zo());K.DefaultMessageIdProvider=rA.default;var iA=qi(jp());K.UniqueMessageIdProvider=iA.default;var nA=qi(Qo());K.Store=nA.default;var Bg=tA(Rg());K.connect=Bg.default;Object.defineProperty(K,"connectAsync",{enumerable:!0,get:function(){return Bg.connectAsync}});var sA=qi(qa());K.KeepaliveManager=sA.default;K.Client=kg.default;Pg(ts(),K);Pg(cr(),K);var oA=Si();Object.defineProperty(K,"ReasonCodes",{enumerable:!0,get:function(){return oA.ReasonCodes}})});var fA=O(je=>{_();v();m();var xg=je&&je.__createBinding||(Object.create?function(t,e,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);(!n||("get"in n?!e.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){i===void 0&&(i=r),t[i]=e[r]}),aA=je&&je.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),lA=je&&je.__importStar||function(){var t=function(e){return t=Object.getOwnPropertyNames||function(r){var i=[];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(i[i.length]=n);return i},t(e)};return function(e){if(e&&e.__esModule)return e;var r={};if(e!=null)for(var i=t(e),n=0;n<i.length;n++)i[n]!=="default"&&xg(r,e,i[n]);return aA(r,e),r}}(),uA=je&&je.__exportStar||function(t,e){for(var r in t)r!=="default"&&!Object.prototype.hasOwnProperty.call(e,r)&&xg(e,t,r)};Object.defineProperty(je,"__esModule",{value:!0});var cA=lA(gl());je.default=cA;uA(gl(),je)});return fA();})();
/*! Bundled license information:

@jspm/core/nodelibs/browser/buffer.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
