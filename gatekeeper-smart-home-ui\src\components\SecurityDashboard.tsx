import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Users, 
  Activity,
  Lock,
  Unlock,
  Ban,
  CheckCircle,
  XCircle,
  Clock,
  Globe
} from 'lucide-react';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { supabase } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';

interface SecurityLog {
  id: string;
  event: string;
  details: any;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  timestamp: string;
}

interface LoginLog {
  id: string;
  user_id: string;
  success: boolean;
  timestamp: string;
  ip_address?: string;
  user_agent?: string;
}

const SecurityDashboard: React.FC = () => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [loginLogs, setLoginLogs] = useState<LoginLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    failedLogins24h: 0,
    securityEvents24h: 0,
    suspiciousActivity: 0
  });

  // Kontrola admin oprávnění
  if (!profile?.permissions?.includes('admin')) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Nemáte oprávnění k zobrazení bezpečnostního dashboardu.
        </AlertDescription>
      </Alert>
    );
  }

  useEffect(() => {
    fetchSecurityData();
    const interval = setInterval(fetchSecurityData, 30000); // Aktualizace každých 30 sekund
    return () => clearInterval(interval);
  }, []);

  const fetchSecurityData = async () => {
    setLoading(true);
    try {
      // Načtení bezpečnostních logů
      const { data: secLogs, error: secError } = await supabase
        .from('security_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(50);

      if (secError) throw secError;
      setSecurityLogs(secLogs || []);

      // Načtení login logů
      const { data: loginLogsData, error: loginError } = await supabase
        .from('login_logs')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(100);

      if (loginError) throw loginError;
      setLoginLogs(loginLogsData || []);

      // Výpočet statistik
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const failedLogins24h = loginLogsData?.filter(log => 
        !log.success && new Date(log.timestamp) > yesterday
      ).length || 0;

      const securityEvents24h = secLogs?.filter(log => 
        new Date(log.timestamp) > yesterday
      ).length || 0;

      const suspiciousActivity = secLogs?.filter(log => 
        log.event.includes('SUSPICIOUS') || 
        log.event.includes('BLOCKED') ||
        log.event.includes('INVALID')
      ).length || 0;

      // Načtení počtu uživatelů
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      const { count: activeUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      setStats({
        totalUsers: totalUsers || 0,
        activeUsers: activeUsers || 0,
        failedLogins24h,
        securityEvents24h,
        suspiciousActivity
      });

    } catch (error) {
      console.error('Chyba při načítání bezpečnostních dat:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se načíst bezpečnostní data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (event: string) => {
    if (event.includes('LOGIN')) return <Users className="h-4 w-4" />;
    if (event.includes('SUSPICIOUS') || event.includes('BLOCKED')) return <Ban className="h-4 w-4" />;
    if (event.includes('FAILED') || event.includes('INVALID')) return <XCircle className="h-4 w-4" />;
    if (event.includes('SUCCESS')) return <CheckCircle className="h-4 w-4" />;
    return <Activity className="h-4 w-4" />;
  };

  const getEventColor = (event: string) => {
    if (event.includes('SUSPICIOUS') || event.includes('BLOCKED')) return 'destructive';
    if (event.includes('FAILED') || event.includes('INVALID')) return 'secondary';
    if (event.includes('SUCCESS')) return 'default';
    return 'outline';
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('cs-CZ');
  };

  const truncateString = (str: string, maxLength: number = 50) => {
    return str.length > maxLength ? str.substring(0, maxLength) + '...' : str;
  };

  return (
    <div className="space-y-6">
      {/* Statistiky */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Celkem uživatelů</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktivní uživatelé</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeUsers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Neúspěšná přihlášení (24h)</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.failedLogins24h}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bezpečnostní události (24h)</CardTitle>
            <Shield className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.securityEvents24h}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Podezřelá aktivita</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.suspiciousActivity}</div>
          </CardContent>
        </Card>
      </div>

      {/* Bezpečnostní logy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Bezpečnostní události
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {securityLogs.map((log) => (
              <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getEventIcon(log.event)}
                  <div>
                    <Badge variant={getEventColor(log.event) as any}>
                      {log.event}
                    </Badge>
                    <p className="text-sm text-muted-foreground mt-1">
                      {log.details && typeof log.details === 'object' 
                        ? JSON.stringify(log.details).substring(0, 100) + '...'
                        : truncateString(log.details || '', 100)
                      }
                    </p>
                  </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatTimestamp(log.timestamp)}
                  </div>
                  {log.ip_address && (
                    <div className="flex items-center gap-1 mt-1">
                      <Globe className="h-3 w-3" />
                      {log.ip_address}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Login logy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Přihlašovací aktivity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {loginLogs.map((log) => (
              <div key={log.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {log.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <div>
                    <Badge variant={log.success ? 'default' : 'destructive'}>
                      {log.success ? 'Úspěšné přihlášení' : 'Neúspěšné přihlášení'}
                    </Badge>
                    <p className="text-sm text-muted-foreground mt-1">
                      Uživatel: {log.user_id}
                    </p>
                  </div>
                </div>
                <div className="text-right text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatTimestamp(log.timestamp)}
                  </div>
                  {log.ip_address && (
                    <div className="flex items-center gap-1 mt-1">
                      <Globe className="h-3 w-3" />
                      {log.ip_address}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Akce */}
      <Card>
        <CardHeader>
          <CardTitle>Bezpečnostní akce</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button onClick={fetchSecurityData} disabled={loading}>
              <Activity className="h-4 w-4 mr-2" />
              Obnovit data
            </Button>
            <Button variant="outline" onClick={() => {
              // Implementace exportu logů
              toast({
                title: "Export",
                description: "Export logů bude implementován v další verzi"
              });
            }}>
              Export logů
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityDashboard;
