import { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  UserPlus, 
  Shield, 
  Eye, 
  EyeOff, 
  Check, 
  AlertTriangle,
  Mail
} from 'lucide-react';

const API_BASE = 'http://localhost:3000/api';

const RegisterPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const inviteToken = searchParams.get('invite');
  
  const [inviteData, setInviteData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
    email: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);

  const availablePermissions = [
    { id: 'gate', label: 'Ovládání brány', description: 'Může ovládat posuvnou bránu' },
    { id: 'camera', label: 'Kamera', description: 'Může zobrazovat kamerový přenos' },
    { id: 'garage', label: 'Garáž', description: 'Může ovládat garáž' },
    { id: 'admin', label: 'Administrátor', description: 'Může spravovat uživatele a systém' }
  ];

  useEffect(() => {
    if (!inviteToken) {
      navigate('/login');
      return;
    }
    
    verifyInvite();
  }, [inviteToken, navigate]);

  const verifyInvite = async () => {
    try {
      const response = await fetch(`${API_BASE}/auth/verify-invite?token=${inviteToken}`);
      
      if (response.ok) {
        const data = await response.json();
        setInviteData(data);
      } else {
        const error = await response.json();
        alert(error.error || 'Neplatná nebo expirovaná pozvánka');
        navigate('/login');
      }
    } catch (error) {
      console.error('Chyba při ověřování pozvánky:', error);
      alert('Chyba při ověřování pozvánky');
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Uživatelské jméno je povinné';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Uživatelské jméno musí mít alespoň 3 znaky';
    }

    if (!formData.password) {
      newErrors.password = 'Heslo je povinné';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Heslo musí mít alespoň 6 znaků';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Hesla se neshodují';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Neplatný formát emailu';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      const response = await fetch(`${API_BASE}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          inviteToken,
          username: formData.username,
          password: formData.password,
          email: formData.email || undefined
        })
      });

      const data = await response.json();

      if (response.ok) {
        alert('Registrace byla úspěšná! Nyní se můžete přihlásit.');
        navigate('/login');
      } else {
        alert(data.error || 'Chyba při registraci');
      }
    } catch (error) {
      console.error('Chyba při registraci:', error);
      alert('Chyba při registraci');
    } finally {
      setSubmitting(false);
    }
  };

  const getPermissionBadgeColor = (permission: string) => {
    switch (permission) {
      case 'admin': return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'gate': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'camera': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'garage': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white">Ověřuji pozvánku...</div>
      </div>
    );
  }

  if (!inviteData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <Card className="backdrop-blur-lg bg-white/10 border-red-500/20 w-full max-w-md">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-2">Neplatná pozvánka</h2>
            <p className="text-red-300 mb-4">Pozvánka je neplatná nebo vypršela.</p>
            <Button onClick={() => navigate('/login')} className="w-full">
              Zpět na přihlášení
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
      
      <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
        <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20 w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-green-500/20 border border-green-500/30">
                <Mail className="w-8 h-8 text-green-300" />
              </div>
            </div>
            <CardTitle className="text-white text-2xl">Dokončit registraci</CardTitle>
            <p className="text-purple-300 text-sm">
              Byli jste pozváni k přístupu do IoT Control systému
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Informace o pozvánce */}
            <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
              <h3 className="text-green-300 font-medium mb-2 flex items-center">
                <Shield className="w-4 h-4 mr-2" />
                Vaše oprávnění
              </h3>
              <div className="flex flex-wrap gap-1">
                {inviteData.permissions.map((permission: string) => (
                  <Badge
                    key={permission}
                    className={`text-xs ${getPermissionBadgeColor(permission)}`}
                  >
                    {availablePermissions.find(p => p.id === permission)?.label || permission}
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-green-400 mt-2">
                Pozvánka vyprší: {new Date(inviteData.expiresAt).toLocaleString('cs-CZ')}
              </p>
            </div>

            {/* Registrační formulář */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label className="text-purple-200">Uživatelské jméno *</Label>
                <Input
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  className={`bg-white/10 border-purple-500/30 text-white ${
                    errors.username ? 'border-red-500/50' : ''
                  }`}
                  placeholder="Vaše uživatelské jméno"
                />
                {errors.username && (
                  <p className="text-red-400 text-xs mt-1">{errors.username}</p>
                )}
              </div>

              <div>
                <Label className="text-purple-200">Email (volitelný)</Label>
                <Input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  className={`bg-white/10 border-purple-500/30 text-white ${
                    errors.email ? 'border-red-500/50' : ''
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-400 text-xs mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <Label className="text-purple-200">Heslo *</Label>
                <div className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className={`bg-white/10 border-purple-500/30 text-white pr-10 ${
                      errors.password ? 'border-red-500/50' : ''
                    }`}
                    placeholder="Minimálně 6 znaků"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-300 hover:text-white"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-400 text-xs mt-1">{errors.password}</p>
                )}
              </div>

              <div>
                <Label className="text-purple-200">Potvrdit heslo *</Label>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className={`bg-white/10 border-purple-500/30 text-white pr-10 ${
                      errors.confirmPassword ? 'border-red-500/50' : ''
                    }`}
                    placeholder="Zopakujte heslo"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-purple-300 hover:text-white"
                  >
                    {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-400 text-xs mt-1">{errors.confirmPassword}</p>
                )}
              </div>

              <Button
                type="submit"
                disabled={submitting}
                className="w-full bg-green-500/20 hover:bg-green-500/30 text-green-300 border border-green-500/30"
              >
                {submitting ? (
                  'Registruji...'
                ) : (
                  <>
                    <UserPlus className="w-4 h-4 mr-2" />
                    Dokončit registraci
                  </>
                )}
              </Button>
            </form>

            <div className="text-center">
              <Button
                onClick={() => navigate('/login')}
                variant="ghost"
                className="text-purple-300 hover:text-white"
              >
                Zpět na přihlášení
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;
