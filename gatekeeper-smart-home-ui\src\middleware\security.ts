// Bezpečnostní middleware pro React aplikaci
import { supabase } from '../lib/supabase';

// Rate limiting pro frontend
class RateLimiter {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      this.attempts.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return true;
    }

    if (record.count >= this.maxAttempts) {
      return false;
    }

    record.count++;
    return true;
  }

  getRemainingTime(identifier: string): number {
    const record = this.attempts.get(identifier);
    if (!record) return 0;
    return Math.max(0, record.resetTime - Date.now());
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Instance rate limiteru pro různé akce
export const loginLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 pokusů za 15 minut
export const apiLimiter = new RateLimiter(100, 60 * 1000); // 100 požadavků za minutu

// Validace vstupů
export const validateInput = {
  email: (email: string): { valid: boolean; error?: string } => {
    if (!email) return { valid: false, error: 'Email je povinný' };
    if (email.length > 254) return { valid: false, error: 'Email je příliš dlouhý' };
    
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!emailRegex.test(email)) return { valid: false, error: 'Neplatný formát emailu' };
    
    return { valid: true };
  },

  password: (password: string): { valid: boolean; error?: string; strength: number } => {
    if (!password) return { valid: false, error: 'Heslo je povinné', strength: 0 };
    if (password.length < 8) return { valid: false, error: 'Heslo musí mít alespoň 8 znaků', strength: 1 };
    if (password.length > 128) return { valid: false, error: 'Heslo je příliš dlouhé', strength: 0 };

    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^a-zA-Z0-9]/.test(password)) strength++;

    if (strength < 3) {
      return { valid: false, error: 'Heslo musí obsahovat alespoň 3 z: malá písmena, velká písmena, čísla, speciální znaky', strength };
    }

    // Kontrola běžných hesel
    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein', 'welcome'];
    if (commonPasswords.some(common => password.toLowerCase().includes(common))) {
      return { valid: false, error: 'Heslo nesmí obsahovat běžná slova', strength };
    }

    return { valid: true, strength };
  },

  username: (username: string): { valid: boolean; error?: string } => {
    if (!username) return { valid: false, error: 'Uživatelské jméno je povinné' };
    if (username.length < 3) return { valid: false, error: 'Uživatelské jméno musí mít alespoň 3 znaky' };
    if (username.length > 50) return { valid: false, error: 'Uživatelské jméno je příliš dlouhé' };
    
    const usernameRegex = /^[a-zA-Z0-9_.-]+$/;
    if (!usernameRegex.test(username)) {
      return { valid: false, error: 'Uživatelské jméno může obsahovat pouze písmena, čísla, tečky, pomlčky a podtržítka' };
    }

    return { valid: true };
  },

  sanitizeString: (input: string, maxLength: number = 255): string => {
    if (!input) return '';
    return input.trim().slice(0, maxLength).replace(/[<>]/g, '');
  }
};

// Bezpečnostní logger
export const securityLogger = {
  logSecurityEvent: async (event: string, details: any = {}) => {
    const logData = {
      event,
      details: JSON.stringify(details),
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      ip_address: 'client-side', // Na serveru by bylo skutečné IP
    };

    try {
      await supabase.from('security_logs').insert(logData);
    } catch (error) {
      console.error('Chyba při logování bezpečnostní události:', error);
    }
  },

  logLoginAttempt: async (email: string, success: boolean, reason?: string) => {
    await securityLogger.logSecurityEvent('LOGIN_ATTEMPT', {
      email,
      success,
      reason,
      timestamp: new Date().toISOString()
    });
  },

  logSuspiciousActivity: async (activity: string, details: any = {}) => {
    await securityLogger.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      activity,
      ...details
    });
  }
};

// CSP (Content Security Policy) helper
export const getCSPHeader = (): string => {
  return [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' https://veqmmmwrxeigeagqbqor.supabase.co",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://veqmmmwrxeigeagqbqor.supabase.co wss://veqmmmwrxeigeagqbqor.supabase.co",
    "font-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'"
  ].join('; ');
};

// Session security
export const sessionSecurity = {
  // Kontrola integrity session
  validateSession: async (): Promise<boolean> => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error || !session) return false;

      // Kontrola expiry
      if (session.expires_at && session.expires_at * 1000 < Date.now()) {
        await supabase.auth.signOut();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Chyba při validaci session:', error);
      return false;
    }
  },

  // Refresh token před expirací
  refreshTokenIfNeeded: async (): Promise<void> => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      // Refresh 5 minut před expirací
      const expiresAt = session.expires_at * 1000;
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;

      if (expiresAt - now < fiveMinutes) {
        await supabase.auth.refreshSession();
      }
    } catch (error) {
      console.error('Chyba při refresh tokenu:', error);
    }
  }
};

// Detekce podezřelé aktivity
export const suspiciousActivityDetector = {
  // Detekce rychlých požadavků
  detectRapidRequests: (() => {
    let requestTimes: number[] = [];
    const maxRequests = 10;
    const timeWindow = 1000; // 1 sekunda

    return () => {
      const now = Date.now();
      requestTimes = requestTimes.filter(time => now - time < timeWindow);
      requestTimes.push(now);

      if (requestTimes.length > maxRequests) {
        securityLogger.logSuspiciousActivity('RAPID_REQUESTS', {
          requestCount: requestTimes.length,
          timeWindow
        });
        return true;
      }
      return false;
    };
  })(),

  // Detekce neobvyklých vzorů
  detectUnusualPatterns: (action: string, context: any = {}) => {
    // Implementace detekce vzorů podle potřeby
    const suspiciousPatterns = [
      'multiple_failed_logins',
      'unusual_time_access',
      'multiple_permission_requests'
    ];

    // Zde by byla logika pro detekci vzorů
    // Pro demonstraci pouze logování
    securityLogger.logSecurityEvent('PATTERN_ANALYSIS', {
      action,
      context,
      timestamp: new Date().toISOString()
    });
  }
};
