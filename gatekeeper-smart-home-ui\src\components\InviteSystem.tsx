import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  UserPlus, 
  Mail, 
  Shield, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Eye,
  Edit,
  Trash2,
  Copy,
  Send,
  Users,
  Settings,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { inviteSchema, type InviteFormData } from '@/lib/validation';
import { supabase } from '@/lib/supabase';
import { auditLogger } from '@/lib/auditLogger';

interface Invite {
  id: string;
  email: string;
  permissions: string[];
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  created_at: string;
  expires_at: string;
  invited_by: string;
  accepted_at?: string;
  message?: string;
  token: string;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
}

const AVAILABLE_PERMISSIONS: Permission[] = [
  // Základní oprávnění
  { id: 'user', name: 'Základní uživatel', description: 'Přístup k základním funkcím', category: 'basic', risk_level: 'low' },
  { id: 'dashboard_view', name: 'Zobrazení dashboardu', description: 'Přístup k hlavnímu dashboardu', category: 'basic', risk_level: 'low' },
  
  // IoT oprávnění
  { id: 'gate_control', name: 'Ovládání brány', description: 'Možnost otevírat/zavírat bránu', category: 'iot', risk_level: 'medium' },
  { id: 'camera_view', name: 'Zobrazení kamer', description: 'Přístup k live streamu kamer', category: 'iot', risk_level: 'medium' },
  { id: 'device_status', name: 'Stav zařízení', description: 'Zobrazení stavu IoT zařízení', category: 'iot', risk_level: 'low' },
  { id: 'device_control', name: 'Ovládání zařízení', description: 'Ovládání všech IoT zařízení', category: 'iot', risk_level: 'high' },
  
  // Administrátorská oprávnění
  { id: 'admin', name: 'Administrátor', description: 'Plný přístup ke všem funkcím', category: 'admin', risk_level: 'critical' },
  { id: 'user_management', name: 'Správa uživatelů', description: 'Vytváření a správa uživatelských účtů', category: 'admin', risk_level: 'high' },
  { id: 'invite_users', name: 'Pozvání uživatelů', description: 'Možnost posílat pozvánky novým uživatelům', category: 'admin', risk_level: 'medium' },
  { id: 'security_logs', name: 'Bezpečnostní logy', description: 'Přístup k bezpečnostním logům', category: 'admin', risk_level: 'high' },
  { id: 'system_settings', name: 'Systémová nastavení', description: 'Změna systémových nastavení', category: 'admin', risk_level: 'critical' },
  
  // Reporting oprávnění
  { id: 'reports_view', name: 'Zobrazení reportů', description: 'Přístup k reportům a statistikám', category: 'reporting', risk_level: 'low' },
  { id: 'reports_export', name: 'Export reportů', description: 'Export dat a reportů', category: 'reporting', risk_level: 'medium' },
  
  // API oprávnění
  { id: 'api_access', name: 'API přístup', description: 'Přístup k REST API', category: 'api', risk_level: 'medium' },
  { id: 'api_admin', name: 'Admin API', description: 'Přístup k administrátorským API endpointům', category: 'api', risk_level: 'high' }
];

const InviteSystem: React.FC = () => {
  const [invites, setInvites] = useState<Invite[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedInvite, setSelectedInvite] = useState<Invite | null>(null);
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
    reset
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      permissions: [],
      expiresIn: 7,
      message: ''
    }
  });

  const watchedPermissions = watch('permissions');

  // Načtení pozvánek
  useEffect(() => {
    loadInvites();
  }, []);

  const loadInvites = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('invites')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setInvites(data || []);
    } catch (error) {
      console.error('Chyba při načítání pozvánek:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se načíst pozvánky",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Odeslání pozvánky
  const onSubmit = async (data: InviteFormData) => {
    try {
      // Generování tokenu
      const token = 'inv_' + Math.random().toString(36).substr(2, 16);
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + data.expiresIn);

      // Uložení do databáze
      const { error } = await supabase
        .from('invites')
        .insert([{
          email: data.email,
          permissions: data.permissions,
          expires_at: expiresAt.toISOString(),
          message: data.message,
          token,
          status: 'pending'
        }]);

      if (error) throw error;

      // Audit log
      await auditLogger.logAdminAction('INVITE_SENT', '', {
        invitedEmail: data.email,
        permissions: data.permissions,
        expiresIn: data.expiresIn
      });

      // Odeslání emailu (v produkci by mělo být přes Edge Function)
      await sendInviteEmail(data.email, token, data.permissions, data.message);

      toast({
        title: "Pozvánka odeslána",
        description: `Pozvánka byla odeslána na ${data.email}`,
      });

      reset();
      setShowInviteDialog(false);
      loadInvites();
    } catch (error) {
      console.error('Chyba při odesílání pozvánky:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se odeslat pozvánku",
        variant: "destructive",
      });
    }
  };

  // Odeslání emailu s pozvánkou
  const sendInviteEmail = async (email: string, token: string, permissions: string[], message?: string) => {
    try {
      // V produkci by mělo být přes Supabase Edge Function
      const inviteUrl = `${window.location.origin}/invite/${token}`;
      
      console.log('Email by byl odeslán na:', email);
      console.log('URL pozvánky:', inviteUrl);
      console.log('Oprávnění:', permissions);
      console.log('Zpráva:', message);

      // Simulace odeslání emailu
      // await supabase.functions.invoke('send-invite-email', {
      //   body: { email, token, permissions, message, inviteUrl }
      // });
    } catch (error) {
      console.error('Chyba při odesílání emailu:', error);
    }
  };

  // Zrušení pozvánky
  const revokeInvite = async (inviteId: string) => {
    try {
      const { error } = await supabase
        .from('invites')
        .update({ status: 'revoked' })
        .eq('id', inviteId);

      if (error) throw error;

      await auditLogger.logAdminAction('INVITE_REVOKED', '', { inviteId });

      toast({
        title: "Pozvánka zrušena",
        description: "Pozvánka byla úspěšně zrušena",
      });

      loadInvites();
    } catch (error) {
      console.error('Chyba při rušení pozvánky:', error);
      toast({
        title: "Chyba",
        description: "Nepodařilo se zrušit pozvánku",
        variant: "destructive",
      });
    }
  };

  // Kopírování odkazu pozvánky
  const copyInviteLink = (token: string) => {
    const inviteUrl = `${window.location.origin}/invite/${token}`;
    navigator.clipboard.writeText(inviteUrl);
    toast({
      title: "Odkaz zkopírován",
      description: "Odkaz na pozvánku byl zkopírován do schránky",
    });
  };

  // Získání barvy podle stavu
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500';
      case 'accepted': return 'bg-green-500';
      case 'expired': return 'bg-gray-500';
      case 'revoked': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // Získání barvy podle risk level
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Filtrování oprávnění podle kategorie
  const getPermissionsByCategory = (category: string) => {
    return AVAILABLE_PERMISSIONS.filter(p => p.category === category);
  };

  // Kontrola, zda je oprávnění vybráno
  const isPermissionSelected = (permissionId: string) => {
    return watchedPermissions.includes(permissionId);
  };

  // Přepnutí oprávnění
  const togglePermission = (permissionId: string) => {
    const current = watchedPermissions;
    if (current.includes(permissionId)) {
      setValue('permissions', current.filter(p => p !== permissionId));
    } else {
      setValue('permissions', [...current, permissionId]);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Systém pozvánek</h2>
          <p className="text-muted-foreground">Správa pozvánek a oprávnění uživatelů</p>
        </div>
        <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Nová pozvánka
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Odeslat pozvánku</DialogTitle>
              <DialogDescription>
                Vytvořte novou pozvánku pro uživatele s konkrétními oprávněními
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email">Email adresa</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-500">{errors.email.message}</p>
                )}
              </div>

              {/* Doba platnosti */}
              <div className="space-y-2">
                <Label htmlFor="expiresIn">Doba platnosti (dny)</Label>
                <Select onValueChange={(value) => setValue('expiresIn', parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte dobu platnosti" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 den</SelectItem>
                    <SelectItem value="3">3 dny</SelectItem>
                    <SelectItem value="7">7 dní</SelectItem>
                    <SelectItem value="14">14 dní</SelectItem>
                    <SelectItem value="30">30 dní</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Oprávnění */}
              <div className="space-y-4">
                <Label>Oprávnění</Label>
                
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="basic">Základní</TabsTrigger>
                    <TabsTrigger value="iot">IoT</TabsTrigger>
                    <TabsTrigger value="admin">Admin</TabsTrigger>
                    <TabsTrigger value="other">Ostatní</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="basic" className="space-y-2">
                    {getPermissionsByCategory('basic').map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={permission.id}
                          checked={isPermissionSelected(permission.id)}
                          onCheckedChange={() => togglePermission(permission.id)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={permission.id} className="font-medium">
                            {permission.name}
                          </Label>
                          <p className="text-sm text-muted-foreground">{permission.description}</p>
                        </div>
                        <Badge variant="outline" className={getRiskColor(permission.risk_level)}>
                          {permission.risk_level}
                        </Badge>
                      </div>
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="iot" className="space-y-2">
                    {getPermissionsByCategory('iot').map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={permission.id}
                          checked={isPermissionSelected(permission.id)}
                          onCheckedChange={() => togglePermission(permission.id)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={permission.id} className="font-medium">
                            {permission.name}
                          </Label>
                          <p className="text-sm text-muted-foreground">{permission.description}</p>
                        </div>
                        <Badge variant="outline" className={getRiskColor(permission.risk_level)}>
                          {permission.risk_level}
                        </Badge>
                      </div>
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="admin" className="space-y-2">
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Administrátorská oprávnění poskytují vysoký přístup k systému. Udělujte je pouze důvěryhodným uživatelům.
                      </AlertDescription>
                    </Alert>
                    {getPermissionsByCategory('admin').map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={permission.id}
                          checked={isPermissionSelected(permission.id)}
                          onCheckedChange={() => togglePermission(permission.id)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={permission.id} className="font-medium">
                            {permission.name}
                          </Label>
                          <p className="text-sm text-muted-foreground">{permission.description}</p>
                        </div>
                        <Badge variant="outline" className={getRiskColor(permission.risk_level)}>
                          {permission.risk_level}
                        </Badge>
                      </div>
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="other" className="space-y-2">
                    {[...getPermissionsByCategory('reporting'), ...getPermissionsByCategory('api')].map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={permission.id}
                          checked={isPermissionSelected(permission.id)}
                          onCheckedChange={() => togglePermission(permission.id)}
                        />
                        <div className="flex-1">
                          <Label htmlFor={permission.id} className="font-medium">
                            {permission.name}
                          </Label>
                          <p className="text-sm text-muted-foreground">{permission.description}</p>
                        </div>
                        <Badge variant="outline" className={getRiskColor(permission.risk_level)}>
                          {permission.risk_level}
                        </Badge>
                      </div>
                    ))}
                  </TabsContent>
                </Tabs>

                {errors.permissions && (
                  <p className="text-sm text-red-500">{errors.permissions.message}</p>
                )}
              </div>

              {/* Zpráva */}
              <div className="space-y-2">
                <Label htmlFor="message">Osobní zpráva (volitelné)</Label>
                <Textarea
                  id="message"
                  placeholder="Přidejte osobní zprávu k pozvánce..."
                  {...register('message')}
                  rows={3}
                />
                {errors.message && (
                  <p className="text-sm text-red-500">{errors.message.message}</p>
                )}
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowInviteDialog(false)}>
                  Zrušit
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Send className="mr-2 h-4 w-4 animate-pulse" />
                      Odesílání...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Odeslat pozvánku
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Seznam pozvánek */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Odeslané pozvánky
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Načítání pozvánek...</div>
          ) : invites.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Zatím nebyly odeslány žádné pozvánky</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Stav</TableHead>
                  <TableHead>Oprávnění</TableHead>
                  <TableHead>Vytvořeno</TableHead>
                  <TableHead>Vyprší</TableHead>
                  <TableHead>Akce</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invites.map((invite) => (
                  <TableRow key={invite.id}>
                    <TableCell className="font-medium">{invite.email}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(invite.status)}>
                        {invite.status === 'pending' && 'Čeká'}
                        {invite.status === 'accepted' && 'Přijato'}
                        {invite.status === 'expired' && 'Vypršelo'}
                        {invite.status === 'revoked' && 'Zrušeno'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {invite.permissions.slice(0, 2).map((perm) => (
                          <Badge key={perm} variant="outline" className="text-xs">
                            {AVAILABLE_PERMISSIONS.find(p => p.id === perm)?.name || perm}
                          </Badge>
                        ))}
                        {invite.permissions.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{invite.permissions.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {new Date(invite.created_at).toLocaleDateString('cs-CZ')}
                    </TableCell>
                    <TableCell>
                      {new Date(invite.expires_at).toLocaleDateString('cs-CZ')}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyInviteLink(invite.token)}
                          disabled={invite.status !== 'pending'}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedInvite(invite)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {invite.status === 'pending' && (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => revokeInvite(invite.id)}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Detail pozvánky */}
      {selectedInvite && (
        <Dialog open={!!selectedInvite} onOpenChange={() => setSelectedInvite(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Detail pozvánky</DialogTitle>
              <DialogDescription>
                Podrobné informace o pozvánce pro {selectedInvite.email}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label>Email</Label>
                <p className="font-medium">{selectedInvite.email}</p>
              </div>
              
              <div>
                <Label>Stav</Label>
                <Badge className={getStatusColor(selectedInvite.status)}>
                  {selectedInvite.status}
                </Badge>
              </div>
              
              <div>
                <Label>Oprávnění</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedInvite.permissions.map((perm) => {
                    const permission = AVAILABLE_PERMISSIONS.find(p => p.id === perm);
                    return (
                      <Badge key={perm} variant="outline">
                        {permission?.name || perm}
                      </Badge>
                    );
                  })}
                </div>
              </div>
              
              {selectedInvite.message && (
                <div>
                  <Label>Zpráva</Label>
                  <p className="text-sm text-muted-foreground mt-1">{selectedInvite.message}</p>
                </div>
              )}
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Vytvořeno</Label>
                  <p className="text-sm">{new Date(selectedInvite.created_at).toLocaleString('cs-CZ')}</p>
                </div>
                <div>
                  <Label>Vyprší</Label>
                  <p className="text-sm">{new Date(selectedInvite.expires_at).toLocaleString('cs-CZ')}</p>
                </div>
              </div>
              
              {selectedInvite.accepted_at && (
                <div>
                  <Label>Přijato</Label>
                  <p className="text-sm">{new Date(selectedInvite.accepted_at).toLocaleString('cs-CZ')}</p>
                </div>
              )}
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedInvite(null)}>
                Zavřít
              </Button>
              {selectedInvite.status === 'pending' && (
                <Button onClick={() => copyInviteLink(selectedInvite.token)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Kopírovat odkaz
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default InviteSystem;
