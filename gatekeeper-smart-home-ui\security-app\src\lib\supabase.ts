import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Typy pro naše tabulky
export interface Profile {
  id: string
  username: string
  email: string
  permissions: string[]
  created_at: string
  last_login?: string
  is_active: boolean
}

export interface SecurityLog {
  id: string
  timestamp: string
  event_type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  ip_address?: string
  user_agent?: string
  user_id?: string
  details: Record<string, any>
  created_at: string
}

export interface UserSecurity {
  id: string
  user_id: string
  two_factor_enabled: boolean
  two_factor_secret?: string
  last_login?: string
  failed_login_attempts: number
  account_locked_until?: string
  created_at: string
}

export interface Invite {
  id: string
  email: string
  permissions: string[]
  status: 'pending' | 'accepted' | 'expired' | 'revoked'
  token: string
  message?: string
  invited_by?: string
  invited_at: string
  expires_at: string
  created_at: string
}

// Pomocné funkce
export const isAdmin = async (): Promise<boolean> => {
  const { data, error } = await supabase.rpc('is_admin')
  if (error) {
    console.error('Error checking admin status:', error)
    return false
  }
  return data
}

export const getCurrentProfile = async (): Promise<Profile | null> => {
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) return null

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return data
}

export async function createProfile(userId: string, username: string, email: string): Promise<{ error: any }> {
  try {
    const { error } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        username,
        email,
        permissions: email === '<EMAIL>'
          ? ['admin', 'user_management', 'security_logs', 'system_config']
          : ['basic_access'],
        is_active: true
      })

    return { error }
  } catch (error) {
    console.error('Error creating profile:', error)
    return { error }
  }
}
