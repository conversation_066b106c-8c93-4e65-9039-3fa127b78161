var r=require("@hookform/resolvers");function e(r,t,o){void 0===o&&(o=[]);var n=function(){var n=[].concat(o,[a]),i=r[a];Array.isArray(i)?i.forEach(function(r,o){e(r,t,[].concat(n,[o]))}):"object"==typeof i&&null!==i?e(i,t,n):"string"==typeof i&&(t[n.join(".")]={type:"validation",message:i})};for(var a in r)n()}var t=function(r,t){var o={};return e(r,o),o};exports.fluentAsyncValidationResolver=function(e){return function(o,n,a){try{return Promise.resolve(e.validateAsync(o)).then(function(e){var n=0===Object.keys(e).length;return a.shouldUseNativeValidation&&r.validateFieldsNatively({},a),n?{values:o,errors:{}}:{values:{},errors:r.toNestErrors(t(e),a)}})}catch(r){return Promise.reject(r)}}},exports.fluentValidationResolver=function(e){return function(o,n,a){try{var i=e.validate(o),s=0===Object.keys(i).length;return a.shouldUseNativeValidation&&r.validateFieldsNatively({},a),Promise.resolve(s?{values:o,errors:{}}:{values:{},errors:r.toNestErrors(t(i),a)})}catch(r){return Promise.reject(r)}}};
//# sourceMappingURL=fluentvalidation-ts.js.map
