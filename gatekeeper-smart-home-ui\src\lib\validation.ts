import { z } from 'zod';

// Bezpečnostní validační schémata pomocí Zod

// Email validace s pokročilými kontrolami
export const emailSchema = z
  .string()
  .min(1, 'Email je povinný')
  .max(254, 'Email je př<PERSON>')
  .email('Neplatný formát emailu')
  .refine((email) => {
    // Kontrola na nebezpečné znaky
    const dangerousChars = /[<>'"&]/;
    return !dangerousChars.test(email);
  }, 'Email obsahuje nepovolené znaky')
  .refine((email) => {
    // Kontrola na SQL injection pokusy
    const sqlPatterns = /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER)\b)/i;
    return !sqlPatterns.test(email);
  }, 'Neplatný formát emailu');

// <PERSON><PERSON><PERSON> validace s pokročilými bezpečnostními požadavky
export const passwordSchema = z
  .string()
  .min(8, 'Heslo musí mít alespoň 8 znaků')
  .max(128, '<PERSON><PERSON><PERSON> je pří<PERSON> dlouhé')
  .refine((password) => {
    // Alespoň jedno malé písmeno
    return /[a-z]/.test(password);
  }, 'Heslo musí obsahovat alespoň jedno malé písmeno')
  .refine((password) => {
    // Alespoň jedno velké písmeno
    return /[A-Z]/.test(password);
  }, 'Heslo musí obsahovat alespoň jedno velké písmeno')
  .refine((password) => {
    // Alespoň jedno číslo
    return /[0-9]/.test(password);
  }, 'Heslo musí obsahovat alespoň jedno číslo')
  .refine((password) => {
    // Alespoň jeden speciální znak
    return /[^a-zA-Z0-9]/.test(password);
  }, 'Heslo musí obsahovat alespoň jeden speciální znak')
  .refine((password) => {
    // Kontrola na běžná hesla
    const commonPasswords = [
      'password', '123456', 'qwerty', 'admin', 'letmein', 'welcome',
      'password123', '123456789', 'qwerty123', 'admin123', 'root',
      'toor', 'pass', '1234', '12345', 'abc123', 'password1'
    ];
    return !commonPasswords.some(common => 
      password.toLowerCase().includes(common.toLowerCase())
    );
  }, 'Heslo nesmí obsahovat běžná slova nebo vzory')
  .refine((password) => {
    // Kontrola na opakující se znaky
    const repeatingPattern = /(.)\1{3,}/;
    return !repeatingPattern.test(password);
  }, 'Heslo nesmí obsahovat více než 3 stejné znaky za sebou')
  .refine((password) => {
    // Kontrola na sekvenční znaky
    const sequences = ['1234', 'abcd', 'qwer', 'asdf', 'zxcv'];
    return !sequences.some(seq => password.toLowerCase().includes(seq));
  }, 'Heslo nesmí obsahovat sekvenční znaky');

// Uživatelské jméno validace
export const usernameSchema = z
  .string()
  .min(3, 'Uživatelské jméno musí mít alespoň 3 znaky')
  .max(50, 'Uživatelské jméno je příliš dlouhé')
  .regex(/^[a-zA-Z0-9_.-]+$/, 'Uživatelské jméno může obsahovat pouze písmena, čísla, tečky, pomlčky a podtržítka')
  .refine((username) => {
    // Kontrola na rezervovaná slova
    const reservedWords = [
      'admin', 'root', 'administrator', 'user', 'guest', 'test',
      'api', 'www', 'mail', 'ftp', 'localhost', 'system', 'null',
      'undefined', 'true', 'false', 'select', 'insert', 'update', 'delete'
    ];
    return !reservedWords.includes(username.toLowerCase());
  }, 'Toto uživatelské jméno je rezervované')
  .refine((username) => {
    // Nesmí začínat nebo končit tečkou/pomlčkou
    return !/^[.-]|[.-]$/.test(username);
  }, 'Uživatelské jméno nesmí začínat nebo končit tečkou či pomlčkou');

// MQTT topic validace
export const mqttTopicSchema = z
  .string()
  .min(1, 'MQTT topic je povinný')
  .max(255, 'MQTT topic je příliš dlouhý')
  .regex(/^[a-zA-Z0-9/_-]+$/, 'MQTT topic může obsahovat pouze písmena, čísla, lomítka, pomlčky a podtržítka')
  .refine((topic) => {
    // Kontrola na nebezpečné vzory
    const dangerousPatterns = [
      /\.\./,  // Directory traversal
      /\/\//,  // Double slashes
      /^\//,   // Starting with slash
      /\/$/    // Ending with slash
    ];
    return !dangerousPatterns.some(pattern => pattern.test(topic));
  }, 'MQTT topic obsahuje nepovolené vzory');

// IP adresa validace
export const ipAddressSchema = z
  .string()
  .refine((ip) => {
    // IPv4 validace
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6 validace (zjednodušená)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }, 'Neplatná IP adresa');

// Port validace
export const portSchema = z
  .number()
  .int('Port musí být celé číslo')
  .min(1, 'Port musí být větší než 0')
  .max(65535, 'Port musí být menší než 65536')
  .refine((port) => {
    // Kontrola na rezervované porty (pokud není admin)
    const reservedPorts = [22, 23, 25, 53, 80, 110, 143, 443, 993, 995];
    // Tuto kontrolu by měl provádět pouze admin
    return true; // Pro teď povolujeme všechny porty
  }, 'Tento port je rezervovaný');

// Obecná textová validace s sanitizací
export const sanitizedTextSchema = (maxLength: number = 255) => z
  .string()
  .max(maxLength, `Text je příliš dlouhý (maximum ${maxLength} znaků)`)
  .refine((text) => {
    // Kontrola na XSS pokusy
    const xssPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i,
      /<link/i,
      /<meta/i
    ];
    return !xssPatterns.some(pattern => pattern.test(text));
  }, 'Text obsahuje potenciálně nebezpečný obsah')
  .refine((text) => {
    // Kontrola na SQL injection
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER|CREATE|EXEC)\b)/i,
      /('|(\\')|(;)|(\\)|(\/\*)|(--)|(\*\/)/,
      /(\b(OR|AND)\b.*=)/i
    ];
    return !sqlPatterns.some(pattern => pattern.test(text));
  }, 'Text obsahuje potenciálně nebezpečný obsah')
  .transform((text) => {
    // Sanitizace - odstranění nebezpečných znaků
    return text
      .trim()
      .replace(/[<>]/g, '') // Odstranění < a >
      .replace(/&/g, '&amp;') // Escapování &
      .replace(/"/g, '&quot;') // Escapování "
      .replace(/'/g, '&#x27;'); // Escapování '
  });

// URL validace
export const urlSchema = z
  .string()
  .url('Neplatná URL adresa')
  .refine((url) => {
    // Povolené protokoly
    const allowedProtocols = ['http:', 'https:', 'ws:', 'wss:'];
    try {
      const urlObj = new URL(url);
      return allowedProtocols.includes(urlObj.protocol);
    } catch {
      return false;
    }
  }, 'Nepovolený protokol URL')
  .refine((url) => {
    // Kontrola na nebezpečné domény
    const dangerousDomains = [
      'localhost',
      '127.0.0.1',
      '0.0.0.0',
      '::1'
    ];
    try {
      const urlObj = new URL(url);
      // Pro produkci by měly být localhost adresy zakázané
      // Pro development je povolujeme
      return true;
    } catch {
      return false;
    }
  }, 'Nepovolená doména');

// Schéma pro přihlášení
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Heslo je povinné'), // Pro přihlášení nevalidujeme složitost
  rememberMe: z.boolean().optional()
});

// Schéma pro registraci
export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  username: usernameSchema,
  inviteToken: z.string().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Hesla se neshodují",
  path: ["confirmPassword"]
});

// Schéma pro změnu hesla
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Současné heslo je povinné'),
  newPassword: passwordSchema,
  confirmNewPassword: z.string()
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "Nová hesla se neshodují",
  path: ["confirmNewPassword"]
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "Nové heslo musí být odlišné od současného",
  path: ["newPassword"]
});

// Schéma pro MQTT nastavení
export const mqttSettingsSchema = z.object({
  host: z.union([ipAddressSchema, z.string().min(1, 'Host je povinný')]),
  port: portSchema,
  username: usernameSchema.optional(),
  password: z.string().optional(),
  topic: mqttTopicSchema,
  clientId: sanitizedTextSchema(50).optional()
});

// Schéma pro pozvánku
export const inviteSchema = z.object({
  email: emailSchema,
  permissions: z.array(z.string()).min(1, 'Alespoň jedno oprávnění je povinné'),
  expiresIn: z.number().min(1).max(30), // dny
  message: sanitizedTextSchema(500).optional()
});

// Funkce pro bezpečnou sanitizaci
export const sanitizeInput = (input: string, maxLength: number = 255): string => {
  if (!input) return '';
  
  return input
    .trim()
    .slice(0, maxLength)
    .replace(/[<>]/g, '')
    .replace(/&/g, '&amp;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');
};

// Funkce pro validaci síly hesla
export const getPasswordStrength = (password: string): {
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) score++;
  else feedback.push('Alespoň 8 znaků');

  if (/[a-z]/.test(password)) score++;
  else feedback.push('Malé písmeno');

  if (/[A-Z]/.test(password)) score++;
  else feedback.push('Velké písmeno');

  if (/[0-9]/.test(password)) score++;
  else feedback.push('Číslo');

  if (/[^a-zA-Z0-9]/.test(password)) score++;
  else feedback.push('Speciální znak');

  if (password.length >= 12) score++;

  return { score, feedback };
};

// Export typů pro TypeScript
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type MqttSettingsFormData = z.infer<typeof mqttSettingsSchema>;
export type InviteFormData = z.infer<typeof inviteSchema>;
