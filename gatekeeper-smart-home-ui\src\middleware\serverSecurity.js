// Bezpečnostní middleware pro Express server
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');

// Rate limiting konfigurace
const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      console.log(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      res.status(429).json({ 
        error: message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// Různé rate limitery pro různé endpointy
const rateLimiters = {
  // Obecný rate limiter
  general: createRateLimiter(
    15 * 60 * 1000, // 15 minut
    100, // 100 požadavků
    'Příliš mnoho požadavků. Zkuste to znovu za 15 minut.'
  ),

  // Přísný rate limiter pro přihlášení
  auth: createRateLimiter(
    15 * 60 * 1000, // 15 minut
    5, // 5 pokusů
    'P<PERSON><PERSON><PERSON>š mnoho pokusů o přihlášení. Zkuste to znovu za 15 minut.'
  ),

  // Rate limiter pro API
  api: createRateLimiter(
    1 * 60 * 1000, // 1 minuta
    60, // 60 požadavků
    'Příliš mnoho API požadavků. Zkuste to znovu za minutu.'
  ),

  // Velmi přísný rate limiter pro admin operace
  admin: createRateLimiter(
    5 * 60 * 1000, // 5 minut
    10, // 10 požadavků
    'Příliš mnoho admin operací. Zkuste to znovu za 5 minut.'
  )
};

// CORS konfigurace pro produkci
const corsOptions = {
  origin: function (origin, callback) {
    // Povolené domény pro produkci
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:8080',
      'http://localhost:8081',
      'https://your-production-domain.com', // Nahraďte skutečnou doménou
      // Přidejte další povolené domény
    ];

    // Povolit požadavky bez origin (např. mobilní aplikace)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log(`CORS blocked origin: ${origin}`);
      callback(new Error('Nepovolen CORS origin'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-API-Key',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hodin
};

// Helmet konfigurace pro bezpečnostní hlavičky
const helmetOptions = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // Pouze pro development
        "https://veqmmmwrxeigeagqbqor.supabase.co"
      ],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: [
        "'self'",
        "https://veqmmmwrxeigeagqbqor.supabase.co",
        "wss://veqmmmwrxeigeagqbqor.supabase.co"
      ],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: []
    }
  },
  crossOriginEmbedderPolicy: false, // Může způsobit problémy s některými knihovnami
  hsts: {
    maxAge: 31536000, // 1 rok
    includeSubDomains: true,
    preload: true
  }
};

// Middleware pro logování bezpečnostních událostí
const securityLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Logování podezřelých vzorů
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS pokusy
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /%3Cscript/i,  // URL encoded XSS
  ];

  const userAgent = req.headers['user-agent'] || '';
  const url = req.url;
  const ip = req.ip || req.connection.remoteAddress;

  // Kontrola podezřelých vzorů
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(url) || pattern.test(userAgent)
  );

  if (isSuspicious) {
    console.log(`🚨 SUSPICIOUS REQUEST: IP=${ip}, URL=${url}, UA=${userAgent}`);
    // Zde by se mělo logovat do databáze nebo bezpečnostního systému
  }

  // Logování pomalých požadavků
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    if (duration > 5000) { // Pomalejší než 5 sekund
      console.log(`⚠️ SLOW REQUEST: ${duration}ms, IP=${ip}, URL=${url}`);
    }
  });

  next();
};

// Middleware pro validaci API klíčů
const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'] || 
                 req.headers['authorization']?.replace('Bearer ', '') || 
                 req.query.apiKey;

  const validApiKey = process.env.API_KEY || 'brana-mqtt-secure-2025-v2';

  if (!apiKey || apiKey !== validApiKey) {
    console.log(`❌ INVALID API KEY: IP=${req.ip}, Key=${apiKey}`);
    return res.status(401).json({ 
      error: 'Neplatný nebo chybějící API klíč',
      code: 'INVALID_API_KEY'
    });
  }

  next();
};

// Middleware pro blokování známých škodlivých IP
const ipBlacklist = new Set([
  // Přidejte známé škodlivé IP adresy
  // '*************',
]);

const blockMaliciousIPs = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  
  if (ipBlacklist.has(ip)) {
    console.log(`🚫 BLOCKED IP: ${ip}`);
    return res.status(403).json({ 
      error: 'Přístup odepřen',
      code: 'IP_BLOCKED'
    });
  }

  next();
};

// Middleware pro sanitizaci vstupů
const sanitizeInputs = (req, res, next) => {
  const sanitize = (obj) => {
    if (typeof obj === 'string') {
      return obj.trim().replace(/[<>]/g, '');
    }
    if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        obj[key] = sanitize(obj[key]);
      }
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitize(req.body);
  }
  if (req.query) {
    req.query = sanitize(req.query);
  }

  next();
};

// Export všech middleware funkcí
module.exports = {
  rateLimiters,
  corsOptions,
  helmetOptions,
  securityLogger,
  validateApiKey,
  blockMaliciousIPs,
  sanitizeInputs,
  
  // Funkce pro aplikaci všech bezpečnostních middleware
  applySecurityMiddleware: (app) => {
    // Základní bezpečnostní hlavičky
    app.use(helmet(helmetOptions));
    
    // CORS
    app.use(cors(corsOptions));
    
    // Blokování škodlivých IP
    app.use(blockMaliciousIPs);
    
    // Obecný rate limiting
    app.use(rateLimiters.general);
    
    // Bezpečnostní logování
    app.use(securityLogger);
    
    // Sanitizace vstupů
    app.use(sanitizeInputs);
    
    // Parsování JSON s limitem velikosti
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }
};
