-- <PERSON>MI<PERSON> FUNKCE A POLICIES - SPUSTIT AŽ PO ÚSPĚŠNÉM NASTAVENÍ ZÁKLADNÍ DATABÁZE
-- Zkopírujte a vložte do Supabase SQL Editor AŽ PO SPUŠTĚNÍ minimal-database-setup.sql

-- 1. <PERSON><PERSON>Z<PERSON><PERSON><PERSON>NOST<PERSON><PERSON> FUNKCE PRO ADMIN KONTROLU
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM profiles
    WHERE id = auth.uid()
    AND (
      'admin' = ANY(permissions)
      OR email = '<EMAIL>'
    )
  );
$$;

-- 2. ADMIN POLICIES PRO PROFILES
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all profiles" ON profiles
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admins can insert profiles" ON profiles
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admins can delete profiles" ON profiles
  FOR DELETE USING (is_admin());

-- 3. ADMI<PERSON> POLICIES PRO SECURITY LOGS
CREATE POLICY "Admins can view all security logs" ON security_logs
  FOR SELECT USING (is_admin());

-- 4. ADMIN POLICIES PRO USER SECURITY
CREATE POLICY "Admins can view all user security" ON user_security
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all user security" ON user_security
  FOR UPDATE USING (is_admin());

-- 5. ADMIN POLICIES PRO INVITES
CREATE POLICY "Admins can manage all invites" ON invites
  FOR ALL USING (is_admin());

-- 6. ADMIN POLICIES PRO USER SESSIONS
CREATE POLICY "Admins can view all sessions" ON user_sessions
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all sessions" ON user_sessions
  FOR UPDATE USING (is_admin());

-- 7. TEST ADMIN FUNKCÍ
SELECT 
  'Admin funkce nastaveny úspěšně!' as status,
  is_admin() as current_user_is_admin;
