
import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Camera, RefreshCw, Lock, AlertCircle, Maximize2, Minimize2, Wifi, Settings } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface CameraViewProps {
  hasPermission?: boolean;
}

const CameraView = ({ hasPermission = false }: CameraViewProps) => {
  const [imageKey, setImageKey] = useState(0);
  const [loading, setLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState<'fast' | 'slow' | 'unknown'>('unknown');
  const [refreshInterval, setRefreshInterval] = useState(2000);
  
  const isMobile = useIsMobile();

  // Detekce kvality připojení pomocí Network Information API
  useEffect(() => {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const updateConnectionInfo = () => {
        if (connection.effectiveType === '4g' || connection.effectiveType === '3g') {
          setConnectionQuality('fast');
          setRefreshInterval(isMobile ? 3000 : 2000); // Na mobilech trochu pomalejší
        } else if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
          setConnectionQuality('slow');
          setRefreshInterval(isMobile ? 8000 : 6000); // Výrazně pomalejší pro slabé spojení
        } else {
          setConnectionQuality('unknown');
          setRefreshInterval(isMobile ? 4000 : 3000); // Konzervativní nastavení
        }
      };
      
      updateConnectionInfo();
      connection.addEventListener('change', updateConnectionInfo);
      
      return () => connection.removeEventListener('change', updateConnectionInfo);
    } else {
      // Fallback pro browsery bez Network Information API
      setRefreshInterval(isMobile ? 4000 : 2000);
    }
  }, [isMobile]);

  useEffect(() => {
    if (!hasPermission) return;

    const interval = setInterval(() => {
      setImageKey(prev => prev + 1);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [hasPermission, refreshInterval]);

  const refreshImage = useCallback(() => {
    setLoading(true);
    setImageError(false);
    setImageKey(prev => prev + 1);
    setTimeout(() => setLoading(false), 500);
  }, []);  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  const openCameraSettings = useCallback(() => {
    window.open('http://89.24.76.191:10180', '_blank', 'noopener,noreferrer');
  }, []);

  // Ukončení fullscreen pomocí ESC nebo kliknutí na obrázek
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isFullscreen]);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setLoading(false);
    console.log('Camera image failed to load');
  }, []);

  const handleImageLoad = useCallback(() => {
    setImageError(false);
    setLoading(false);
    console.log('Camera image loaded successfully');
  }, []);
  // Určí velikost obrázku podle zařízení a režimu
  const getImageUrl = () => {
    const baseUrl = 'http://89.24.76.191:10180/photo.jpg';
    const params = new URLSearchParams({
      t: imageKey.toString(),
      cache: Date.now().toString(),
    });

    return `${baseUrl}?${params.toString()}`;
  };

  const getConnectionIcon = () => {
    switch (connectionQuality) {
      case 'fast': return <Wifi className="w-3 h-3 text-green-400" />;
      case 'slow': return <Wifi className="w-3 h-3 text-orange-400" />;
      default: return <Wifi className="w-3 h-3 text-gray-400" />;
    }
  };  const getImageHeight = () => {
    if (isFullscreen) {
      return isMobile ? 'h-screen' : 'h-96';
    }
    // Použijeme aspect ratio místo pevné výšky
    return 'aspect-video';
  };
  if (!hasPermission) {
    return (
      <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center space-x-2">
            <Camera className="w-5 h-5" />
            <span>Kamera</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-8 text-center">
            <Lock className="w-12 h-12 text-red-300 mx-auto mb-4" />
            <p className="text-red-300">
              Nemáte oprávnění k zobrazení kamery
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }  return (
    <div className={isFullscreen ? 'fixed inset-0 z-50 bg-black' : ''}>
      <Card className={`backdrop-blur-lg bg-white/10 border-purple-500/20 ${
        isFullscreen ? 'h-full border-0 rounded-none bg-black' : ''
      }`}>
        <CardHeader className={`flex flex-row items-center justify-between ${
          isMobile ? 'px-2 py-2' : ''
        } ${isFullscreen ? 'absolute top-0 left-0 right-0 z-10 bg-black/70 backdrop-blur-sm' : ''}`}>
          <CardTitle className="text-white flex items-center space-x-2">
            <Camera className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} />
            <span className={isMobile ? 'text-sm' : ''}>Live kamera</span>
            {getConnectionIcon()}
          </CardTitle>          <div className="flex items-center space-x-2">
            {/* Refresh tlačítko - větší na mobilech */}
            <button
              onClick={refreshImage}
              disabled={loading}
              className={`text-purple-300 hover:text-white transition-colors camera-button ${
                isMobile ? 'p-2 touch-manipulation' : ''
              }`}
              title="Obnovit obrázek"
            >
              <RefreshCw className={`${
                isMobile ? 'w-5 h-5' : 'w-4 h-4'
              } ${loading ? 'animate-spin' : ''}`} />
            </button>
              {/* Settings tlačítko - odkaz na kameru */}
            <button
              onClick={openCameraSettings}
              className={`text-purple-300 hover:text-yellow-400 transition-colors camera-button camera-settings-button ${
                isMobile ? 'p-2 touch-manipulation' : ''
              }`}
              title="Otevřít nastavení kamery"
            >
              <Settings className={isMobile ? 'w-5 h-5' : 'w-4 h-4'} />
            </button>
            
            {/* Fullscreen tlačítko */}
            <button
              onClick={toggleFullscreen}
              className={`text-purple-300 hover:text-white transition-colors camera-button ${
                isMobile ? 'p-2 touch-manipulation' : ''
              }`}
              title={isFullscreen ? 'Zmenšit' : 'Celá obrazovka'}
            >
              {isFullscreen ? (
                <Minimize2 className={isMobile ? 'w-5 h-5' : 'w-4 h-4'} />
              ) : (
                <Maximize2 className={isMobile ? 'w-5 h-5' : 'w-4 h-4'} />
              )}
            </button>
          </div>
        </CardHeader>        <CardContent className={`${isMobile ? 'px-2 pb-2' : ''} ${
          isFullscreen ? 'p-0 h-full flex flex-col' : ''
        }`}>
          <div className={`relative ${isFullscreen ? 'flex-1' : 'rounded-lg'} overflow-hidden bg-slate-800/50 ${
            isFullscreen ? 'rounded-none bg-black' : ''
          }`}>
            {imageError ? (
              <div className={`w-full ${getImageHeight()} flex items-center justify-center bg-slate-700`}>
                <div className="text-center">
                  <AlertCircle className={`${
                    isMobile ? 'w-8 h-8' : 'w-12 h-12'
                  } text-red-400 mx-auto mb-4`} />
                  <p className={`text-red-300 mb-2 ${isMobile ? 'text-sm' : ''}`}>
                    Kamera nedostupná
                  </p>
                  <p className={`text-purple-300 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                    Zkontrolujte připojení kamery nebo kontaktujte administrátora
                  </p>
                </div>
              </div>
            ) : (
              <>                <img
                  src={getImageUrl()}
                  alt="Live camera feed"
                  className={`w-full ${getImageHeight()} object-contain bg-slate-900 ${
                    isFullscreen ? 'cursor-pointer' : ''
                  }`}
                  onError={handleImageError}
                  onLoad={handleImageLoad}
                  onClick={isFullscreen ? toggleFullscreen : undefined}
                  style={{ display: loading ? 'none' : 'block' }}
                  loading="lazy"
                />
                {loading && (
                  <div className={`w-full ${getImageHeight()} flex items-center justify-center bg-slate-700`}>
                    <RefreshCw className={`${
                      isMobile ? 'w-6 h-6' : 'w-8 h-8'
                    } text-purple-400 animate-spin loading-spinner`} />
                  </div>
                )}
              </>
            )}
            
            {/* Status indikátory */}
            <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {imageError ? 'Offline' : 'Live'}
            </div>
            <div className="absolute top-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center space-x-1">
              <span>#{imageKey}</span>
              {connectionQuality !== 'unknown' && (
                <span className={`w-2 h-2 rounded-full connection-indicator ${
                  connectionQuality === 'fast' ? 'bg-green-400' : 'bg-orange-400'
                }`}></span>
              )}
            </div>
            
            {/* Refresh interval info na mobilech */}
            {isMobile && (
              <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                {Math.round(refreshInterval / 1000)}s
              </div>
            )}
          </div>
          
          {imageError && (
            <div className="mt-3 text-center">
              <button
                onClick={refreshImage}
                className={`text-purple-300 hover:text-white transition-colors camera-button ${
                  isMobile ? 'text-base p-2 touch-manipulation' : 'text-sm'
                }`}
              >
                Zkusit znovu načíst
              </button>
            </div>
          )}
            {/* Info panel pro mobily */}
          {isMobile && !isFullscreen && (
            <div className="mt-2 text-xs text-purple-300 text-center">
              Klepněte na {' '}
              <Maximize2 className="w-3 h-3 inline" />
              {' '} pro celou obrazovku
            </div>
          )}
          
          {/* Fullscreen info */}
          {isFullscreen && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 bg-black/70 text-white text-sm px-4 py-2 rounded-lg backdrop-blur-sm">
              <p className="text-center">
                Klepněte na obrázek nebo stiskněte ESC pro ukončení
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CameraView;
