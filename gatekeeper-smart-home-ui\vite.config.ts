import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8081, // Změněno z 8080 na 8081 podle aktuálního nastavení
    headers: {
      // Bezpečnostní hlavičky pro development server
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      // CSP pro development - méně přísné
      'Content-Security-Policy': mode === 'development'
        ? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://veqmmmwrxeigeagqbqor.supabase.co wss://veqmmmwrxeigeagqbqor.supabase.co; img-src 'self' data: https:;"
        : "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://veqmmmwrxeigeagqbqor.supabase.co wss://veqmmmwrxeigeagqbqor.supabase.co; img-src 'self' data: https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none';"
    },
    cors: {
      origin: mode === 'development'
        ? ['http://localhost:8081', 'http://127.0.0.1:8081']
        : false, // Pro produkci zakázat CORS
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Bezpečnostní nastavení pro build
    rollupOptions: {
      output: {
        // Obfuskace názvů souborů
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    // Minimalizace a optimalizace
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production', // Odstranit console.log v produkci
        drop_debugger: true
      }
    },
    // Source maps pouze pro development
    sourcemap: mode === 'development'
  },
  define: {
    // Bezpečné environment variables
    __DEV__: mode === 'development',
    __PROD__: mode === 'production'
  }
}));
