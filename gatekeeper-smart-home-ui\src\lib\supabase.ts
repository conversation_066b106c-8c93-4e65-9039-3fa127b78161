import { createClient } from '@supabase/supabase-js'

console.log('SUPABASE_URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('SUPABASE_ANON_KEY:', import.meta.env.VITE_SUPABASE_ANON_KEY);

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Typy pro databázi
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string
          email: string
          permissions: string[]
          created_at: string
          last_login: string | null
          is_active: boolean
        }
        Insert: {
          id: string
          username: string
          email: string
          permissions?: string[]
          created_at?: string
          last_login?: string | null
          is_active?: boolean
        }
        Update: {
          id?: string
          username?: string
          email?: string
          permissions?: string[]
          created_at?: string
          last_login?: string | null
          is_active?: boolean
        }
      }
      invites: {
        Row: {
          id: string
          token: string
          permissions: string[]
          expires_at: string
          created_by: string
          used_by: string | null
          created_at: string
          used_at: string | null
        }
        Insert: {
          id?: string
          token: string
          permissions: string[]
          expires_at: string
          created_by: string
          used_by?: string | null
          created_at?: string
          used_at?: string | null
        }
        Update: {
          id?: string
          token?: string
          permissions?: string[]
          expires_at?: string
          created_by?: string
          used_by?: string | null
          created_at?: string
          used_at?: string | null
        }
      }
      login_logs: {
        Row: {
          id: string
          user_id: string
          ip_address: string | null
          user_agent: string | null
          success: boolean
          timestamp: string
        }
        Insert: {
          id?: string
          user_id: string
          ip_address?: string | null
          user_agent?: string | null
          success: boolean
          timestamp?: string
        }
        Update: {
          id?: string
          user_id?: string
          ip_address?: string | null
          user_agent?: string | null
          success?: boolean
          timestamp?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
