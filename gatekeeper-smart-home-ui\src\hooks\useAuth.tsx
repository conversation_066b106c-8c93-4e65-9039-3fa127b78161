
import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: number;
  username: string;
  email?: string;
  permissions: string[];
  lastLogin?: string;
}

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API base URL
const API_BASE = 'http://localhost:3000/api';

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Zkontrolovat uložené přihlášení
    const savedToken = localStorage.getItem('iot-token');
    if (savedToken) {
      verifyToken(savedToken);
    } else {
      setLoading(false);
    }
  }, []);

  const verifyToken = async (token: string) => {
    try {
      const response = await fetch(`${API_BASE}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        // Token je neplatný, odstraníme ho
        localStorage.removeItem('iot-token');
        localStorage.removeItem('iot-user');
      }
    } catch (error) {
      console.error('Chyba při ověřování tokenu:', error);
      localStorage.removeItem('iot-token');
      localStorage.removeItem('iot-user');
    } finally {
      setLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      const response = await fetch(`${API_BASE}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Chyba při přihlašování');
      }

      // Uložit token a uživatelská data
      localStorage.setItem('iot-token', data.token);
      localStorage.setItem('iot-user', JSON.stringify(data.user));
      setUser(data.user);

    } catch (error) {
      console.error('Chyba při přihlašování:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('iot-token');
      if (token) {
        // Odhlásit na serveru
        await fetch(`${API_BASE}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
      }
    } catch (error) {
      console.error('Chyba při odhlašování na serveru:', error);
    } finally {
      // Vždy vyčistit lokální stav
      setUser(null);
      localStorage.removeItem('iot-token');
      localStorage.removeItem('iot-user');
    }
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
