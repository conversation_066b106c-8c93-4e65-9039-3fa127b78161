'use client'

import { useAuth } from '@/contexts/AuthContext'
import { LogOut, Shield, Users, Camera, Settings, AlertTriangle } from 'lucide-react'

export default function Dashboard() {
  const { user, profile, isAdmin, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-blue-600" />
              <h1 className="ml-2 text-xl font-semibold text-gray-900">
                Smart Home Security
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                <span className="font-medium">{profile?.username || user?.email}</span>
                {isAdmin && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Admin
                  </span>
                )}
              </div>
              
              <button
                onClick={handleSignOut}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Odhlásit se
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-2">
                Vítejte v bezpečnostním systému
              </h2>
              <p className="text-sm text-gray-600">
                Přihlášeni jako: <strong>{profile?.username || user?.email}</strong>
              </p>
              {profile?.permissions && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 mb-2">Vaše oprávnění:</p>
                  <div className="flex flex-wrap gap-2">
                    {profile.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions Grid */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {/* Camera Control */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Camera className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Kamery
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        4 aktivní
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <button className="font-medium text-blue-600 hover:text-blue-500">
                    Zobrazit záznamy
                  </button>
                </div>
              </div>
            </div>

            {/* User Management (Admin only) */}
            {isAdmin && (
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Users className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Uživatelé
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          Správa účtů
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-5 py-3">
                  <div className="text-sm">
                    <button className="font-medium text-blue-600 hover:text-blue-500">
                      Spravovat uživatele
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Logs (Admin only) */}
            {isAdmin && (
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">
                          Bezpečnostní logy
                        </dt>
                        <dd className="text-lg font-medium text-gray-900">
                          Sledování aktivit
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-5 py-3">
                  <div className="text-sm">
                    <button className="font-medium text-blue-600 hover:text-blue-500">
                      Zobrazit logy
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Settings */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Settings className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Nastavení
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        Konfigurace
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-5 py-3">
                <div className="text-sm">
                  <button className="font-medium text-blue-600 hover:text-blue-500">
                    Upravit nastavení
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Debug Info (Admin only) */}
          {isAdmin && (
            <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">
                Debug informace (pouze pro admin)
              </h3>
              <div className="text-xs text-yellow-700 space-y-1">
                <p>User ID: {user?.id}</p>
                <p>Email: {user?.email}</p>
                <p>Admin status: {isAdmin ? 'Ano' : 'Ne'}</p>
                <p>Profile: {profile ? 'Načten' : 'Nenačten'}</p>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
