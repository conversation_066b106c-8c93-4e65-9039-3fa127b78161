{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Programovani/test-html-mqtt/gatekeeper-smart-home-ui/security-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Typy pro naše tabulky\nexport interface Profile {\n  id: string\n  username: string\n  email: string\n  permissions: string[]\n  created_at: string\n  last_login?: string\n  is_active: boolean\n}\n\nexport interface SecurityLog {\n  id: string\n  timestamp: string\n  event_type: string\n  severity: 'low' | 'medium' | 'high' | 'critical'\n  ip_address?: string\n  user_agent?: string\n  user_id?: string\n  details: Record<string, any>\n  created_at: string\n}\n\nexport interface UserSecurity {\n  id: string\n  user_id: string\n  two_factor_enabled: boolean\n  two_factor_secret?: string\n  last_login?: string\n  failed_login_attempts: number\n  account_locked_until?: string\n  created_at: string\n}\n\nexport interface Invite {\n  id: string\n  email: string\n  permissions: string[]\n  status: 'pending' | 'accepted' | 'expired' | 'revoked'\n  token: string\n  message?: string\n  invited_by?: string\n  invited_at: string\n  expires_at: string\n  created_at: string\n}\n\n// Pomocné funkce\nexport const isAdmin = async (): Promise<boolean> => {\n  const { data, error } = await supabase.rpc('is_admin')\n  if (error) {\n    console.error('Error checking admin status:', error)\n    return false\n  }\n  return data\n}\n\nexport const getCurrentProfile = async (): Promise<Profile | null> => {\n  const { data: { user } } = await supabase.auth.getUser()\n  if (!user) return null\n\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', user.id)\n    .single()\n\n  if (error) {\n    console.error('Error fetching profile:', error)\n    return null\n  }\n\n  return data\n}\n"], "names": [], "mappings": ";;;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAkD3C,MAAM,UAAU;IACrB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC;IAC3C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;IACA,OAAO;AACT;AAEO,MAAM,oBAAoB;IAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACtD,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Programovani/test-html-mqtt/gatekeeper-smart-home-ui/security-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase, Profile, getCurrentProfile, isAdmin } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  isAdmin: boolean\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, username: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [isAdminUser, setIsAdminUser] = useState(false)\n  const [loading, setLoading] = useState(true)\n\n  const refreshProfile = async () => {\n    if (user) {\n      const profileData = await getCurrentProfile()\n      setProfile(profileData)\n      \n      const adminStatus = await isAdmin()\n      setIsAdminUser(adminStatus)\n    } else {\n      setProfile(null)\n      setIsAdminUser(false)\n    }\n  }\n\n  useEffect(() => {\n    // Získání aktuálního uživatele\n    supabase.auth.getUser().then(({ data: { user } }) => {\n      setUser(user)\n      setLoading(false)\n    })\n\n    // Poslouchání změn autentifikace\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  useEffect(() => {\n    refreshProfile()\n  }, [user])\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, username: string) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          username,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const value = {\n    user,\n    profile,\n    isAdmin: isAdminUser,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    refreshProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;YAC1C,WAAW;YAEX,MAAM,cAAc,MAAM,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;YAChC,eAAe;QACjB,OAAO;YACL,WAAW;YACX,eAAe;QACjB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,+BAA+B;YAC/B,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;oBAC9C,QAAQ;oBACR,WAAW;gBACb;;YAEA,iCAAiC;YACjC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBACzB,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;oBACJ;gBACF;YACF;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,QAAQ;QACZ;QACA;QACA,SAAS;QACT;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA9EgB;KAAA;AAgFT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}