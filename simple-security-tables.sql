-- <PERSON><PERSON><PERSON><PERSON><PERSON> bezpečnostní tabulky pro Supabase
-- Zkopírujte a vložte do Supabase SQL Editor

-- 1. Security logs tabulka
CREATE TABLE IF NOT EXISTS security_logs (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp timestamptz DEFAULT now() NOT NULL,
  event_type text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  ip_address text,
  user_agent text,
  user_id uuid,
  details jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now()
);

-- 2. User security tabulka
CREATE TABLE IF NOT EXISTS user_security (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid UNIQUE NOT NULL,
  two_factor_enabled boolean DEFAULT false,
  two_factor_secret text,
  last_login timestamptz,
  failed_login_attempts integer DEFAULT 0,
  account_locked_until timestamptz,
  created_at timestamptz DEFAULT now()
);

-- 3. Invites tabulka
CREATE TABLE IF NOT EXISTS invites (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  email text NOT NULL,
  permissions jsonb DEFAULT '[]'::jsonb,
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
  token text UNIQUE NOT NULL,
  message text,
  invited_by uuid,
  invited_at timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- 4. User sessions tabulka
CREATE TABLE IF NOT EXISTS user_sessions (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL,
  session_token text UNIQUE NOT NULL,
  ip_address text,
  user_agent text,
  created_at timestamptz DEFAULT now(),
  last_activity timestamptz DEFAULT now(),
  expires_at timestamptz NOT NULL,
  is_active boolean DEFAULT true
);

-- 5. Základní indexy pro výkon
CREATE INDEX IF NOT EXISTS idx_security_logs_timestamp ON security_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);

CREATE INDEX IF NOT EXISTS idx_user_security_user_id ON user_security(user_id);
CREATE INDEX IF NOT EXISTS idx_user_security_two_factor ON user_security(two_factor_enabled);

CREATE INDEX IF NOT EXISTS idx_invites_email ON invites(email);
CREATE INDEX IF NOT EXISTS idx_invites_token ON invites(token);
CREATE INDEX IF NOT EXISTS idx_invites_status ON invites(status);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);

-- 6. Základní RLS policies (Row Level Security)

-- Security logs
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own security logs" ON security_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert security logs" ON security_logs
  FOR INSERT WITH CHECK (true);

-- User security
ALTER TABLE user_security ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own security settings" ON user_security
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own security settings" ON user_security
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert security settings" ON user_security
  FOR INSERT WITH CHECK (true);

-- Invites - zatím bez RLS, přidáme později
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view pending invites by token" ON invites
  FOR SELECT USING (status = 'pending');

CREATE POLICY "System can manage invites" ON invites
  FOR ALL WITH CHECK (true);

-- User sessions
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own sessions" ON user_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON user_sessions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert sessions" ON user_sessions
  FOR INSERT WITH CHECK (true);

-- 7. Test dotaz pro ověření
SELECT 
  'Tabulky vytvořeny úspěšně!' as status,
  (SELECT count(*) FROM security_logs) as security_logs_count,
  (SELECT count(*) FROM user_security) as user_security_count,
  (SELECT count(*) FROM invites) as invites_count,
  (SELECT count(*) FROM user_sessions) as sessions_count;
