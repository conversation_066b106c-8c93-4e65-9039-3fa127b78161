{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Programovani/test-html-mqtt/gatekeeper-smart-home-ui/security-app/src/components/LoginForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Eye, EyeOff, Lock, Mail, User } from 'lucide-react'\n\nexport default function LoginForm() {\n  const [isLogin, setIsLogin] = useState(true)\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [username, setUsername] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signIn, signUp } = useAuth()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      if (isLogin) {\n        const { error } = await signIn(email, password)\n        if (error) throw error\n      } else {\n        const { error } = await signUp(email, password, username)\n        if (error) throw error\n      }\n    } catch (error: any) {\n      setError(error.message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100\">\n            <Lock className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            {isLogin ? 'Přihlášení' : 'Registrace'}\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Bezpečnostní systém Smart Home\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            {!isLogin && (\n              <div>\n                <label htmlFor=\"username\" className=\"sr-only\">\n                  Uživatelské jméno\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <User className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"username\"\n                    name=\"username\"\n                    type=\"text\"\n                    required={!isLogin}\n                    className=\"appearance-none rounded-md relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                    placeholder=\"Uživatelské jméno\"\n                    value={username}\n                    onChange={(e) => setUsername(e.target.value)}\n                  />\n                </div>\n              </div>\n            )}\n            \n            <div>\n              <label htmlFor=\"email\" className=\"sr-only\">\n                Email\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Mail className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                />\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Heslo\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Heslo\"\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeOff className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <Eye className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Načítání...' : (isLogin ? 'Přihlásit se' : 'Registrovat se')}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              className=\"text-blue-600 hover:text-blue-500 text-sm\"\n              onClick={() => setIsLogin(!isLogin)}\n            >\n              {isLogin ? 'Nemáte účet? Registrujte se' : 'Máte účet? Přihlaste se'}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEjC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,IAAI,SAAS;gBACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;gBACtC,IAAI,OAAO,MAAM;YACnB,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO,UAAU;gBAChD,IAAI,OAAO,MAAM;YACnB;QACF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAG,WAAU;sCACX,UAAU,eAAe;;;;;;sCAE5B,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;8BAKxD,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,yBACA,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAU;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,UAAU,CAAC;oDACX,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAMnD,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAU;;;;;;sDAG3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;8CAK9C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAU;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;8DAE7C,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOxB,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;sCAI3C,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,gBAAiB,UAAU,iBAAiB;;;;;;;;;;;sCAI3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,WAAW,CAAC;0CAE1B,UAAU,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Programovani/test-html-mqtt/gatekeeper-smart-home-ui/security-app/src/components/Dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { LogOut, Shield, Users, Camera, Settings, AlertTriangle } from 'lucide-react'\n\nexport default function Dashboard() {\n  const { user, profile, isAdmin, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Shield className=\"h-8 w-8 text-blue-600\" />\n              <h1 className=\"ml-2 text-xl font-semibold text-gray-900\">\n                Smart Home Security\n              </h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm text-gray-700\">\n                <span className=\"font-medium\">{profile?.username || user?.email}</span>\n                {isAdmin && (\n                  <span className=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                    Admin\n                  </span>\n                )}\n              </div>\n              \n              <button\n                onClick={handleSignOut}\n                className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <LogOut className=\"h-4 w-4 mr-1\" />\n                Odhlásit se\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Welcome Section */}\n          <div className=\"bg-white overflow-hidden shadow rounded-lg mb-6\">\n            <div className=\"px-4 py-5 sm:p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-2\">\n                Vítejte v bezpečnostním systému\n              </h2>\n              <p className=\"text-sm text-gray-600\">\n                Přihlášeni jako: <strong>{profile?.username || user?.email}</strong>\n              </p>\n              {profile?.permissions && (\n                <div className=\"mt-3\">\n                  <p className=\"text-sm text-gray-600 mb-2\">Vaše oprávnění:</p>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {profile.permissions.map((permission) => (\n                      <span\n                        key={permission}\n                        className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                      >\n                        {permission}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Quick Actions Grid */}\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {/* Camera Control */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <Camera className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Kamery\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        4 aktivní\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 px-5 py-3\">\n                <div className=\"text-sm\">\n                  <button className=\"font-medium text-blue-600 hover:text-blue-500\">\n                    Zobrazit záznamy\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* User Management (Admin only) */}\n            {isAdmin && (\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <Users className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                          Uživatelé\n                        </dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          Správa účtů\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-5 py-3\">\n                  <div className=\"text-sm\">\n                    <button className=\"font-medium text-blue-600 hover:text-blue-500\">\n                      Spravovat uživatele\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Security Logs (Admin only) */}\n            {isAdmin && (\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <AlertTriangle className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                          Bezpečnostní logy\n                        </dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          Sledování aktivit\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"bg-gray-50 px-5 py-3\">\n                  <div className=\"text-sm\">\n                    <button className=\"font-medium text-blue-600 hover:text-blue-500\">\n                      Zobrazit logy\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Settings */}\n            <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <Settings className=\"h-6 w-6 text-gray-400\" />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                        Nastavení\n                      </dt>\n                      <dd className=\"text-lg font-medium text-gray-900\">\n                        Konfigurace\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n              <div className=\"bg-gray-50 px-5 py-3\">\n                <div className=\"text-sm\">\n                  <button className=\"font-medium text-blue-600 hover:text-blue-500\">\n                    Upravit nastavení\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Debug Info (Admin only) */}\n          {isAdmin && (\n            <div className=\"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h3 className=\"text-sm font-medium text-yellow-800 mb-2\">\n                Debug informace (pouze pro admin)\n              </h3>\n              <div className=\"text-xs text-yellow-700 space-y-1\">\n                <p>User ID: {user?.id}</p>\n                <p>Email: {user?.email}</p>\n                <p>Admin status: {isAdmin ? 'Ano' : 'Ne'}</p>\n                <p>Profile: {profile ? 'Načten' : 'Nenačten'}</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAElD,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;;;;;;;0CAK3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAe,SAAS,YAAY,MAAM;;;;;;4CACzD,yBACC,8OAAC;gDAAK,WAAU;0DAAuG;;;;;;;;;;;;kDAM3H,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAE,WAAU;;4CAAwB;0DAClB,8OAAC;0DAAQ,SAAS,YAAY,MAAM;;;;;;;;;;;;oCAEtD,SAAS,6BACR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,2BACxB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAanB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;gCAQvE,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;gCASzE,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;8CAS1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA6C;;;;;;8EAG3D,8OAAC;oEAAG,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASzE,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAU,MAAM;;;;;;;sDACnB,8OAAC;;gDAAE;gDAAQ,MAAM;;;;;;;sDACjB,8OAAC;;gDAAE;gDAAe,UAAU,QAAQ;;;;;;;sDACpC,8OAAC;;gDAAE;gDAAU,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Programovani/test-html-mqtt/gatekeeper-smart-home-ui/security-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginForm from '@/components/LoginForm'\nimport Dashboard from '@/components/Dashboard'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return <LoginForm />\n  }\n\n  return <Dashboard />\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;IACnB;IAEA,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;AACnB", "debugId": null}}]}