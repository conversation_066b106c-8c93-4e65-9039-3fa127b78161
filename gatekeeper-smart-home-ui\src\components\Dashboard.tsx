import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { useMQTT } from '@/hooks/useMQTT';
import GateControl from '@/components/GateControl';
import CameraView from '@/components/CameraView';
import NavigationMenu from '@/components/NavigationMenu';
import { Shield } from 'lucide-react';

const Dashboard = () => {
  const { user, profile, logout } = useAuth();
  const { connected, gateStatus, statusMessage, sendCommand } = useMQTT();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
        {/* Navigation */}
      <NavigationMenu user={user} onLogout={logout} connected={connected} />

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 pt-36 md:pt-24 pb-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Gate Control */}
          <div className="lg:col-span-2">
            <GateControl 
              status={gateStatus} 
              statusMessage={statusMessage}
              onCommand={sendCommand}
              hasPermission={profile?.permissions?.includes('gate')}
            />
          </div>
          {/* Camera View and Quick Stats */}
          <div className="space-y-6">
            <CameraView hasPermission={profile?.permissions?.includes('camera')} />
            {/* Quick Stats */}
            <Card className="backdrop-blur-lg bg-white/10 border-purple-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>Rychlé informace</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Status brány:</span>
                  <span className={`font-medium ${
                    gateStatus === 'open' ? 'text-green-400' : 
                    gateStatus === 'moving' ? 'text-orange-400' : 'text-red-400'
                  }`}>
                    {statusMessage || 'Neznámý stav'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Uživatel:</span>
                  <span className="text-white font-medium">{profile?.username}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-purple-200">Oprávnění:</span>
                  <span className="text-white font-medium">{profile?.permissions?.length || 0}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
