import { useState } from 'react';
import { supabase, type Tables, type TablesInsert } from '../lib/supabase';
import { useAuth } from './useSupabaseAuth';

interface AdminHookResult {
  users: Tables<'profiles'>[];
  invites: Tables<'invites'>[];
  loginLogs: Tables<'login_logs'>[];
  loading: boolean;
  
  // User management
  fetchUsers: () => Promise<void>;
  createUser: (userData: {
    email: string;
    password: string;
    username: string;
    permissions: string[];
  }) => Promise<void>;
  updateUser: (userId: string, updates: {
    username?: string;
    email?: string;
    permissions?: string[];
    isActive?: boolean;
  }) => Promise<void>;
  deactivateUser: (userId: string) => Promise<void>;
  
  // Invite management
  fetchInvites: () => Promise<void>;
  createInvite: (permissions: string[], expiresInHours?: number) => Promise<{
    token: string;
    url: string;
    expiresAt: string;
  }>;
  
  // Logs
  fetchLoginLogs: (limit?: number, offset?: number) => Promise<void>;
}

export const useAdmin = (): AdminHookResult => {
  const { user, profile } = useAuth();
  const [users, setUsers] = useState<Tables<'profiles'>[]>([]);
  const [invites, setInvites] = useState<Tables<'invites'>[]>([]);
  const [loginLogs, setLoginLogs] = useState<Tables<'login_logs'>[]>([]);
  const [loading, setLoading] = useState(false);

  // Kontrola admin oprávnění
  const checkAdminPermission = () => {
    if (!profile?.permissions.includes('admin')) {
      throw new Error('Vyžaduje admin oprávnění');
    }
  };

  const fetchUsers = async () => {
    checkAdminPermission();
    setLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Chyba při načítání uživatelů:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const createUser = async (userData: {
    email: string;
    password: string;
    username: string;
    permissions: string[];
  }) => {
    checkAdminPermission();
    setLoading(true);

    try {
      // Vytvořit uživatele v Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true
      });

      if (authError) throw authError;
      if (!authData.user) throw new Error('Nepodařilo se vytvořit uživatele');

      // Vytvořit profil
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          username: userData.username,
          email: userData.email,
          permissions: userData.permissions,
          is_active: true
        });

      if (profileError) throw profileError;

      // Aktualizovat seznam uživatelů
      await fetchUsers();
    } catch (error) {
      console.error('Chyba při vytváření uživatele:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async (userId: string, updates: {
    username?: string;
    email?: string;
    permissions?: string[];
    isActive?: boolean;
  }) => {
    checkAdminPermission();
    setLoading(true);

    try {
      // Ochrana proti úpravě vlastního účtu
      if (userId === user?.id && updates.isActive === false) {
        throw new Error('Nemůžete deaktivovat vlastní účet');
      }

      const updateData: any = {};
      if (updates.username !== undefined) updateData.username = updates.username;
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.permissions !== undefined) updateData.permissions = updates.permissions;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) throw error;

      // Aktualizovat seznam uživatelů
      await fetchUsers();
    } catch (error) {
      console.error('Chyba při aktualizaci uživatele:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const deactivateUser = async (userId: string) => {
    checkAdminPermission();

    // Ochrana proti deaktivaci vlastního účtu
    if (userId === user?.id) {
      throw new Error('Nemůžete deaktivovat vlastní účet');
    }

    await updateUser(userId, { isActive: false });
  };

  const fetchInvites = async () => {
    checkAdminPermission();
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('invites')
        .select(`
          *,
          created_by_profile:profiles!invites_created_by_fkey(username),
          used_by_profile:profiles!invites_used_by_fkey(username)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setInvites(data || []);
    } catch (error) {
      console.error('Chyba při načítání pozvánek:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const createInvite = async (permissions: string[], expiresInHours: number = 24) => {
    checkAdminPermission();
    setLoading(true);

    try {
      // Generovat pozvánkový token
      const token = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + expiresInHours * 60 * 60 * 1000).toISOString();

      const { error } = await supabase
        .from('invites')
        .insert({
          token,
          permissions,
          expires_at: expiresAt,
          created_by: user!.id
        });

      if (error) throw error;

      const inviteUrl = `${window.location.origin}/register?invite=${token}`;

      // Aktualizovat seznam pozvánek
      await fetchInvites();

      return {
        token,
        url: inviteUrl,
        expiresAt
      };
    } catch (error) {
      console.error('Chyba při vytváření pozvánky:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const fetchLoginLogs = async (limit: number = 50, offset: number = 0) => {
    checkAdminPermission();
    setLoading(true);

    try {
      const { data, error } = await supabase
        .from('login_logs')
        .select(`
          *,
          profile:profiles(username)
        `)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      setLoginLogs(data || []);
    } catch (error) {
      console.error('Chyba při načítání logů:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    users,
    invites,
    loginLogs,
    loading,
    fetchUsers,
    createUser,
    updateUser,
    deactivateUser,
    fetchInvites,
    createInvite,
    fetchLoginLogs
  };
};
