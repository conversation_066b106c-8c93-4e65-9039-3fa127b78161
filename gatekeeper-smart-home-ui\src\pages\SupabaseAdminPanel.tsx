import { useAuth } from '@/hooks/useSupabaseAuth';
import { Navigate } from 'react-router-dom';
import NavigationMenu from '@/components/NavigationMenu';
import { useMQTT } from '@/hooks/useMQTT';
import SupabaseAdminPanel from '@/components/SupabaseAdminPanel';

const SupabaseAdminPanelPage = () => {
  const { user, profile, logout } = useAuth();
  const { connected } = useMQTT();

  if (!profile?.permissions?.includes('admin')) {
    return <Navigate to="/" replace />;
  }

  // Adaptér pro NavigationMenu (očekává starý formát user objektu)
  const legacyUser = profile ? {
    id: profile.id,
    username: profile.username,
    email: profile.email,
    permissions: profile.permissions,
    lastLogin: profile.last_login
  } : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
      
      {/* Navigation */}
      <NavigationMenu user={legacyUser} onLogout={logout} connected={connected} />

      {/* Admin Panel Content */}
      <div className="relative z-10 pt-24">
        <SupabaseAdminPanel />
      </div>
    </div>
  );
};

export default SupabaseAdminPanelPage;
