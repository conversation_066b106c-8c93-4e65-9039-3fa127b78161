#!/usr/bin/env python3
"""
Script pro testování a opravu databázových tabulek v Supabase
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

# Načtení environment variables
load_dotenv()

def get_supabase_client():
    """Vytvoří Supabase klienta"""
    url = os.getenv("VITE_SUPABASE_URL") or os.getenv("NEXT_PUBLIC_SUPABASE_URL")
    key = os.getenv("VITE_SUPABASE_ANON_KEY") or os.getenv("SUPABASE_SERVICE_ROLE_KEY")

    if not url:
        print("❌ Chybí VITE_SUPABASE_URL v .env")
        sys.exit(1)

    if not key:
        print("❌ Chybí VITE_SUPABASE_ANON_KEY v .env")
        print("💡 Zkusím použít anon key pro základní operace...")

    return create_client(url, key)

def test_table_exists(supabase: Client, table_name: str) -> bool:
    """Testuje, zda tabulka existuje"""
    try:
        result = supabase.table(table_name).select("*").limit(1).execute()
        print(f"✅ Tabulka '{table_name}' existuje")
        return True
    except Exception as e:
        print(f"❌ Tabulka '{table_name}' neexistuje: {e}")
        return False

def create_simple_tables(supabase: Client):
    """Vytvoří jednoduché verze tabulek bez problematických částí"""
    
    # Jednoduché SQL příkazy bez RLS a složitých referencí
    simple_sql = """
    -- 1. Security logs
    CREATE TABLE IF NOT EXISTS security_logs (
      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
      timestamp timestamptz DEFAULT now() NOT NULL,
      event_type text NOT NULL,
      severity text NOT NULL,
      ip_address text,
      user_agent text,
      user_id uuid,
      details jsonb DEFAULT '{}'::jsonb,
      created_at timestamptz DEFAULT now()
    );

    -- 2. User security
    CREATE TABLE IF NOT EXISTS user_security (
      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id uuid UNIQUE NOT NULL,
      two_factor_enabled boolean DEFAULT false,
      last_login timestamptz,
      failed_login_attempts integer DEFAULT 0,
      created_at timestamptz DEFAULT now()
    );

    -- 3. Invites
    CREATE TABLE IF NOT EXISTS invites (
      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
      email text NOT NULL,
      permissions jsonb DEFAULT '[]'::jsonb,
      status text DEFAULT 'pending',
      token text UNIQUE NOT NULL,
      message text,
      invited_by uuid,
      invited_at timestamptz DEFAULT now(),
      expires_at timestamptz NOT NULL,
      created_at timestamptz DEFAULT now()
    );

    -- 4. User sessions
    CREATE TABLE IF NOT EXISTS user_sessions (
      id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
      user_id uuid NOT NULL,
      session_token text UNIQUE NOT NULL,
      ip_address text,
      user_agent text,
      created_at timestamptz DEFAULT now(),
      last_activity timestamptz DEFAULT now(),
      expires_at timestamptz NOT NULL,
      is_active boolean DEFAULT true
    );
    """
    
    try:
        # Spustíme SQL přímo
        result = supabase.rpc('exec_sql', {'sql': simple_sql}).execute()
        print("✅ Jednoduché tabulky vytvořeny úspěšně!")
        return True
    except Exception as e:
        print(f"❌ Chyba při vytváření tabulek: {e}")
        
        # Zkusíme po jedné tabulce
        tables = [
            ("security_logs", """
                CREATE TABLE IF NOT EXISTS security_logs (
                  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
                  timestamp timestamptz DEFAULT now() NOT NULL,
                  event_type text NOT NULL,
                  severity text NOT NULL,
                  ip_address text,
                  user_agent text,
                  user_id uuid,
                  details jsonb DEFAULT '{}'::jsonb,
                  created_at timestamptz DEFAULT now()
                );
            """),
            ("user_security", """
                CREATE TABLE IF NOT EXISTS user_security (
                  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
                  user_id uuid UNIQUE NOT NULL,
                  two_factor_enabled boolean DEFAULT false,
                  last_login timestamptz,
                  failed_login_attempts integer DEFAULT 0,
                  created_at timestamptz DEFAULT now()
                );
            """),
            ("invites", """
                CREATE TABLE IF NOT EXISTS invites (
                  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
                  email text NOT NULL,
                  permissions jsonb DEFAULT '[]'::jsonb,
                  status text DEFAULT 'pending',
                  token text UNIQUE NOT NULL,
                  message text,
                  invited_by uuid,
                  invited_at timestamptz DEFAULT now(),
                  expires_at timestamptz NOT NULL,
                  created_at timestamptz DEFAULT now()
                );
            """),
            ("user_sessions", """
                CREATE TABLE IF NOT EXISTS user_sessions (
                  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
                  user_id uuid NOT NULL,
                  session_token text UNIQUE NOT NULL,
                  ip_address text,
                  user_agent text,
                  created_at timestamptz DEFAULT now(),
                  last_activity timestamptz DEFAULT now(),
                  expires_at timestamptz NOT NULL,
                  is_active boolean DEFAULT true
                );
            """)
        ]
        
        for table_name, sql in tables:
            try:
                supabase.rpc('exec_sql', {'sql': sql}).execute()
                print(f"✅ Tabulka '{table_name}' vytvořena")
            except Exception as table_error:
                print(f"❌ Chyba při vytváření '{table_name}': {table_error}")
        
        return False

def main():
    """Hlavní funkce"""
    print("🚀 Testování a oprava databázových tabulek...")
    
    # Vytvoření klienta
    supabase = get_supabase_client()
    print("✅ Připojení k Supabase úspěšné")
    
    # Testování existujících tabulek
    required_tables = ["security_logs", "user_security", "invites", "user_sessions"]
    missing_tables = []
    
    for table in required_tables:
        if not test_table_exists(supabase, table):
            missing_tables.append(table)
    
    if missing_tables:
        print(f"\n🔧 Chybí tabulky: {missing_tables}")
        print("📝 Vytvářím jednoduché verze tabulek...")
        
        if create_simple_tables(supabase):
            print("\n✅ Všechny tabulky vytvořeny!")
        else:
            print("\n⚠️ Některé tabulky se nepodařilo vytvořit, ale základní struktura by měla fungovat")
    else:
        print("\n✅ Všechny požadované tabulky již existují!")
    
    # Finální test
    print("\n🔍 Finální test všech tabulek:")
    all_good = True
    for table in required_tables:
        if test_table_exists(supabase, table):
            print(f"  ✅ {table}")
        else:
            print(f"  ❌ {table}")
            all_good = False
    
    if all_good:
        print("\n🎉 Databáze je připravena! Aplikace by měla fungovat.")
    else:
        print("\n⚠️ Některé tabulky stále chybí. Zkontrolujte Supabase dashboard.")

if __name__ == "__main__":
    main()
