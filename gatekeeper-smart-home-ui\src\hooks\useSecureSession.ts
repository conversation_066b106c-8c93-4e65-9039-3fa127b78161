import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { User, Session } from '@supabase/supabase-js';
import { securityLogger } from '@/middleware/security';

interface SessionState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isValid: boolean;
  expiresAt: number | null;
  lastActivity: number;
  deviceFingerprint: string;
}

interface SessionSecurity {
  maxInactivity: number; // ms
  warningTime: number; // ms před expirací
  refreshThreshold: number; // ms před expirací kdy refreshovat
  maxSessionDuration: number; // ms
}

const DEFAULT_SECURITY_CONFIG: SessionSecurity = {
  maxInactivity: 30 * 60 * 1000, // 30 minut
  warningTime: 5 * 60 * 1000, // 5 minut
  refreshThreshold: 10 * 60 * 1000, // 10 minut
  maxSessionDuration: 8 * 60 * 60 * 1000, // 8 hodin
};

export const useSecureSession = (config: Partial<SessionSecurity> = {}) => {
  const securityConfig = { ...DEFAULT_SECURITY_CONFIG, ...config };
  
  const [sessionState, setSessionState] = useState<SessionState>({
    user: null,
    session: null,
    loading: true,
    isValid: false,
    expiresAt: null,
    lastActivity: Date.now(),
    deviceFingerprint: ''
  });

  const [showWarning, setShowWarning] = useState(false);
  const [timeUntilExpiry, setTimeUntilExpiry] = useState<number | null>(null);

  // Generování device fingerprint
  const generateDeviceFingerprint = useCallback((): string => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 0,
      navigator.deviceMemory || 0
    ].join('|');
    
    // Jednoduchý hash
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return Math.abs(hash).toString(36);
  }, []);

  // Aktualizace aktivity
  const updateActivity = useCallback(() => {
    setSessionState(prev => ({
      ...prev,
      lastActivity: Date.now()
    }));
  }, []);

  // Kontrola validity session
  const validateSession = useCallback(async (): Promise<boolean> => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        await securityLogger.logSecurityEvent('SESSION_INVALID', { error: error?.message });
        return false;
      }

      const now = Date.now();
      const expiresAt = session.expires_at ? session.expires_at * 1000 : 0;
      const sessionAge = now - (session.issued_at ? new Date(session.issued_at).getTime() : 0);
      const inactivityTime = now - sessionState.lastActivity;

      // Kontrola expiry
      if (expiresAt && now >= expiresAt) {
        await securityLogger.logSecurityEvent('SESSION_EXPIRED', { expiresAt });
        return false;
      }

      // Kontrola maximální doby session
      if (sessionAge > securityConfig.maxSessionDuration) {
        await securityLogger.logSecurityEvent('SESSION_MAX_DURATION_EXCEEDED', { sessionAge });
        await supabase.auth.signOut();
        return false;
      }

      // Kontrola neaktivity
      if (inactivityTime > securityConfig.maxInactivity) {
        await securityLogger.logSecurityEvent('SESSION_INACTIVE_TIMEOUT', { inactivityTime });
        await supabase.auth.signOut();
        return false;
      }

      // Kontrola device fingerprint
      const currentFingerprint = generateDeviceFingerprint();
      if (sessionState.deviceFingerprint && 
          sessionState.deviceFingerprint !== currentFingerprint) {
        await securityLogger.logSecurityEvent('DEVICE_FINGERPRINT_MISMATCH', {
          stored: sessionState.deviceFingerprint,
          current: currentFingerprint
        });
        // Můžeme buď vylogovat nebo požádat o re-autentifikaci
        // Pro bezpečnost vylogujeme
        await supabase.auth.signOut();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Chyba při validaci session:', error);
      await securityLogger.logSecurityEvent('SESSION_VALIDATION_ERROR', { error });
      return false;
    }
  }, [sessionState.lastActivity, sessionState.deviceFingerprint, securityConfig, generateDeviceFingerprint]);

  // Refresh token
  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error || !data.session) {
        await securityLogger.logSecurityEvent('SESSION_REFRESH_FAILED', { error: error?.message });
        return false;
      }

      await securityLogger.logSecurityEvent('SESSION_REFRESHED', {
        userId: data.session.user.id,
        expiresAt: data.session.expires_at
      });

      setSessionState(prev => ({
        ...prev,
        session: data.session,
        user: data.session.user,
        expiresAt: data.session.expires_at ? data.session.expires_at * 1000 : null,
        isValid: true
      }));

      return true;
    } catch (error) {
      console.error('Chyba při refresh session:', error);
      await securityLogger.logSecurityEvent('SESSION_REFRESH_ERROR', { error });
      return false;
    }
  }, []);

  // Vylogování
  const signOut = useCallback(async (reason: string = 'USER_LOGOUT') => {
    try {
      await securityLogger.logSecurityEvent('SESSION_LOGOUT', {
        userId: sessionState.user?.id,
        reason
      });

      await supabase.auth.signOut();
      
      setSessionState({
        user: null,
        session: null,
        loading: false,
        isValid: false,
        expiresAt: null,
        lastActivity: Date.now(),
        deviceFingerprint: ''
      });

      setShowWarning(false);
      setTimeUntilExpiry(null);
    } catch (error) {
      console.error('Chyba při odhlášení:', error);
    }
  }, [sessionState.user?.id]);

  // Prodloužení session
  const extendSession = useCallback(async () => {
    const success = await refreshSession();
    if (success) {
      setShowWarning(false);
      updateActivity();
    }
    return success;
  }, [refreshSession, updateActivity]);

  // Inicializace session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Chyba při načítání session:', error);
          setSessionState(prev => ({ ...prev, loading: false }));
          return;
        }

        const fingerprint = generateDeviceFingerprint();
        
        if (session) {
          const isValid = await validateSession();
          setSessionState({
            user: session.user,
            session,
            loading: false,
            isValid,
            expiresAt: session.expires_at ? session.expires_at * 1000 : null,
            lastActivity: Date.now(),
            deviceFingerprint: fingerprint
          });

          if (isValid) {
            await securityLogger.logSecurityEvent('SESSION_INITIALIZED', {
              userId: session.user.id,
              deviceFingerprint: fingerprint
            });
          }
        } else {
          setSessionState(prev => ({
            ...prev,
            loading: false,
            deviceFingerprint: fingerprint
          }));
        }
      } catch (error) {
        console.error('Chyba při inicializaci session:', error);
        setSessionState(prev => ({ ...prev, loading: false }));
      }
    };

    initializeSession();
  }, [generateDeviceFingerprint, validateSession]);

  // Monitoring session
  useEffect(() => {
    if (!sessionState.session || !sessionState.isValid) return;

    const interval = setInterval(async () => {
      const now = Date.now();
      const expiresAt = sessionState.expiresAt;
      
      if (!expiresAt) return;

      const timeUntilExpiry = expiresAt - now;
      setTimeUntilExpiry(timeUntilExpiry);

      // Varování před expirací
      if (timeUntilExpiry <= securityConfig.warningTime && timeUntilExpiry > 0) {
        setShowWarning(true);
      }

      // Auto refresh před expirací
      if (timeUntilExpiry <= securityConfig.refreshThreshold && timeUntilExpiry > 0) {
        const success = await refreshSession();
        if (!success) {
          await signOut('AUTO_REFRESH_FAILED');
        }
      }

      // Session expirovala
      if (timeUntilExpiry <= 0) {
        await signOut('SESSION_EXPIRED');
      }

      // Kontrola neaktivity
      const inactivityTime = now - sessionState.lastActivity;
      if (inactivityTime > securityConfig.maxInactivity) {
        await signOut('INACTIVITY_TIMEOUT');
      }
    }, 30000); // Kontrola každých 30 sekund

    return () => clearInterval(interval);
  }, [sessionState.session, sessionState.isValid, sessionState.expiresAt, sessionState.lastActivity, securityConfig, refreshSession, signOut]);

  // Event listenery pro aktivitu
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateActivity]);

  // Auth state listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT') {
          setSessionState({
            user: null,
            session: null,
            loading: false,
            isValid: false,
            expiresAt: null,
            lastActivity: Date.now(),
            deviceFingerprint: generateDeviceFingerprint()
          });
          setShowWarning(false);
        } else if (event === 'SIGNED_IN' && session) {
          const isValid = await validateSession();
          setSessionState({
            user: session.user,
            session,
            loading: false,
            isValid,
            expiresAt: session.expires_at ? session.expires_at * 1000 : null,
            lastActivity: Date.now(),
            deviceFingerprint: generateDeviceFingerprint()
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [generateDeviceFingerprint, validateSession]);

  return {
    ...sessionState,
    showWarning,
    timeUntilExpiry,
    signOut,
    extendSession,
    updateActivity,
    refreshSession,
    validateSession
  };
};
