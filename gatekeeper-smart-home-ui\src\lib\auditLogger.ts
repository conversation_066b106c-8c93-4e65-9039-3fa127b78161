// Pokročilý audit logging systém
import { supabase } from './supabase';

interface SecurityEvent {
  timestamp: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  ip_address: string;
  user_agent: string;
  user_id: string | null;
  details: string;
  session_id: string;
  risk_score: number;
  location_info: any;
  device_fingerprint: string;
}

interface SuspiciousPattern {
  pattern: string;
  count: number;
  firstSeen: number;
  lastSeen: number;
  severity: number;
}

class AuditLogger {
  private ipCache: string | null = null;
  private deviceInfo: any = null;
  private suspiciousPatterns: Map<string, SuspiciousPattern> = new Map();
  private offlineLogs: SecurityEvent[] = [];
  private isOnline: boolean = navigator.onLine;

  constructor() {
    // Sledování online/offline stavu
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.syncOfflineLogs();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });

    // Pravidelná synchronizace offline logů
    setInterval(() => {
      if (this.isOnline) {
        this.syncOfflineLogs();
      }
    }, 60000); // každou minutu

    // Pravidelné čištění starých vzorů
    setInterval(() => {
      this.cleanupSuspiciousPatterns();
    }, 5 * 60 * 1000); // každých 5 minut
  }

  // Hlavní metoda pro logování bezpečnostních událostí
  async logSecurityEvent(
    eventType: string,
    details: Record<string, any> = {},
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    try {
      const logEntry: SecurityEvent = {
        timestamp: new Date().toISOString(),
        event_type: eventType,
        severity,
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
        user_id: details.userId || null,
        details: JSON.stringify({
          ...details,
          deviceInfo: await this.getDeviceInfo(),
          browserInfo: this.getBrowserInfo(),
          screenInfo: this.getScreenInfo(),
          timezoneInfo: this.getTimezoneInfo(),
          performanceInfo: this.getPerformanceInfo()
        }),
        session_id: this.getSessionId(),
        risk_score: this.calculateRiskScore(eventType, details),
        location_info: await this.getLocationInfo(),
        device_fingerprint: this.generateDeviceFingerprint()
      };

      // Analýza podezřelých vzorů před uložením
      await this.analyzeSuspiciousActivity(eventType, details);

      // Uložení logu
      if (this.isOnline) {
        await this.saveToDatabase(logEntry);
      } else {
        this.saveToOfflineStorage(logEntry);
      }

      // Speciální akce pro kritické události
      if (severity === 'critical') {
        await this.handleCriticalEvent(logEntry);
      }

      // Automatické bezpečnostní odpovědi
      if (logEntry.risk_score >= 80) {
        await this.triggerSecurityResponse(logEntry);
      }

    } catch (error) {
      console.error('Chyba při logování bezpečnostní události:', error);
      // Fallback uložení
      this.saveToLocalStorage({
        timestamp: new Date().toISOString(),
        event_type: eventType,
        severity,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: JSON.stringify(details)
      });
    }
  }

  // Výpočet risk score
  private calculateRiskScore(eventType: string, details: Record<string, any>): number {
    let score = 0;

    // Základní skóre podle typu události
    const eventScores: Record<string, number> = {
      'LOGIN_FAILED': 30,
      'LOGIN_SUCCESS': 5,
      'RATE_LIMITED': 50,
      'SUSPICIOUS_ACTIVITY': 70,
      'INVALID_TOKEN': 60,
      'DEVICE_FINGERPRINT_MISMATCH': 80,
      'SESSION_HIJACK_ATTEMPT': 90,
      'SQL_INJECTION_ATTEMPT': 95,
      'XSS_ATTEMPT': 85,
      'BRUTE_FORCE_DETECTED': 85,
      'MULTIPLE_FAILED_LOGINS': 70,
      'LOGIN_FROM_NEW_LOCATION': 40,
      'LOGIN_FROM_NEW_DEVICE': 50,
      'ADMIN_ACTION_UNAUTHORIZED': 90,
      'PASSWORD_CHANGED': 20,
      '2FA_ENABLED': 10,
      '2FA_DISABLED': 30,
      'PERMISSION_ESCALATION': 85,
      'DATA_EXPORT': 40,
      'BULK_OPERATION': 30
    };

    score += eventScores[eventType] || 20;

    // Modifikátory na základě kontextu
    if (details.failedAttempts) {
      score += Math.min(details.failedAttempts * 10, 50);
    }

    if (details.isNewDevice) score += 20;
    if (details.isNewLocation) score += 15;
    if (details.isOffHours) score += 10;
    if (details.isWeekend) score += 5;
    if (details.isTorNetwork) score += 40;
    if (details.isVpn) score += 15;

    // Kontrola user agenta
    if (this.isSuspiciousUserAgent(navigator.userAgent)) {
      score += 30;
    }

    // Frekvence událostí
    const pattern = `${eventType}_${details.userId || 'anonymous'}`;
    const patternData = this.suspiciousPatterns.get(pattern);
    if (patternData && patternData.count > 3) {
      score += Math.min(patternData.count * 5, 30);
    }

    return Math.min(score, 100);
  }

  // Analýza podezřelých aktivit
  private async analyzeSuspiciousActivity(eventType: string, details: Record<string, any>): Promise<void> {
    const key = `${eventType}_${details.userId || 'anonymous'}`;
    const now = Date.now();
    
    const existing = this.suspiciousPatterns.get(key);
    if (existing) {
      existing.count++;
      existing.lastSeen = now;
      existing.severity = Math.min(existing.severity + 1, 10);
    } else {
      this.suspiciousPatterns.set(key, {
        pattern: key,
        count: 1,
        firstSeen: now,
        lastSeen: now,
        severity: 1
      });
    }

    const pattern = this.suspiciousPatterns.get(key)!;

    // Detekce specifických vzorů
    if (pattern.count >= 5 && eventType === 'LOGIN_FAILED') {
      await this.logSecurityEvent('BRUTE_FORCE_DETECTED', {
        ...details,
        attemptCount: pattern.count,
        timespan: now - pattern.firstSeen
      }, 'critical');
    }

    if (pattern.count >= 10) {
      await this.logSecurityEvent('PERSISTENT_ATTACK_DETECTED', {
        ...details,
        attemptCount: pattern.count,
        pattern: eventType,
        duration: now - pattern.firstSeen
      }, 'critical');
    }

    // Rychlé následné pokusy
    if (pattern.count >= 3 && (now - pattern.firstSeen) < 60000) {
      await this.logSecurityEvent('RAPID_FIRE_ATTEMPTS', {
        ...details,
        attemptCount: pattern.count,
        timespan: now - pattern.firstSeen
      }, 'high');
    }
  }

  // Vyčištění starých vzorů
  private cleanupSuspiciousPatterns(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hodin

    for (const [key, pattern] of this.suspiciousPatterns.entries()) {
      if (now - pattern.lastSeen > maxAge) {
        this.suspiciousPatterns.delete(key);
      }
    }

    // Omezení velikosti cache
    if (this.suspiciousPatterns.size > 1000) {
      const entries = Array.from(this.suspiciousPatterns.entries());
      entries.sort((a, b) => b[1].lastSeen - a[1].lastSeen);
      
      this.suspiciousPatterns.clear();
      entries.slice(0, 500).forEach(([key, value]) => {
        this.suspiciousPatterns.set(key, value);
      });
    }
  }

  // Kontrola podezřelého user agenta
  private isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /curl/i, /wget/i, /python/i, /java/i,
      /go-http/i, /postman/i, /insomnia/i,
      /nikto/i, /sqlmap/i, /nmap/i, /masscan/i,
      /burp/i, /zap/i, /w3af/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  // Získání IP adresy
  private async getClientIP(): Promise<string> {
    // Dočasně vypnuto kvůli CSP - použijeme lokální detekci
    return 'localhost';
  }

  // Získání informací o zařízení
  private async getDeviceInfo(): Promise<any> {
    if (this.deviceInfo) return this.deviceInfo;

    this.deviceInfo = {
      platform: navigator.platform,
      language: navigator.language,
      languages: navigator.languages,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
      deviceMemory: (navigator as any).deviceMemory || 'unknown',
      connection: this.getConnectionInfo(),
      battery: await this.getBatteryInfo()
    };

    return this.deviceInfo;
  }

  // Informace o připojení
  private getConnectionInfo(): any {
    const connection = (navigator as any).connection;
    if (!connection) return 'unknown';

    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }

  // Informace o baterii
  private async getBatteryInfo(): Promise<any> {
    try {
      if ('getBattery' in navigator) {
        const battery = await (navigator as any).getBattery();
        return {
          charging: battery.charging,
          level: battery.level,
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime
        };
      }
      return 'not_supported';
    } catch {
      return 'error';
    }
  }

  // Informace o prohlížeči
  private getBrowserInfo(): any {
    return {
      userAgent: navigator.userAgent,
      vendor: navigator.vendor,
      appName: navigator.appName,
      appVersion: navigator.appVersion,
      product: navigator.product,
      webdriver: (navigator as any).webdriver || false
    };
  }

  // Informace o obrazovce
  private getScreenInfo(): any {
    return {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      orientation: screen.orientation?.type || 'unknown'
    };
  }

  // Informace o časovém pásmu
  private getTimezoneInfo(): any {
    return {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      timezoneOffset: new Date().getTimezoneOffset(),
      locale: Intl.DateTimeFormat().resolvedOptions().locale,
      calendar: Intl.DateTimeFormat().resolvedOptions().calendar
    };
  }

  // Informace o výkonu
  private getPerformanceInfo(): any {
    if (!performance) return 'not_supported';

    return {
      memory: (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      } : 'not_available',
      timing: performance.timing ? {
        navigationStart: performance.timing.navigationStart,
        loadEventEnd: performance.timing.loadEventEnd,
        domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
      } : 'not_available'
    };
  }

  // Získání informací o poloze
  private async getLocationInfo(): Promise<any> {
    // Dočasně vypnuto kvůli CSP
    return 'disabled';
  }

  // Generování device fingerprint
  private generateDeviceFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }

    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 0,
      navigator.deviceMemory || 0,
      navigator.platform
    ];

    const fingerprint = components.join('|');
    
    // Jednoduchý hash
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }

    return Math.abs(hash).toString(36);
  }

  // Uložení do databáze
  private async saveToDatabase(logEntry: SecurityEvent): Promise<void> {
    const { error } = await supabase
      .from('security_logs')
      .insert([logEntry]);

    if (error) {
      console.error('Chyba při ukládání do databáze:', error);
      this.saveToOfflineStorage(logEntry);
    }
  }

  // Uložení do offline úložiště
  private saveToOfflineStorage(logEntry: SecurityEvent): void {
    this.offlineLogs.push(logEntry);
    
    // Omezení velikosti offline cache
    if (this.offlineLogs.length > 100) {
      this.offlineLogs = this.offlineLogs.slice(-50);
    }

    // Uložení do localStorage jako backup
    this.saveToLocalStorage(logEntry);
  }

  // Uložení do localStorage
  private saveToLocalStorage(logEntry: any): void {
    try {
      const logs = JSON.parse(localStorage.getItem('security_logs_offline') || '[]');
      logs.push(logEntry);
      
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      
      localStorage.setItem('security_logs_offline', JSON.stringify(logs));
    } catch (error) {
      console.error('Chyba při ukládání do localStorage:', error);
    }
  }

  // Synchronizace offline logů
  private async syncOfflineLogs(): Promise<void> {
    if (this.offlineLogs.length === 0) return;

    try {
      const { error } = await supabase
        .from('security_logs')
        .insert(this.offlineLogs);

      if (!error) {
        console.log(`Synchronizováno ${this.offlineLogs.length} offline logů`);
        this.offlineLogs = [];
        
        // Vyčištění localStorage
        localStorage.removeItem('security_logs_offline');
      }
    } catch (error) {
      console.error('Chyba při synchronizaci offline logů:', error);
    }
  }

  // Zpracování kritických událostí
  private async handleCriticalEvent(logEntry: SecurityEvent): Promise<void> {
    console.error('🚨 KRITICKÁ BEZPEČNOSTNÍ UDÁLOST:', {
      event: logEntry.event_type,
      time: logEntry.timestamp,
      user: logEntry.user_id,
      risk: logEntry.risk_score,
      ip: logEntry.ip_address
    });

    // V produkci by mělo posílat notifikace
    try {
      // await supabase.functions.invoke('send-security-alert', { body: logEntry });
    } catch (error) {
      console.error('Chyba při odesílání kritického upozornění:', error);
    }
  }

  // Spuštění bezpečnostní odpovědi
  private async triggerSecurityResponse(logEntry: SecurityEvent): Promise<void> {
    try {
      if (logEntry.risk_score >= 90) {
        // Okamžité vylogování
        await supabase.auth.signOut();
        
        await this.logSecurityEvent('AUTO_SECURITY_RESPONSE', {
          action: 'force_logout',
          trigger: logEntry.event_type,
          risk_score: logEntry.risk_score
        }, 'high');
      } else if (logEntry.risk_score >= 80) {
        // Varování uživatele
        await this.logSecurityEvent('SECURITY_WARNING_ISSUED', {
          trigger: logEntry.event_type,
          risk_score: logEntry.risk_score
        }, 'medium');
      }
    } catch (error) {
      console.error('Chyba při spouštění bezpečnostní odpovědi:', error);
    }
  }

  // Získání session ID
  private getSessionId(): string {
    let sessionId = localStorage.getItem('session_id');
    if (!sessionId) {
      sessionId = 'sess_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('session_id', sessionId);
    }
    return sessionId;
  }

  // Veřejné metody pro snadné použití
  async logLogin(success: boolean, userId?: string, details: any = {}): Promise<void> {
    await this.logSecurityEvent(
      success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
      { userId, ...details },
      success ? 'low' : 'medium'
    );
  }

  async logLogout(userId?: string, reason: string = 'user_action'): Promise<void> {
    await this.logSecurityEvent('LOGOUT', { userId, reason }, 'low');
  }

  async logSuspiciousActivity(activity: string, details: any = {}): Promise<void> {
    await this.logSecurityEvent('SUSPICIOUS_ACTIVITY', { activity, ...details }, 'high');
  }

  async logAdminAction(action: string, userId: string, details: any = {}): Promise<void> {
    await this.logSecurityEvent('ADMIN_ACTION', { action, userId, ...details }, 'medium');
  }

  async logDataAccess(resource: string, userId: string, action: string = 'read'): Promise<void> {
    await this.logSecurityEvent('DATA_ACCESS', { resource, userId, action }, 'low');
  }

  // Získání statistik
  getSuspiciousPatterns(): Map<string, SuspiciousPattern> {
    return new Map(this.suspiciousPatterns);
  }

  getOfflineLogsCount(): number {
    return this.offlineLogs.length;
  }
}

// Export singleton instance
export const auditLogger = new AuditLogger();
