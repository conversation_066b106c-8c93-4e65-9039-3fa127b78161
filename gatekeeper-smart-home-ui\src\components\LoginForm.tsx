
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/hooks/useSupabaseAuth';
import { Shield, Lock, Mail, Loader2, Eye, EyeOff, AlertTriangle, Clock, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, type LoginFormData } from '@/lib/validation';
import { loginLimiter } from '@/middleware/security';
import { auditLogger } from '@/lib/auditLogger';

const LoginForm = () => {
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [deviceFingerprint, setDeviceFingerprint] = useState('');
  const { login } = useAuth();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setError
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  });

  const watchedPassword = watch('password');

  // Generování device fingerprint
  useEffect(() => {
    const generateFingerprint = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);
      }

      const components = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 0
      ];

      const fingerprint = components.join('|');
      let hash = 0;
      for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }

      setDeviceFingerprint(Math.abs(hash).toString(36));
    };

    generateFingerprint();
  }, []);

  // Kontrola rate limiting - pouze pokud už byly pokusy
  useEffect(() => {
    if (loginAttempts === 0 && !deviceFingerprint) return; // Nespouštět při prvním načtení

    const checkRateLimit = () => {
      const identifier = `login_${deviceFingerprint}`;

      // Zkontroluj pouze pokud už byly nějaké pokusy
      if (loginAttempts > 0) {
        const isAllowed = loginLimiter.isAllowed(identifier);

        if (!isAllowed) {
          setIsBlocked(true);
          const remainingTime = loginLimiter.getRemainingTime(identifier);
          setBlockTimeRemaining(Math.ceil(remainingTime / 1000));
        } else {
          setIsBlocked(false);
          setBlockTimeRemaining(0);
        }
      }
    };

    if (loginAttempts > 0) {
      checkRateLimit();
      const interval = setInterval(checkRateLimit, 1000);
      return () => clearInterval(interval);
    }
  }, [deviceFingerprint, loginAttempts]);

  // Výpočet síly hesla
  const calculatePasswordStrength = (password: string): number => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 15;
    if (/[^a-zA-Z0-9]/.test(password)) strength += 10;
    return Math.min(strength, 100);
  };

  const passwordStrength = calculatePasswordStrength(watchedPassword || '');

  const getPasswordStrengthColor = (strength: number) => {
    if (strength < 30) return 'bg-red-500';
    if (strength < 60) return 'bg-yellow-500';
    if (strength < 80) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = (strength: number) => {
    if (strength < 30) return 'Slabé';
    if (strength < 60) return 'Střední';
    if (strength < 80) return 'Silné';
    return 'Velmi silné';
  };

  const onSubmit = async (data: LoginFormData) => {
    const identifier = `login_${deviceFingerprint}`;

    // Kontrola rate limiting - pouze pokud už byly pokusy
    if (loginAttempts > 0 && !loginLimiter.isAllowed(identifier)) {
      setIsBlocked(true);
      await auditLogger.logSecurityEvent('RATE_LIMITED', {
        email: data.email,
        deviceFingerprint,
        attempts: loginAttempts + 1
      }, 'medium');

      toast({
        title: "Příliš mnoho pokusů",
        description: `Zkuste to znovu za ${Math.ceil(loginLimiter.getRemainingTime(identifier) / 1000)} sekund`,
        variant: "destructive",
      });
      return;
    }

    try {
      // Audit log před pokusem o přihlášení
      await auditLogger.logSecurityEvent('LOGIN_ATTEMPT', {
        email: data.email,
        deviceFingerprint,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }, 'low');

      await login(data.email, data.password);

      // Úspěšné přihlášení
      await auditLogger.logLogin(true, undefined, {
        email: data.email,
        deviceFingerprint,
        loginMethod: 'password'
      });

      loginLimiter.reset(identifier);
      setLoginAttempts(0);

      toast({
        title: "Přihlášení úspěšné",
        description: "Vítejte v IoT Smart Control System",
      });
    } catch (error) {
      // Neúspěšné přihlášení
      const newAttempts = loginAttempts + 1;
      setLoginAttempts(newAttempts);

      await auditLogger.logLogin(false, undefined, {
        email: data.email,
        deviceFingerprint,
        attempts: newAttempts,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Kontrola na brute force
      if (newAttempts >= 3) {
        await auditLogger.logSecurityEvent('MULTIPLE_FAILED_LOGINS', {
          email: data.email,
          deviceFingerprint,
          attempts: newAttempts
        }, 'high');
      }

      setError('root', {
        message: error instanceof Error ? error.message : "Neplatné přihlašovací údaje"
      });

      toast({
        title: "Chyba přihlášení",
        description: error instanceof Error ? error.message : "Neplatné přihlašovací údaje",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <div 
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}
      ></div>
      
      <Card className="w-full max-w-md backdrop-blur-lg bg-white/10 border-purple-500/20 shadow-2xl">
        <CardHeader className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-white">
            IoT Smart Control
          </CardTitle>
          <p className="text-purple-200">Přihlaste se pro pokračování</p>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Rate limiting upozornění */}
          {isBlocked && (
            <Alert variant="destructive">
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Příliš mnoho neúspěšných pokusů. Zkuste to znovu za {blockTimeRemaining} sekund.
              </AlertDescription>
            </Alert>
          )}

          {/* Bezpečnostní informace */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2 text-purple-300">
              <Shield className="h-4 w-4" />
              <span>Zabezpečené přihlášení</span>
            </div>
            <Badge variant="outline" className="text-purple-300 border-purple-500">
              Pokusů: {loginAttempts}/5
            </Badge>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Email pole */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-purple-200">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  className={`pl-10 bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300 ${
                    errors.email ? 'border-red-500' : ''
                  }`}
                  placeholder="<EMAIL>"
                  disabled={isBlocked}
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-400">{errors.email.message}</p>
              )}
            </div>

            {/* Heslo pole */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-purple-200">
                Heslo
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-purple-400" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  {...register('password')}
                  className={`pl-10 pr-10 bg-white/10 border-purple-500/30 text-white placeholder:text-purple-300 ${
                    errors.password ? 'border-red-500' : ''
                  }`}
                  placeholder="Vaše heslo"
                  disabled={isBlocked}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-purple-400 hover:text-purple-300"
                  disabled={isBlocked}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-400">{errors.password.message}</p>
              )}

              {/* Indikátor síly hesla */}
              {watchedPassword && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs text-purple-300">
                    <span>Síla hesla:</span>
                    <span>{getPasswordStrengthText(passwordStrength)}</span>
                  </div>
                  <Progress
                    value={passwordStrength}
                    className="h-2"
                  />
                </div>
              )}
            </div>

            {/* Chybové hlášení */}
            {errors.root && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {errors.root.message}
                </AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              disabled={isSubmitting || isBlocked}
              className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white font-semibold py-3 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Přihlašování...
                </>
              ) : isBlocked ? (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  Zablokováno ({blockTimeRemaining}s)
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Přihlásit se
                </>
              )}
            </Button>
          </form>

          {/* Bezpečnostní informace */}
          <div className="space-y-2 text-center text-sm text-purple-300">
            <p>Potřebujete účet? Požádejte o pozvánku administrátora.</p>
            <div className="flex items-center justify-center gap-2 text-xs">
              <Shield className="h-3 w-3" />
              <span>Chráněno 2FA a pokročilým monitoringem</span>
            </div>
          </div>

          {/* Device fingerprint info (pouze pro debug) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="text-xs text-purple-400 text-center">
              Device ID: {deviceFingerprint.slice(0, 8)}...
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginForm;
