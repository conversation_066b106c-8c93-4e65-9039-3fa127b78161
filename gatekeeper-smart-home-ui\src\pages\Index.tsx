import { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import LoginForm from '@/components/LoginForm';
import Dashboard from '@/components/Dashboard';
import { useAuth } from '@/hooks/useSupabaseAuth';

const Index = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400"></div>
      </div>
    );
  }

  if (!user) {
    return <LoginForm />;
  }

  return <Dashboard />;
};

export default Index;
