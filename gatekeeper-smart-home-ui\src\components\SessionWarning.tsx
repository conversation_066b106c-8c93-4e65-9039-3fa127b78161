import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Clock, 
  Shield, 
  LogOut, 
  RefreshCw,
  AlertTriangle,
  Timer
} from 'lucide-react';

interface SessionWarningProps {
  isOpen: boolean;
  timeUntilExpiry: number | null; // v milisekundách
  onExtendSession: () => Promise<boolean>;
  onSignOut: () => void;
  onClose: () => void;
}

const SessionWarning: React.FC<SessionWarningProps> = ({
  isOpen,
  timeUntilExpiry,
  onExtendSession,
  onSignOut,
  onClose
}) => {
  const [isExtending, setIsExtending] = useState(false);
  const [countdown, setCountdown] = useState<number>(0);
  const [autoLogoutIn, setAutoLogoutIn] = useState<number>(0);

  // Formátování času
  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Aktualizace odpočtu
  useEffect(() => {
    if (!isOpen || !timeUntilExpiry) return;

    setCountdown(timeUntilExpiry);
    
    const interval = setInterval(() => {
      setCountdown(prev => {
        const newValue = prev - 1000;
        
        // Auto logout za 30 sekund
        if (newValue <= 30000 && newValue > 0) {
          setAutoLogoutIn(Math.floor(newValue / 1000));
        }
        
        // Auto logout
        if (newValue <= 0) {
          onSignOut();
          return 0;
        }
        
        return newValue;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isOpen, timeUntilExpiry, onSignOut]);

  // Prodloužení session
  const handleExtendSession = async () => {
    setIsExtending(true);
    try {
      const success = await onExtendSession();
      if (success) {
        onClose();
      } else {
        // Pokud se nepodařilo prodloužit, vyloguj
        onSignOut();
      }
    } catch (error) {
      console.error('Chyba při prodlužování session:', error);
      onSignOut();
    } finally {
      setIsExtending(false);
    }
  };

  // Výpočet progress hodnoty
  const getProgressValue = (): number => {
    if (!timeUntilExpiry || countdown <= 0) return 0;
    return (countdown / timeUntilExpiry) * 100;
  };

  // Barva progress baru podle zbývajícího času
  const getProgressColor = (): string => {
    const progress = getProgressValue();
    if (progress > 50) return 'bg-green-500';
    if (progress > 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Urgentnost zprávy
  const getUrgencyLevel = (): 'low' | 'medium' | 'high' => {
    if (countdown <= 30000) return 'high'; // 30 sekund
    if (countdown <= 120000) return 'medium'; // 2 minuty
    return 'low';
  };

  const urgency = getUrgencyLevel();

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md" onPointerDownOutside={(e) => e.preventDefault()}>
        <DialogHeader>
          <div className="flex items-center gap-2">
            {urgency === 'high' ? (
              <AlertTriangle className="h-6 w-6 text-red-500 animate-pulse" />
            ) : urgency === 'medium' ? (
              <Timer className="h-6 w-6 text-yellow-500" />
            ) : (
              <Clock className="h-6 w-6 text-blue-500" />
            )}
            <DialogTitle>
              {urgency === 'high' ? 'Kritické upozornění!' : 'Upozornění na session'}
            </DialogTitle>
          </div>
          <DialogDescription>
            {urgency === 'high' 
              ? 'Vaše session vyprší velmi brzy!'
              : 'Vaše session brzy vyprší kvůli neaktivitě.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Progress bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Zbývající čas:</span>
              <span className={`font-mono font-bold ${
                urgency === 'high' ? 'text-red-500' : 
                urgency === 'medium' ? 'text-yellow-500' : 
                'text-blue-500'
              }`}>
                {formatTime(countdown)}
              </span>
            </div>
            <Progress 
              value={getProgressValue()} 
              className="h-3"
            />
          </div>

          {/* Auto logout varování */}
          {autoLogoutIn > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Automatické odhlášení za {autoLogoutIn} sekund!
              </AlertDescription>
            </Alert>
          )}

          {/* Bezpečnostní informace */}
          <div className="bg-muted p-3 rounded-lg space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Shield className="h-4 w-4" />
              Bezpečnostní informace
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <p>• Session vyprší automaticky z bezpečnostních důvodů</p>
              <p>• Vaše data zůstanou v bezpečí</p>
              <p>• Můžete se znovu přihlásit kdykoli</p>
            </div>
          </div>

          {/* Akce podle urgentnosti */}
          {urgency === 'high' ? (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Okamžitá akce vyžadována!</strong><br />
                Klikněte na "Prodloužit session" nebo budete automaticky odhlášeni.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Pokud chcete pokračovat v práci, prodloužte si session.
                Jinak budete automaticky odhlášeni z bezpečnostních důvodů.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onSignOut}
            className="w-full sm:w-auto"
            disabled={isExtending}
          >
            <LogOut className="mr-2 h-4 w-4" />
            Odhlásit se
          </Button>
          <Button
            onClick={handleExtendSession}
            disabled={isExtending}
            className={`w-full sm:w-auto ${
              urgency === 'high' ? 'bg-red-600 hover:bg-red-700' :
              urgency === 'medium' ? 'bg-yellow-600 hover:bg-yellow-700' :
              'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isExtending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Prodlužování...
              </>
            ) : (
              <>
                <Shield className="mr-2 h-4 w-4" />
                Prodloužit session
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SessionWarning;
