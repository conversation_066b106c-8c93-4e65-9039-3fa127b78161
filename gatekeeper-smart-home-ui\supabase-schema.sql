-- Supabase datab<PERSON><PERSON>vé schéma pro IoT Smart Home autentifikaci
-- Spusťte tyto dotazy v Supabase SQL editoru

-- Tabulka profilů uživatelů
CREATE TABLE profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    permissions TEXT[] DEFAULT ARRAY['user'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- <PERSON><PERSON><PERSON>
CREATE TABLE invites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token TEXT UNIQUE NOT NULL,
    permissions TEXT[] NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    used_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE
);

-- Tabulka logů přihlášení
CREATE TABLE login_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL, -- může být UUID nebo 'unknown' pro neúspěšné pokusy
    ip_address TEXT,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- RLS (Row Level Security) politiky

-- Povolit všem čtení vlastního profilu
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admini mohou číst všechny profily
CREATE POLICY "Admins can read all profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'admin' = ANY(permissions)
        )
    );

-- Admini mohou vytvářet a upravovat profily
CREATE POLICY "Admins can insert profiles" ON profiles
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'admin' = ANY(permissions)
        )
    );

CREATE POLICY "Admins can update all profiles" ON profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'admin' = ANY(permissions)
        )
    );

-- Pozvánky - pouze admini
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage invites" ON invites
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'admin' = ANY(permissions)
        )
    );

-- Login logy - pouze admini mohou číst
ALTER TABLE login_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can read login logs" ON login_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() 
            AND 'admin' = ANY(permissions)
        )
    );

-- Povolit všem vkládání login logů (pro tracking)
CREATE POLICY "Anyone can insert login logs" ON login_logs
    FOR INSERT WITH CHECK (true);

-- Funkce pro automatické vytvoření profilu při registraci
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    -- Kontrola, zda profil již neexistuje
    IF EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
        RETURN NEW;
    END IF;
    
    -- Vytvoření základního profilu s minimálními oprávněními
    INSERT INTO public.profiles (id, username, email, permissions)
    VALUES (NEW.id, NEW.email, NEW.email, ARRAY['user']);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pro automatické vytvoření profilu
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Vytvoření admin uživatele (upravte email a heslo podle potřeby)
-- Tento kód spusťte po vytvoření tabulek a nastavení RLS

-- 1. Vytvořte admin uživatele v Auth
-- 2. Poté spusťte tento dotaz s jeho UUID:

/*
-- Najděte UUID admin uživatele z auth.users tabulky a vložte správný profil:
INSERT INTO profiles (id, username, email, permissions, is_active)
VALUES (
    'UUID_ADMIN_UZIVATELE', -- nahraďte skutečným UUID
    'admin',
    '<EMAIL>',
    ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    true
)
ON CONFLICT (id) DO UPDATE SET
    permissions = ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    is_active = true;
*/

-- Indexy pro výkon
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_profiles_permissions ON profiles USING GIN(permissions);
CREATE INDEX idx_invites_token ON invites(token);
CREATE INDEX idx_invites_expires_at ON invites(expires_at);
CREATE INDEX idx_login_logs_timestamp ON login_logs(timestamp);
CREATE INDEX idx_login_logs_user_id ON login_logs(user_id);
