-- POUZ<PERSON> POLICIES - BEZ TRIGGERŮ
-- Zkopírujte a vložte do Supabase SQL Editor

-- 1. VYMAZÁNÍ VŠECH STARÝCH POLICIES
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can delete profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all security logs" ON security_logs;
DROP POLICY IF EXISTS "Ad<PERSON> can view all user security" ON user_security;
DROP POLICY IF EXISTS "Ad<PERSON> can update all user security" ON user_security;
DROP POLICY IF EXISTS "Ad<PERSON> can manage all invites" ON invites;
DROP POLICY IF EXISTS "Ad<PERSON> can view all sessions" ON user_sessions;
DROP POLICY IF EXISTS "Ad<PERSON> can update all sessions" ON user_sessions;

-- 2. VYMAZÁNÍ STARÝCH FUNKCÍ (BEZ TRIGGERU)
-- DROP FUNCTION IF EXISTS is_admin(); -- NEMAZAT! Funkce už existuje a funguje!

-- 3. BEZPEČNOSTNÍ FUNKCE PRO ADMIN KONTROLU
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM profiles
    WHERE id = auth.uid()
    AND (
      'admin' = ANY(permissions)
      OR email = '<EMAIL>'
    )
  );
$$;

-- 4. ZÁKLADNÍ POLICIES PRO PROFILES
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all profiles" ON profiles
  FOR UPDATE USING (is_admin());

CREATE POLICY "Allow profile creation during signup" ON profiles
  FOR INSERT WITH CHECK (
    auth.uid() = id OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid()
      AND auth.users.email = '<EMAIL>'
    )
  );

-- 5. POLICIES PRO OSTATNÍ TABULKY
CREATE POLICY "Admins can view all security logs" ON security_logs
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can view all user security" ON user_security
  FOR SELECT USING (is_admin());

CREATE POLICY "Admins can manage all invites" ON invites
  FOR ALL USING (is_admin());

CREATE POLICY "Admins can view all sessions" ON user_sessions
  FOR SELECT USING (is_admin());

-- 6. MANUÁLNÍ VYTVOŘENÍ ADMIN PROFILU (pokud neexistuje)
INSERT INTO profiles (id, email, username, permissions)
SELECT 
  auth.uid(),
  '<EMAIL>',
  'admin',
  ARRAY['admin', 'user', 'gate_control', 'camera_view', 'device_control', 'user_management']
WHERE NOT EXISTS (
  SELECT 1 FROM profiles WHERE email = '<EMAIL>'
);

-- 7. TEST
SELECT 
  'Policies nastaveny úspěšně!' as status,
  is_admin() as current_user_is_admin,
  COUNT(*) as profile_count
FROM profiles;
