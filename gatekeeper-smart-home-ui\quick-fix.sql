-- RYCHLÁ OPRAVA pro nekonečnou rekurzi v RLS policies
-- Spusťte tento kód v Supabase SQL editoru

-- 1. Nejdříve odstraňte problematické policies
DROP POLICY IF EXISTS "Admins can read all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can manage invites" ON invites;
DROP POLICY IF EXISTS "Ad<PERSON> can read login logs" ON login_logs;

-- 2. Vytvořte funkci pro kontrolu admin oprávnění
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id 
        AND 'admin' = ANY(permissions)
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Vytvořte nové policies bez rekurze
CREATE POLICY "Ad<PERSON> can read all profiles" ON profiles
    FOR SELECT USING (public.is_admin());

CREATE POLICY "Admins can insert profiles" ON profiles
    FOR INSERT WITH CHECK (public.is_admin());

CREATE POLICY "Admins can update all profiles" ON profiles
    FOR UPDATE USING (public.is_admin());

CREATE POLICY "Admins can manage invites" ON invites
    FOR ALL USING (public.is_admin());

CREATE POLICY "Admins can read login logs" ON login_logs
    FOR SELECT USING (public.is_admin());

-- 4. Nastavte admin oprávnění pro vašeho uživatele
INSERT INTO profiles (id, username, email, permissions, is_active)
VALUES (
    'ee40b3bf-7018-4b71-962e-dee2cbdd13eb', -- <NAME_EMAIL>
    'ivan_admin',
    '<EMAIL>',
    ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    true
)
ON CONFLICT (id) DO UPDATE SET
    permissions = ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    is_active = true,
    username = 'ivan_admin';
