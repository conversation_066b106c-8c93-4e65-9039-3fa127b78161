# 🔒 BEZPEČNOSTN<PERSON> DOKUMENTACE
## IoT Smart Control System

### 📋 PŘEHLED IMPLEMENTOVANÝCH BEZPEČNOSTNÍCH OPATŘENÍ

#### 🛡️ 1. AUTENTIFIKACE A AUTORIZACE

**✅ Implementováno:**
- **Supabase Auth** s JWT tokeny
- **Row Level Security (RLS)** v databázi
- **Granulární oprávnění** (admin, user, guest)
- **Session management** s automatickým refresh
- **Dvoufaktorové ověření (2FA)** s TOTP
- **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kódy** pro 2FA recovery

**🔧 Konfigurace:**
```sql
-- Funkce pro kontrolu admin oprávnění (SECURITY DEFINER)
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id 
        AND 'admin' = ANY(permissions)
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### 🚫 2. RATE LIMITING A BRUTE FORCE OCHRANA

**✅ Implementováno:**
- **Frontend rate limiting** pro přihlášení (5 pokusů / 15 min)
- **API rate limiting** (100 požadavků / min)
- **Admin operace limiting** (10 požadavků / 5 min)
- **Progresivní blokování** účtů
- **IP-based tracking** podezřelé aktivity

**📊 Limity:**
```typescript
// Rate limiting konfigurace
const rateLimiters = {
  auth: 5 pokusů / 15 minut,
  api: 100 požadavků / minutu,
  admin: 10 požadavků / 5 minut,
  general: 100 požadavků / 15 minut
}
```

#### 🔍 3. VALIDACE A SANITIZACE VSTUPŮ

**✅ Implementováno:**
- **Zod schémata** pro všechny formuláře
- **XSS ochrana** - sanitizace HTML tagů
- **SQL injection ochrana** - parametrizované dotazy
- **CSRF ochrana** - SameSite cookies
- **Input length limiting** - max délky polí
- **Regex validace** pro specifické formáty

**🛡️ Validační pravidla:**
```typescript
// Příklad bezpečné validace hesla
export const passwordSchema = z
  .string()
  .min(8, 'Alespoň 8 znaků')
  .max(128, 'Max 128 znaků')
  .refine(password => /[a-z]/.test(password), 'Malé písmeno')
  .refine(password => /[A-Z]/.test(password), 'Velké písmeno')
  .refine(password => /[0-9]/.test(password), 'Číslo')
  .refine(password => /[^a-zA-Z0-9]/.test(password), 'Speciální znak')
```

#### 🌐 4. BEZPEČNOSTNÍ HLAVIČKY A CORS

**✅ Implementováno:**
- **Content Security Policy (CSP)**
- **X-Frame-Options: DENY**
- **X-Content-Type-Options: nosniff**
- **X-XSS-Protection: 1; mode=block**
- **Strict-Transport-Security (HSTS)**
- **Referrer-Policy: strict-origin-when-cross-origin**
- **Permissions-Policy** - omezení API přístupu

**🔧 CSP konfigurace:**
```
default-src 'self';
script-src 'self';
style-src 'self' 'unsafe-inline';
connect-src 'self' https://veqmmmwrxeigeagqbqor.supabase.co;
img-src 'self' data: https:;
object-src 'none';
base-uri 'self';
form-action 'self';
frame-ancestors 'none';
```

#### 📊 5. AUDIT LOGGING A MONITORING

**✅ Implementováno:**
- **Security logs** - všechny bezpečnostní události
- **Login logs** - úspěšné/neúspěšné přihlášení
- **Suspicious activity detection** - neobvyklé vzory
- **Real-time monitoring** - dashboard pro adminy
- **IP tracking** - sledování zdrojových adres
- **User agent analysis** - detekce botů

**📋 Logované události:**
```typescript
const securityEvents = [
  'LOGIN_ATTEMPT',
  'LOGIN_SUCCESS', 
  'LOGIN_FAILED',
  'RATE_LIMITED',
  'SUSPICIOUS_ACTIVITY',
  'INVALID_TOKEN',
  '2FA_ENABLED',
  '2FA_DISABLED',
  'PASSWORD_CHANGED',
  'PERMISSION_CHANGED'
];
```

#### 🔐 6. SESSION MANAGEMENT A 2FA

**✅ Implementováno:**
- **Secure session handling** - HttpOnly cookies
- **Session timeout** - automatická expiry
- **Token refresh** - před expirací
- **2FA s TOTP** - Google Authenticator kompatibilní
- **Backup codes** - 10 jednorázových kódů
- **Device tracking** - známá zařízení

**⚙️ 2FA konfigurace:**
```typescript
// TOTP nastavení
const totpConfig = {
  window: 30, // 30 sekund
  digits: 6,  // 6-místný kód
  algorithm: 'SHA1',
  tolerance: 1 // ±1 časové okno
};
```

### 🚨 KRITICKÉ BEZPEČNOSTNÍ AKCE

#### ⚡ 1. OKAMŽITÁ OPRAVA - RLS REKURZE

**🔴 KRITICKÉ:** Spusťte tento SQL kód v Supabase:

```sql
-- RYCHLÁ OPRAVA pro nekonečnou rekurzi v RLS policies
DROP POLICY IF EXISTS "Admins can read all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can insert profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;

-- Vytvořte bezpečnou funkci
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id 
        AND 'admin' = ANY(permissions)
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Nové bezpečné policies
CREATE POLICY "Admins can read all profiles" ON profiles
    FOR SELECT USING (public.is_admin());

-- Nastavte admin oprávnění pro váš účet
UPDATE profiles 
SET permissions = ARRAY['admin', 'user'], is_active = true 
WHERE email = '<EMAIL>';
```

#### 📦 2. INSTALACE ZÁVISLOSTÍ

```bash
# Nainstalujte bezpečnostní závislosti
npm install helmet express-rate-limit cors express-validator bcryptjs jsonwebtoken speakeasy qrcode

# Dev závislosti
npm install -D @types/bcryptjs @types/jsonwebtoken @types/cors @types/qrcode
```

#### 🔧 3. KONFIGURACE PROSTŘEDÍ

```env
# .env.local
VITE_SUPABASE_URL=https://veqmmmwrxeigeagqbqor.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
API_KEY=brana-mqtt-secure-2025-v2
NODE_ENV=production
```

### 📈 BEZPEČNOSTNÍ METRIKY

#### 🎯 Cílové hodnoty:
- **Úspěšnost přihlášení:** >95%
- **Falešné poplachy:** <5%
- **Doba odezvy:** <2s
- **Dostupnost:** 99.9%

#### 📊 Monitorované metriky:
- Počet neúspěšných přihlášení za hodinu
- Podezřelé IP adresy
- Rychlost požadavků na uživatele
- Chybovost API endpointů
- Využití 2FA (% uživatelů)

### 🔄 PRAVIDELNÁ ÚDRŽBA

#### 📅 Denně:
- [ ] Kontrola security logů
- [ ] Monitoring podezřelé aktivity
- [ ] Backup databáze

#### 📅 Týdně:
- [ ] Analýza login vzorů
- [ ] Update bezpečnostních pravidel
- [ ] Kontrola rate limiting efektivity

#### 📅 Měsíčně:
- [ ] Security audit
- [ ] Penetrační testování
- [ ] Update závislostí
- [ ] Revize oprávnění uživatelů

### 🆘 INCIDENT RESPONSE

#### 🚨 Při podezřelé aktivitě:
1. **Okamžitě** zablokovat podezřelé IP
2. **Informovat** administrátory
3. **Analyzovat** logy
4. **Dokumentovat** incident
5. **Implementovat** dodatečná opatření

#### 📞 Kontakty:
- **Admin:** <EMAIL>
- **Emergency:** Supabase dashboard
- **Logs:** Security Dashboard v aplikaci

### 🔗 UŽITEČNÉ ODKAZY

- [Supabase Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [CSP Generator](https://report-uri.com/home/<USER>
- [Security Headers Test](https://securityheaders.com/)

---

**⚠️ DŮLEŽITÉ:** Tato dokumentace obsahuje citlivé bezpečnostní informace. 
Sdílejte pouze s autorizovanými osobami.
