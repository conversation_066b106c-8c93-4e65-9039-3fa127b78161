import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase, type Tables } from '../lib/supabase';
import { User, Session } from '@supabase/supabase-js';
import {
  loginLimiter,
  validateInput,
  securityLogger,
  sessionSecurity
} from '../middleware/security';

interface UserProfile extends Tables<'profiles'> {}

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  signUp: (email: string, password: string, username: string, inviteToken?: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionTimeout, setSessionTimeout] = useState(false);

  useEffect(() => {
    // Debug: start loading
    console.log('[AuthProvider] useEffect: start, loading session...');
    // Nastavit timeout pro načtení session
    const timeout = setTimeout(() => {
      setSessionTimeout(true);
    }, 8000); // 8 sekund

    // Získat aktuální session při načtení
    const getSession = async () => {
      try {
        // Validace session security
        const isValidSession = await sessionSecurity.validateSession();
        if (!isValidSession) {
          setUser(null);
          setProfile(null);
          setLoading(false);
          clearTimeout(timeout);
          return;
        }

        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('[AuthProvider] getSession ERROR:', error);
        }
        console.log('[AuthProvider] getSession: session =', session);
        setUser(session?.user ?? null);
        if (session?.user) {
          await fetchUserProfile(session.user.id);
        }
      } catch (err) {
        console.error('[AuthProvider] getSession: CATCH ERROR', err);
      } finally {
        setLoading(false);
        clearTimeout(timeout);
      }
    };

    getSession();

    // Nastavit interval pro refresh tokenu
    const refreshInterval = setInterval(() => {
      sessionSecurity.refreshTokenIfNeeded();
    }, 5 * 60 * 1000); // každých 5 minut

    // Poslouchat změny autentifikace
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('[AuthProvider] onAuthStateChange:', event, session);
        setUser(session?.user ?? null);
        if (session?.user) {
          await fetchUserProfile(session.user.id);
        } else {
          setProfile(null);
        }
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
      clearTimeout(timeout);
      clearInterval(refreshInterval);
    };
  }, []);

  const fetchUserProfile = async (userId: string) => {
    try {
      console.log('[AuthProvider] fetchUserProfile: loading for', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      console.log('[DEBUG] fetchUserProfile result:', { data, error });

      if (error) {
        console.error('[AuthProvider] Chyba při načítání profilu:', error);
        setProfile(null);
        setLoading(false);
        return;
      }
      console.log('[AuthProvider] fetchUserProfile: loaded', data);
      setProfile(data);
      setLoading(false);
    } catch (error) {
      console.error('[AuthProvider] Chyba při načítání profilu:', error);
    }
  };

  const login = async (email: string, password: string) => {
    // Validace vstupů
    const emailValidation = validateInput.email(email);
    if (!emailValidation.valid) {
      throw new Error(emailValidation.error);
    }

    const passwordValidation = validateInput.password(password);
    if (!passwordValidation.valid) {
      throw new Error(passwordValidation.error);
    }

    // Rate limiting
    const clientId = `login_${email}_${navigator.userAgent}`;
    if (!loginLimiter.isAllowed(clientId)) {
      const remainingTime = Math.ceil(loginLimiter.getRemainingTime(clientId) / 1000 / 60);
      await securityLogger.logLoginAttempt(email, false, 'RATE_LIMITED');
      throw new Error(`Příliš mnoho pokusů o přihlášení. Zkuste to znovu za ${remainingTime} minut.`);
    }

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        await securityLogger.logLoginAttempt(email, false, error.message);
        throw new Error(error.message);
      }

      // Úspěšné přihlášení - reset rate limiter
      loginLimiter.reset(clientId);

      // Aktualizovat last_login
      if (data.user) {
        await supabase
          .from('profiles')
          .update({ last_login: new Date().toISOString() })
          .eq('id', data.user.id);

        // Zalogovat přihlášení
        await supabase
          .from('login_logs')
          .insert({
            user_id: data.user.id,
            success: true,
            timestamp: new Date().toISOString()
          });

        await securityLogger.logLoginAttempt(email, true);
      }
    } catch (error) {
      // Zalogovat neúspěšné přihlášení
      await supabase
        .from('login_logs')
        .insert({
          user_id: 'unknown',
          success: false,
          timestamp: new Date().toISOString()
        });

      throw error;
    }
  };

  const logout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Chyba při odhlášení:', error);
    }
  };

  const signUp = async (email: string, password: string, username: string, inviteToken?: string) => {
    try {
      // Validace vstupů
      const emailValidation = validateInput.email(email);
      if (!emailValidation.valid) {
        throw new Error(emailValidation.error);
      }

      const passwordValidation = validateInput.password(password);
      if (!passwordValidation.valid) {
        throw new Error(passwordValidation.error);
      }

      const usernameValidation = validateInput.username(username);
      if (!usernameValidation.valid) {
        throw new Error(usernameValidation.error);
      }

      // Sanitizace vstupů
      const sanitizedEmail = validateInput.sanitizeString(email, 254);
      const sanitizedUsername = validateInput.sanitizeString(username, 50);

      // Pokud je inviteToken, ověřit pozvánku
      let permissions: string[] = ['user']; // výchozí oprávnění

      if (inviteToken) {
        const sanitizedToken = validateInput.sanitizeString(inviteToken, 255);
        const { data: invite, error: inviteError } = await supabase
          .from('invites')
          .select('*')
          .eq('token', sanitizedToken)
          .is('used_by', null)
          .gt('expires_at', new Date().toISOString())
          .single();

        if (inviteError || !invite) {
          await securityLogger.logSecurityEvent('INVALID_INVITE_TOKEN', {
            token: sanitizedToken,
            email: sanitizedEmail
          });
          throw new Error('Neplatná nebo vypršená pozvánka');
        }

        permissions = invite.permissions;
      }

      // Vytvořit uživatele
      const { data, error } = await supabase.auth.signUp({
        email: sanitizedEmail,
        password,
      });

      if (error) {
        await securityLogger.logSecurityEvent('SIGNUP_FAILED', {
          email: sanitizedEmail,
          error: error.message
        });
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('Nepodařilo se vytvořit uživatele');
      }

      // Vytvořit profil
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: data.user.id,
          username: sanitizedUsername,
          email: sanitizedEmail,
          permissions,
          is_active: true
        });

      if (profileError) {
        throw new Error('Nepodařilo se vytvořit profil: ' + profileError.message);
      }

      // Označit pozvánku jako použitou
      if (inviteToken) {
        await supabase
          .from('invites')
          .update({ 
            used_by: data.user.id,
            used_at: new Date().toISOString()
          })
          .eq('token', inviteToken);
      }

    } catch (error) {
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{ user, profile, login, logout, loading, signUp }}>
      {sessionTimeout && loading && (
        <div style={{position:'fixed',top:0,left:0,right:0,zIndex:9999,background:'#fff3',color:'#b00',padding:'1em',textAlign:'center'}}>
          Nelze načíst přihlášení. Zkuste se odhlásit, vymazat cookies nebo použijte anonymní okno.<br />
          <button
            style={{marginTop:'0.5em',padding:'0.5em 1em',background:'#b00',color:'#fff',border:'none',borderRadius:'4px',cursor:'pointer'}}
            onClick={async () => {
              try {
                // Odhlásit session na serveru
                const mod = await import('../lib/supabase');
                await mod.supabase.auth.signOut();
                console.log('[DEBUG] signOut hotovo');
              } catch (e) {
                console.error('[DEBUG] signOut error', e);
              }
              try {
                localStorage.clear();
                sessionStorage.clear();
                document.cookie.split(';').forEach(function(c) {
                  document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
                });
                console.log('[DEBUG] localStorage, sessionStorage, cookies vymazány');
              } catch (e) {
                console.error('[DEBUG] mazání storage/cookies error', e);
              }
              // Tvrdý reload stránky
              window.location.reload();
            }}
          >
            Vyčistit uložená data, odhlásit a obnovit stránku
          </button>
        </div>
      )}
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
