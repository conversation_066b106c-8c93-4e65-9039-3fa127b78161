-- Supabase datab<PERSON><PERSON>v<PERSON> schéma pro IoT Smart Home autentifikaci - OPRAVENÁ VERZE
-- Spusťte tyto dotazy v Supabase SQL editoru

-- Nejdříve odstraňte všechny existující policies
DROP POLICY IF EXISTS "Users can read own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can read all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage invites" ON invites;
DROP POLICY IF EXISTS "Ad<PERSON> can read login logs" ON login_logs;
DROP POLICY IF EXISTS "Anyone can insert login logs" ON login_logs;

-- <PERSON>bul<PERSON> profilů uživatelů
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT NOT NULL,
    permissions TEXT[] DEFAULT ARRAY['user'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Tabulka pozvánek
CREATE TABLE IF NOT EXISTS invites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    token TEXT UNIQUE NOT NULL,
    permissions TEXT[] NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    used_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE
);

-- Tabulka logů přihlášení
CREATE TABLE IF NOT EXISTS login_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL, -- může být UUID nebo 'unknown' pro neúspěšné pokusy
    ip_address TEXT,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Funkce pro kontrolu admin oprávnění - SECURITY DEFINER obchází RLS
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = user_id 
        AND 'admin' = ANY(permissions)
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS (Row Level Security) politiky - OPRAVENÉ BEZ REKURZE

-- Povolit všem čtení vlastního profilu
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Admini mohou číst všechny profily - používá funkci místo přímého dotazu
CREATE POLICY "Admins can read all profiles" ON profiles
    FOR SELECT USING (public.is_admin());

-- Admini mohou vytvářet profily
CREATE POLICY "Admins can insert profiles" ON profiles
    FOR INSERT WITH CHECK (public.is_admin());

-- Admini mohou upravovat všechny profily
CREATE POLICY "Admins can update all profiles" ON profiles
    FOR UPDATE USING (public.is_admin());

-- Pozvánky - pouze admini
ALTER TABLE invites ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can manage invites" ON invites
    FOR ALL USING (public.is_admin());

-- Login logy - pouze admini mohou číst
ALTER TABLE login_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can read login logs" ON login_logs
    FOR SELECT USING (public.is_admin());

-- Povolit všem vkládání login logů (pro tracking)
CREATE POLICY "Anyone can insert login logs" ON login_logs
    FOR INSERT WITH CHECK (true);

-- Funkce pro automatické vytvoření profilu při registraci
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    -- Kontrola, zda profil již neexistuje
    IF EXISTS (SELECT 1 FROM public.profiles WHERE id = NEW.id) THEN
        RETURN NEW;
    END IF;
    
    -- Vytvoření základního profilu s minimálními oprávněními
    INSERT INTO public.profiles (id, username, email, permissions)
    VALUES (NEW.id, NEW.email, NEW.email, ARRAY['user']);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger pro automatické vytvoření profilu
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Vytvoření admin už<NAME_EMAIL>
-- Najděte UUID uživatele a nastavte admin oprávnění
INSERT INTO profiles (id, username, email, permissions, is_active)
VALUES (
    'ee40b3bf-7018-4b71-962e-dee2cbdd13eb', -- <NAME_EMAIL>
    'ivan_admin',
    '<EMAIL>',
    ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    true
)
ON CONFLICT (id) DO UPDATE SET
    permissions = ARRAY['user', 'gate', 'camera', 'garage', 'admin'],
    is_active = true,
    username = 'ivan_admin';

-- Tabulka pro bezpečnostní logy
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    event TEXT NOT NULL,
    details JSONB,
    user_id UUID REFERENCES auth.users(id),
    ip_address TEXT,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Tabulka pro 2FA a bezpečnostní nastavení uživatelů
CREATE TABLE IF NOT EXISTS user_security (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) UNIQUE NOT NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret TEXT,
    backup_codes TEXT[],
    backup_codes_used TEXT[],
    last_2fa_used TIMESTAMP WITH TIME ZONE,
    failed_2fa_attempts INTEGER DEFAULT 0,
    last_failed_2fa TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
    login_attempts_count INTEGER DEFAULT 0,
    last_login_attempt TIMESTAMP WITH TIME ZONE,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- RLS pro security_logs - pouze admini mohou číst
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can read security logs" ON security_logs
    FOR SELECT USING (public.is_admin());

-- Povolit všem vkládání security logů (pro tracking)
CREATE POLICY "Anyone can insert security logs" ON security_logs
    FOR INSERT WITH CHECK (true);

-- RLS pro user_security - uživatelé mohou číst/upravovat pouze svá data
ALTER TABLE user_security ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own security settings" ON user_security
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update own security settings" ON user_security
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert own security settings" ON user_security
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Admini mohou číst všechna bezpečnostní nastavení
CREATE POLICY "Admins can read all security settings" ON user_security
    FOR SELECT USING (public.is_admin());

-- Indexy pro výkon
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_permissions ON profiles USING GIN(permissions);
CREATE INDEX IF NOT EXISTS idx_invites_token ON invites(token);
CREATE INDEX IF NOT EXISTS idx_invites_expires_at ON invites(expires_at);
CREATE INDEX IF NOT EXISTS idx_login_logs_timestamp ON login_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_timestamp ON security_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_security_logs_event ON security_logs(event);
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_security_user_id ON user_security(user_id);
CREATE INDEX IF NOT EXISTS idx_user_security_2fa_enabled ON user_security(two_factor_enabled);
CREATE INDEX IF NOT EXISTS idx_user_security_account_locked ON user_security(account_locked_until);
