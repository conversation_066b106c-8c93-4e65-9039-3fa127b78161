import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestBody {
  userId: string;
  token: string;
  secret: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { userId, token, secret }: RequestBody = await req.json()

    if (!userId || !token || !secret) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Ověření TOTP tokenu
    const isValid = verifyTOTP(token, secret)

    return new Response(
      JSON.stringify({
        valid: isValid,
        userId
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in verify-2fa function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function verifyTOTP(token: string, secret: string): boolean {
  const timeStep = 30 // 30 sekund
  const currentTime = Math.floor(Date.now() / 1000)
  const timeWindow = Math.floor(currentTime / timeStep)

  // Kontrola aktuálního času a ±1 časové okno pro toleranci
  for (let i = -1; i <= 1; i++) {
    const testTime = timeWindow + i
    const expectedToken = generateTOTP(secret, testTime)
    if (expectedToken === token) {
      return true
    }
  }

  return false
}

function generateTOTP(secret: string, timeWindow: number): string {
  // Převod base32 na bytes
  const secretBytes = base32ToBytes(secret)
  
  // Převod času na 8-byte big-endian
  const timeBytes = new ArrayBuffer(8)
  const timeView = new DataView(timeBytes)
  timeView.setUint32(4, timeWindow, false) // big-endian
  
  // HMAC-SHA1
  const hmac = hmacSha1(secretBytes, new Uint8Array(timeBytes))
  
  // Dynamic truncation
  const offset = hmac[hmac.length - 1] & 0x0f
  const code = ((hmac[offset] & 0x7f) << 24) |
               ((hmac[offset + 1] & 0xff) << 16) |
               ((hmac[offset + 2] & 0xff) << 8) |
               (hmac[offset + 3] & 0xff)
  
  // 6-digit code
  return (code % 1000000).toString().padStart(6, '0')
}

function base32ToBytes(base32: string): Uint8Array {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'
  const bytes: number[] = []
  let bits = 0
  let value = 0

  for (const char of base32.toUpperCase()) {
    const index = alphabet.indexOf(char)
    if (index === -1) continue

    value = (value << 5) | index
    bits += 5

    if (bits >= 8) {
      bytes.push((value >>> (bits - 8)) & 255)
      bits -= 8
    }
  }

  return new Uint8Array(bytes)
}

async function hmacSha1(key: Uint8Array, data: Uint8Array): Promise<Uint8Array> {
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    key,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  )
  
  const signature = await crypto.subtle.sign('HMAC', cryptoKey, data)
  return new Uint8Array(signature)
}

/* Deno configuration for this function */
// deno-lint-ignore no-unused-vars
const _config = {
  runtime: "edge",
  regions: ["fra1"]
}
