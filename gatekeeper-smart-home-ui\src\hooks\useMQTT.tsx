
import { useState, useEffect, useRef } from 'react';

// Use dynamic import to avoid module resolution conflicts
interface MQTTClient {
  connect: () => void;
  connected: boolean;
  subscribe: (topic: string) => void;
  publish: (topic: string, message: string) => void;
  on: (event: string, callback: Function) => void;
  end: () => void;
}

interface MQTTState {
  connected: boolean;
  gateStatus: 'open' | 'closed' | 'moving';
  statusMessage: string;  // Přidáme skutečnou MQTT zprávu
  sendCommand: (command: string) => void;
}

export const useMQTT = (): MQTTState => {
  const [connected, setConnected] = useState(false);
  const [gateStatus, setGateStatus] = useState<'open' | 'closed' | 'moving'>('closed');
  const [statusMessage, setStatusMessage] = useState<string>('Neznámý stav');
  const client = useRef<any>(null);

  useEffect(() => {
    const connectMqtt = async () => {
      try {
        console.log('Připojování k MQTT brokeru...');
        
        // Dynamic import to avoid conflicts
        const mqtt = await import('mqtt');
        const mqttConnect = mqtt.default || mqtt;
        
        // Použijeme stejnou konfiguraci jako v tvém fungujícím HTML
        client.current = mqttConnect.connect('ws://************:9001', {
          connectTimeout: 5000
        });
        
        client.current.on('connect', () => {
          console.log('MQTT WebSocket connected');
          setConnected(true);
          // Subscribe to status topic
          client.current.subscribe('IoT/Brana/Status');
          client.current.subscribe('Log/Brana/ID');
        });

        client.current.on('message', (topic: string, payload: Buffer) => {
          const message = payload.toString();
          console.log('MQTT message received:', { topic, message });          if (topic === 'IoT/Brana/Status') {
            // DEBUGGING - zobrazme všechny příchozí hodnoty
            console.log('🔔 MQTT Status přijat:', `"${message}"`, 'délka:', message.length);
            
            // Zpracování stavů - zobrazujeme pouze to, co skutečně přijde z MQTT
            let newStatus: 'open' | 'closed' | 'moving' = gateStatus; // zachováme současný stav jako default
              // Stavy pohybu - podle logu přesně tyto hodnoty
            if (message === 'Zavírá se...' || message === 'Zavírám bránu' || 
                message === 'Otevírá se...' || message === 'Otevírám bránu' ||
                message === 'Pohyb' || message === 'Moving' || 
                message.toLowerCase().includes('pohyb') ||
                message.toLowerCase().includes('moving') ||
                // Přidejte zde další hodnoty pokud se objeví:
                message === 'opening' || message === 'closing') {
              newStatus = 'moving';
            } 
            // Stavy zavřeno - podle logu
            else if (message === 'Brána zavřena' || message === 'Zavřeno' || 
                     message === 'Closed' || message === '0' ||
                     message === 'Není otevřena' || message === 'closed') {
              newStatus = 'closed';
            } 
            // Stavy otevřeno - čekáme na zjištění skutečné hodnoty
            else if (message === 'Brána otevřena' || message === 'Otevřeno' || 
                     message === 'Open' || message === '1' ||
                     message === 'Není zavřena' || message === 'open') {
              newStatus = 'open';
            }
            // Pro neznámé zprávy logujeme ale zachováváme stav
            else {
              console.log('⚠️ Neznámý status z MQTT:', `"${message}"` + ' - ponechávám současný stav:', gateStatus);
              return; // nepřepisujeme status
            }
              if (newStatus !== gateStatus) {
              console.log('✅ Status změněn z MQTT:', `"${message}"`, '->', newStatus);
              setGateStatus(newStatus);
              setStatusMessage(message); // Uložíme skutečnou MQTT zprávu
            } else {
              // I když se status nezměnil, aktualizujeme zprávu
              setStatusMessage(message);
            }
          }
        });

        client.current.on('close', () => {
          console.log('MQTT WebSocket disconnected');
          setConnected(false);
          // Attempt to reconnect after 3 seconds
          setTimeout(connectMqtt, 3000);
        });

        client.current.on('error', (error: any) => {
          console.error('MQTT WebSocket error:', error);
          setConnected(false);
        });
      } catch (error) {
        console.error('Failed to connect to MQTT WebSocket:', error);
        setConnected(false);
      }
    };

    connectMqtt();

    return () => {
      if (client.current) {
        client.current.end();
      }
    };
  }, []);
  const sendCommand = (command: string) => {
    if (client.current && client.current.connected) {
      // Publikujeme příkaz stejně jako v HTML
      client.current.publish('IoT/Brana/Ovladani', command);
      console.log('MQTT command sent:', command);
      
      // NEMĚNÍME status - čekáme na skutečnou MQTT zprávu
    } else {
      console.log('MQTT client not connected');
    }
  };
  return {
    connected,
    gateStatus,
    statusMessage,
    sendCommand
  };
};
