{"version": 3, "file": "superstruct.module.js", "sources": ["../src/superstruct.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { FieldError } from 'react-hook-form';\n\nimport { StructError, validate } from 'superstruct';\nimport { Resolver } from './types';\n\nconst parseErrorSchema = (error: StructError) =>\n  error.failures().reduce<Record<string, FieldError>>(\n    (previous, error) =>\n      (previous[error.path.join('.')] = {\n        message: error.message,\n        type: error.type,\n      }) && previous,\n    {},\n  );\n\nexport const superstructResolver: Resolver =\n  (schema, schemaOptions, resolverOptions = {}) =>\n  (values, _, options) => {\n    const result = validate(values, schema, schemaOptions);\n\n    if (result[0]) {\n      return {\n        values: {},\n        errors: toNestErrors(parseErrorSchema(result[0]), options),\n      };\n    }\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return {\n      values: resolverOptions.raw ? values : result[1],\n      errors: {},\n    };\n  };\n"], "names": ["superstructResolver", "schema", "schemaOptions", "resolverOptions", "values", "_", "options", "error", "result", "validate", "errors", "toNestErrors", "failures", "reduce", "previous", "path", "join", "message", "type", "shouldUseNativeValidation", "validateFieldsNatively", "raw"], "mappings": "sHAMA,IAUaA,EACX,SAACC,EAAQC,EAAeC,GACxB,YADwBA,IAAAA,IAAAA,EAAkB,CAAA,GAC1C,SAACC,EAAQC,EAAGC,GACV,IAbsBC,EAahBC,EAASC,EAASL,EAAQH,EAAQC,GAExC,OAAIM,EAAO,GACF,CACLJ,OAAQ,CAAE,EACVM,OAAQC,GAlBUJ,EAkBoBC,EAAO,GAjBnDD,EAAMK,WAAWC,OACf,SAACC,EAAUP,GACT,OAACO,EAASP,EAAMQ,KAAKC,KAAK,MAAQ,CAChCC,QAASV,EAAMU,QACfC,KAAMX,EAAMW,QACRJ,CAAQ,EAChB,CAAA,IAWsDR,KAItDA,EAAQa,2BAA6BC,EAAuB,CAAE,EAAEd,GAEzD,CACLF,OAAQD,EAAgBkB,IAAMjB,EAASI,EAAO,GAC9CE,OAAQ,CAAA,GAEZ,CAAC"}